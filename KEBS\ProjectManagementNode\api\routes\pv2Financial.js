//pv2Financial.js - Handles all the Financials of Projects
const express = require("express");
const router = express.Router();
const passport = require("passport");
module.exports = router;

//Services
const financialController = require("../controller/pv2FinancialController");

//=================================================================================================


router.post("/getProjectFinancialInfo",
    passport.authenticate("jwt"),
    financialController.getProjectFinancialInfo
); 


router.post("/insertCostingHeader",
    passport.authenticate("jwt"),
    financialController.insertCostingHeader
); 

router.post("/fetchProjectCurrency",
    passport.authenticate("jwt"),
    financialController.fetchProjectCurrency
); 


router.post("/getProjectCostingHeader",
    passport.authenticate("jwt"),
    financialController.getProjectCostingHeader
); 
router.post("/insertPosition",
    passport.authenticate("jwt"),
    financialController.insertPosition
); 
router.post("/createCostingSheetHeader",
    passport.authenticate("jwt"),
    financialController.createCostingSheetHeader
);
router.post("/getProjectCostingHeaderForid",
    passport.authenticate("jwt"),
    financialController.getProjectCostingHeaderForid
);
router.post("/getDirectCostDetails",
    passport.authenticate("jwt"),
    financialController.getDirectCostDetails
);
router.post("/SaveCostingSheet",
    passport.authenticate("jwt"),
    financialController.SaveCostingSheet
);
router.post("/getInDirectCostDetails",
    passport.authenticate("jwt"),
    financialController.getInDirectCostDetails
);
router.post("/getCostingSheet",
    passport.authenticate("jwt"),
    financialController.getCostingSheet
);
router.post("/getQuoteCostingData",
    passport.authenticate("jwt"),
    financialController.getQuoteCostingData
);
router.post("/getQuoteDetails",
    passport.authenticate("jwt"),
    financialController.getQuoteDetails
);
router.post("/getProjectCost",
    passport.authenticate("jwt"),
    financialController.getProjectCost
);
router.post("/releaseDCS",
    passport.authenticate("jwt"),
    financialController.releaseDCS
);
router.post("/deleteCostingSheet",
    passport.authenticate("jwt"),
    financialController.deleteCostingSheet
);
router.post("/deletePosition",
    passport.authenticate("jwt"),
    financialController.deletePosition
);
router.post("/getResourceName",
    passport.authenticate("jwt"),
    financialController.getResourceName
);

router.post("/getMilestoneTypeList",
    passport.authenticate("jwt"),
    financialController.getMilestoneTypeList
);
router.post("/revertMilestoneFromYTB",
    passport.authenticate("jwt"),
    financialController.revertMilestoneFromYTB
);

router.post("/getConversionRatesCommon",
    passport.authenticate("jwt"),
    financialController.getConversionRatesCommon
);
router.post("/fetchBillingAdviceData",
    passport.authenticate("jwt"),
    financialController.fetchBillingAdviceData
);
router.post("/getRPAProjectFTEDetails",
    passport.authenticate("jwt"),
    financialController.getRPAProjectFTEDetails
);
router.post("/getAllocatedHoursForEmployeeWithDates",
    passport.authenticate("jwt"),
    financialController.getAllocatedHoursForEmployeeWithDates
);
router.post("/getEndDateForEmployeeWithDuration",
    passport.authenticate("jwt"),
    financialController.getEndDateForEmployeeWithDuration
);

router.post("/getPositionCummulativeAlloctaedHours",
    passport.authenticate("jwt"),
    financialController.getPositionCummulativeAlloctaedHours
);
router.post("/getQuoteAndQuotePositionForResourceLoading",
    passport.authenticate("jwt"),
    financialController.getQuoteAndQuotePositionForResourceLoading
);
router.post("/getIsaData",
    passport.authenticate("jwt"),
    financialController.getIsaData
);
router.post("/getRateCardPositionForResourceLoading",
    passport.authenticate("jwt"),
    financialController.getRateCardPositionForResourceLoading
);
router.post("/saveResourceLoadingData",
    passport.authenticate("jwt"),
    financialController.saveResourceLoadingData
);
router.post("/getResourceLoadingData",
passport.authenticate("jwt"),
financialController.getResourceLoadingData
);
router.post("/getQuoteDetailsForResourceLoading",
passport.authenticate("jwt"),
financialController.getQuoteDetailsForResourceLoading
);
router.post("/getResourceLoadingHeaderVersion",
passport.authenticate("jwt"),
financialController.getResourceLoadingHeaderVersion
);

router.post("/loadResourceLoadingFromQuote",
    passport.authenticate("jwt"),
    financialController.loadResourceLoadingFromQuote
)



router.post("/insertBillingPlanData",
    passport.authenticate("jwt"),
    financialController.insertBillingPlanData
)


router.post("/importBillingPlanFromQuote",
    passport.authenticate("jwt"),
    financialController.importBillingPlanFromQuote
)



router.post("/getBillingPlanVersionHistory",
    passport.authenticate("jwt"),
    financialController.getBillingPlanVersionHistory
)

router.post("/getQuoteDetailsForBillingPlan",
    passport.authenticate("jwt"),
    financialController.getQuoteDetailsForBillingPlan
)


router.post("/getBillingPlanData",
    passport.authenticate("jwt"),
    financialController.getBillingPlanData
)
router.post("/getBillingAdviceVersionHistory",
    passport.authenticate("jwt"),
    financialController.getBillingAdviceVersionHistory
)
router.post("/getProjectFinancialValues",
    passport.authenticate("jwt"),
    financialController.getProjectFinancialValues
)


router.post("/getMilestoneMatrix",
    passport.authenticate("jwt"),
    financialController.getMilestoneMatrix
)

router.post("/getBillData",
    passport.authenticate("jwt"),
    financialController.getBillData
); 



router.post("/updateInvoiceDate",
    passport.authenticate("jwt"),
    financialController.updateInvoiceDate
); 


router.post("/validateYTBData",
    passport.authenticate("jwt"),
    financialController.validateYTBData
); 


router.post("/validateAccruedData",
    passport.authenticate("jwt"),
    financialController.validateAccruedData
); 

router.post("/getBillingAdviceBillingPlanData",
    passport.authenticate("jwt"),
    financialController.getBillingAdviceBillingPlanData
); 

router.post("/createMilestones",
    passport.authenticate("jwt"),
    financialController.createMilestone
);
router.post("/getPoMasterData",
    passport.authenticate("jwt"),
    financialController.getPoMasterData
); 

router.post("/deletePoMaster",
    passport.authenticate("jwt"),
    financialController.deletePoMaster
); 

router.post("/getProjectCurrency",
    passport.authenticate("jwt"),
    financialController.getProjectCurrency
); 
router.post("/insertPoMaster",
    passport.authenticate("jwt"),
    financialController.insertPoMaster
); 

router.post("/updatePoMaster",
    passport.authenticate("jwt"),
    financialController.updatePoMaster
); 
router.post("/checkPoNumberExists",
    passport.authenticate("jwt"),
    financialController.checkPoNumberExists
); 
router.post("/getPoMastercheckData",
    passport.authenticate("jwt"),
    financialController.getPoMastercheckData
); 
router.post("/checkmilestonestatus",
    passport.authenticate("jwt"),
    financialController.checkmilestonestatus
); 
router.post("/getBillingAdviceBillingPlanData",
    passport.authenticate("jwt"),
    financialController.getBillingAdviceBillingPlanData
); 

router.post("/getPositionRemainingLogData",
    passport.authenticate("jwt"),
    financialController.getPositionRemainingLogData
);

router.post("/getUpdatedBillingValue",
    passport.authenticate("jwt"),
    financialController.getUpdatedBillingValue
);


router.post("/checkChangingActualEffortsMilestone",
    passport.authenticate("jwt"),
    financialController.checkChangingActualEffortsMilestone
);

router.post("/moveMilestoneToReverseAccrued",
    passport.authenticate("jwt"),
    financialController.moveMilestoneToReverseAccrued
);


router.post("/createPartialInvoiceMilestone",
    passport.authenticate("jwt"),
    financialController.createPartialInvoiceMilestone
);


router.post("/createCreditNoteInvoiceMilestone",
    passport.authenticate("jwt"),
    financialController.createCreditNoteInvoiceMilestone
);

router.post("/triggerApprovalForHoursDeviation",
    passport.authenticate("jwt"),
    financialController.triggerApprovalForHoursDeviation
);

router.post("/getWorkflowDetailsBasedOnSource",
    passport.authenticate("jwt"),
    financialController.getWorkflowDetailsBasedOnSource
);

router.post("/updateProjectWorkflowRequest",
    passport.authenticate("jwt"),
    financialController.updateProjectWorkflowRequest
);

router.post("/triggerApprovalForValueDeviation",
    passport.authenticate("jwt"),
    financialController.triggerApprovalForValueDeviation
);

router.post("/getGeneralPlannedValue",
    passport.authenticate("jwt"),
    financialController.getGeneralPlannedValue
);


router.post("/getProjectPlannedValue",
    passport.authenticate("jwt"),
    financialController.getProjectPlannedValue
);


router.post("/insertFixedBillingPlanData",
    passport.authenticate("jwt"),
    financialController.insertFixedBillingPlanData
);


router.post("/getFixedBillingPlanVersionHistory",
    passport.authenticate("jwt"),
    financialController.getFixedBillingPlanVersionHistory
)

router.post("/mergeBillingAdviceDetails",
    passport.authenticate("jwt"),
    financialController.mergeBillingAdviceDetails
)

router.post("/getApproversDetails",
    passport.authenticate("jwt"),
    financialController.getApproversDetails
)

router.post("/saveAttachDependencies",
    passport.authenticate("jwt"),
    financialController.saveAttachDependencies
)

router.post("/resourceTypeConfigQuote",
    passport.authenticate("jwt"),
    financialController.resourceTypeConfigQuote
)

router.post("/triggerApprovalForCreditNoteCreation",
    passport.authenticate("jwt"),
    financialController.triggerApprovalForCreditNoteCreation
);


router.post("/checkOpportunityBlanketPO",
    passport.authenticate("jwt"),
    financialController.checkOpportunityBlanketPO
);


router.post("/getFixedBillingPlanQuoteDetails",
    passport.authenticate("jwt"),
    financialController.getFixedBillingPlanQuoteDetails
);



router.post("/insertFixedBillingPlanDeliverable",
    passport.authenticate("jwt"),
    financialController.insertFixedBillingPlanDeliverable
);

router.post("/getProjectWorkflowDetails",
    passport.authenticate("jwt"),
    financialController.getProjectWorkflowDetails
);

router.post("/getProjectWorkflowDetailsCount",
    passport.authenticate("jwt"),
    financialController.getProjectWorkflowDetailsCount
);

router.post("/getPeopleAllocationWorkflowDetailsCount",
    passport.authenticate("jwt"),
    financialController.getPeopleAllocationWorkflowDetailsCount
);

router.post("/getPeopleAllocationWorkflowDetails",
    passport.authenticate("jwt"),
    financialController.getPeopleAllocationWorkflowDetails
);

router.post("/updateInboxProjectWorkflowRequest",
    passport.authenticate("jwt"),
    financialController.updateInboxProjectWorkflowRequest
);

router.post("/updateInboxPeopleAllocationWorkflowRequest",
    passport.authenticate("jwt"),
    financialController.updateInboxPeopleAllocationWorkflowRequest
);

router.post("/revertMilestoneFromAccrual",
    passport.authenticate("jwt"),
    financialController.revertMilestoneFromAccrual
);

router.post("/moveMilestoneToReverseAccrued",
    passport.authenticate("jwt"),
    financialController.moveMilestoneToReverseAccrued
);


router.post("/checkPreviousMilestoneStatus",
    passport.authenticate("jwt"),
    financialController.checkPreviousMilestoneStatus
);
router.post("/createCreditNoteInvoiceMilestoneForBills",
    passport.authenticate("jwt"),
    financialController.createCreditNoteInvoiceMilestoneForBills
);
router.post("/checkActualEffortValue",
    passport.authenticate("jwt"),
    financialController.checkActualEffortsMilestone
);


router.post("/checkRevenueBilledEqual",
    passport.authenticate("jwt"),
    financialController.checkRevenueBilledEqual
);

router.post("/acquireForecastSubDivisionReport",
    passport.authenticate("jwt"),
    financialController.acquireForecastSubDivisionReport
)