const { json } = require("body-parser");
const { logger } = require("../../logger")
const pool = require("../../databaseCon").pool;
const rpool = require("../../databaseCon").rpool;
let mongo = require("../../mongo_conn_native").Connection;
const mysql = require("mysql");
const utilService = require("../utils/pv2UtilityService");
const moment = require("moment");
const _ = require("underscore");
const axios = require('axios');

/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * @description Get Project Fincancial Information
 * <AUTHOR>
 * @version 1.0
 * @returns 
 */
module.exports.getProjectFinancialInfo = async(project_id, item_id, date, db, user)=>{
    try{
        let result = await rpool(`SELECT item_value, planned_quote, planned_start_date, planned_end_date, item_name, item_status_id, gantt_id
        FROM ${db}.t_project_item WHERE project_id = ? AND id = ? `,[project_id, item_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"Item not found!"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while fetching Project Financial Information"})
    }
}

/**
 * 
 * @param {*} formatInsertionData 
 * @param {*} db 
 * @description Insert Costing Header Information
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertCostingHeader = async(formatInsertionData, db)=>{
    try{
        let result = await pool(`INSERT INTO ${db}.t_project_costing_header (project_id, project_item_id, name, 
            creation_type, status_id, planned_cost, planned_cost_value, gross_margin, gross_margin_value, 
            gross_margin_percentage, quote_id, currency, interval_type, direct_cost, direct_cost_value, 
            indirect_cost, indirect_cost_value, offsite_direct_cost, offsite_direct_cost_value, 
            onsite_direct_cost, onsite_direct_cost_value, created_by, created_on, updated_by, updated_on) VALUES (?)`,[formatInsertionData])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting costing sheet header details"})
    }
}


/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> Raam Baskar
 * @description Get Project Costing Header
 * @returns 
 */
module.exports.getProjectCostingHeader = async(project_id, item_id, date, db, user)=>{
    try{
        let result = await rpool(`SELECT id, name, creation_type, status_id, planned_cost, planned_cost_value, gross_margin_percentage, created_on, created_by,currency FROM ${db}.t_project_costing_header WHERE project_id =? AND project_item_id =? AND is_active = 1 ORDER BY id DESC`,[project_id, item_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No data found!", data:[]})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while fetching costing sheet Header list for the Project!", data:[]})
    }
}
/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description Insert Position
 * @returns 
 */
module.exports.insertPosition = async(project_id, item_id, data,costing_sheet_id,code, db, user)=>{
    try{
        if(data.parent==null){
            data.parent=0
        }
        let base_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.basePrice, code, moment().format("YYYY-MM-DD"), db) 
        data['base_price_value'] = JSON.stringify(base_price_value);
        let salary_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.salary, code, moment().format("YYYY-MM-DD"), db) 
        data['salary_price_value'] = JSON.stringify(salary_price_value);
        let result = await pool(`INSERT INTO ${db}.t_project_costing_direct_cost_items (costing_header_id,project_id,project_item_id,costing_type,costing_position,position,work_experience,work_location,base_price,base_price_value,salary,salary_value,entity_id,division_id,sub_division_id,billable,quote_id,parent,unique_id,quote_position) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,[costing_sheet_id,project_id, item_id,'Costing Position',data.position,data.position,data.experience,data.workLocation,data.basePrice,data.base_price_value,data.salary,data.salary_price_value,data.entity,data.division,data.subDivision,data.isBillable==false?0:1,data.quote_id,data.parent,data.unique_id,data.quote_position_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting position !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description costing sheet header creation
 * @returns 
 */
module.exports.createCostingSheetHeader = async(data,name, db, user)=>{
    try{
        let total_revenue_value = await utilService.covertToRespectiveCurrency(data.projectID,data.itemID, data.revenue, data.code, moment().format("YYYY-MM-DD"), db) 
        data['total_revenue_value'] = JSON.stringify( total_revenue_value);
        let result = await pool(`INSERT INTO ${db}.t_project_costing_header (project_id,project_item_id,name,creation_type,status_id,currency,interval_type,total_revenue,total_revenue_value,gross_margin_percentage,planned_cost,created_by) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`,[data.projectID,data.itemID,name.costing_sheet_name,data.creation_type,1,data.code,'month',data.revenue,data.total_revenue_value,data.gross_margin_percentage,data.planned_cost,user.aid])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting createCostingSheetHeader !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get parricular costing header
 * @returns 
 */
module.exports.getProjectCostingHeaderForid = async(project_id, item_id,costing_sheet_id, db, user)=>{
    try{
        let result = await rpool(`SELECT id, name, creation_type, status_id,planned_cost, planned_cost_value, gross_margin_percentage,total_revenue,currency,interval_type, created_on, created_by,quote_id FROM ${db}.t_project_costing_header WHERE project_id =? AND project_item_id =? AND id=? AND is_active = 1`,[project_id, item_id,costing_sheet_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting position !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get direct cost details for a costing sheet
 * @returns 
 */
module.exports.getDirectCostDetails = async(project_id, item_id,costing_sheet_id, db, user)=>{
    try{
        let result = await rpool(`SELECT tcdci.id,tcdci.costing_position AS costing_position_id,mr.name AS position_name,tcdci.work_experience,tcdci.work_location AS location,tcdci.base_price,tcdci.salary,tcdci.associate_id,tcdci.entity_id AS entity,tcdci.division_id AS division,tcdci.sub_division_id AS sub_division,tcdci.direct_cost AS directCost,tcdci.parent,tcdci.associate_id,tcdci.quote_id,tcdci.request_id,tcdci.unique_id,tcdci.quote_position
       FROM ${db}.t_project_costing_direct_cost_items tcdci
        INNER JOIN ${db}.m_role mr ON mr.id=tcdci.costing_position
        WHERE tcdci.project_id=? AND tcdci.project_item_id=? AND tcdci.costing_header_id=? AND tcdci.is_active=1`,[project_id, item_id,costing_sheet_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting direct item details !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get direct cost details for a costing sheet (month wise)
 * @returns 
 */
module.exports.getDirectCostDetailsMonthWiseHours = async(project_id, item_id,costing_sheet_id,id, db, user)=>{
    try{
        let result = await rpool(`SELECT month,year,week,hours AS value,calculated_direct_cost AS VALUE,billed_hours,month_key FROM ${db}.t_project_costing_direct_cost_items_details
        WHERE project_id=? AND project_item_id=? AND costing_header_id=? AND direct_costing_item_id=? AND is_active=1`,[project_id, item_id,costing_sheet_id,id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting direct item details !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description save direct cost
 * @returns 
 */
module.exports.SaveDirectCost = async(data,id,project_id,item_id,code, db, user)=>{
    try{
        let result = await pool(`UPDATE ${db}.t_project_costing_direct_cost_items SET is_active=0 WHERE costing_header_id=?`,[id])
        await pool(`UPDATE ${db}.t_project_costing_direct_cost_items_details SET is_active=0 WHERE costing_header_id=?`,[id])
        
        for(let item of data){
       let check= await this.savePosition(project_id,item_id,item,id,code,db, user)
       
       if(check['messType']=='S'){
        await this.savePositionMonthWiseData(project_id,item_id,item.data,id,check.data.insertId,item.directCost,code,db, user)
       }
        }
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while saving direct cost !", data:[]})
    }
}
/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description Insert Position
 * @returns 
 */
module.exports.savePosition = async(project_id, item_id, data,costing_sheet_id,code, db, user)=>{
    try{
        let base_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.base_price, code, moment().format("YYYY-MM-DD"), db) 
        data['base_price_value'] = JSON.stringify(base_price_value);
        let salary_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.salary, code, moment().format("YYYY-MM-DD"), db) 
        data['salary_price_value'] = JSON.stringify(salary_price_value);
        let result = await pool(`INSERT INTO ${db}.t_project_costing_direct_cost_items (costing_header_id,project_id,project_item_id,costing_type,costing_position,position,work_experience,work_location,base_price,base_price_value,salary,salary_value,entity_id,division_id,sub_division_id,direct_cost,billable,parent,quote_id,associate_id,request_id,unique_id,quote_position) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,[costing_sheet_id,project_id, item_id,'Costing Position',data.costing_position_id,data.costing_position_id,data.work_experience,data.location,data.base_price,data.base_price_value,data.salary,data.salary_price_value,data.entity,data.division,data.sub_division,data.directCost,data.billable==false?0:1,data.parent,data.quote_id,data.associate_id,data.request_id,data.unique_id,data.quote_position_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting position !", data:[]})
    }
}
    /**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description Insert Position
 * @returns 
 */
module.exports.savePositionMonthWiseData = async(project_id, item_id, data,costing_sheet_id,id,cost,code, db, user)=>{
    try{
         let result
         logger.info('cos-id')
         logger.info(id)
        for(let item of data){
        let calculated_direct_cost_value = await utilService.covertToRespectiveCurrency(project_id,item_id, cost, code, moment().format("YYYY-MM-DD"), db) 
        let calculated_direct_cost = JSON.stringify(calculated_direct_cost_value);
         result = await pool(`INSERT INTO ${db}.t_project_costing_direct_cost_items_details (costing_header_id,project_id,project_item_id,direct_costing_item_id,type_interval_name,year,month,week,hours,calculated_direct_cost,calculated_direct_cost_value,month_key) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`,[costing_sheet_id,project_id, item_id,id,'month',item.year,item.month,0,item.value,cost,calculated_direct_cost,item.month_key])
        }
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting position !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description save direct cost
 * @returns 
 */
module.exports.saveInDirectCost = async(data,id,project_id,item_id,code, db, user)=>{
    try{
        // logger.info(data)
        let check
        let result = await pool(`UPDATE ${db}.t_project_costing_in_direct_cost_items SET is_active=0 WHERE costing_header_id=?`,[id])
        for(let item of data){
            logger.info('test console')
            let per_quantity_cost = await utilService.covertToRespectiveCurrency(project_id,item_id, item.value, code, moment().format("YYYY-MM-DD"), db) 
            let per_quantity_cost_value = JSON.stringify(per_quantity_cost);
            let total_quantity_cost = await utilService.covertToRespectiveCurrency(project_id,item_id, item.total_cumulative_value, code, moment().format("YYYY-MM-DD"), db) 
            let total_quantity_cost_value = JSON.stringify(total_quantity_cost);
              check = await pool(`INSERT INTO ${db}.t_project_costing_in_direct_cost_items (costing_header_id,project_id,project_item_id,name,quantity,per_quantity_cost,per_quantity_cost_value,total_quantity_cost,total_quantity_cost_value) VALUES (?,?,?,?,?,?,?,?,?)`,[id,project_id, item_id,item.item_name,item.quantity,item.value,per_quantity_cost_value,item.total_cumulative_value,total_quantity_cost_value])
        }
            return Promise.resolve({messType:"S", data:  check})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while saving direct cost !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get In direct cost details for a costing sheet
 * @returns 
 */
module.exports.getInDirectCostDetails = async(project_id, item_id,costing_sheet_id, db, user)=>{
    try{
        let result = await rpool(`SELECT name AS item_name,per_quantity_cost AS value,quantity,total_quantity_cost AS total_cumulative_value FROM ${db}.t_project_costing_in_direct_cost_items WHERE project_id=? AND project_item_id=? AND costing_header_id=? AND is_active=1`,[project_id, item_id,costing_sheet_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting direct item details !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get costing sheet
 * @returns 
 */
module.exports.getCostingSheet = async(project_id, item_id, db, user)=>{
    try{
        let result = await rpool(`SELECT id,name FROM ${db}.t_project_costing_header WHERE project_id=? AND project_item_id=? AND is_active=1 ORDER BY id DESC`,[project_id, item_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting costing sheet !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get quote position
 * @returns 
 */
module.exports.getQuoteData = async(project_id, item_id, db, user)=>{
    try{
        let result = await rpool(`SELECT quote_id FROM ${db}.t_project_financial_details WHERE project_id=? AND project_item_id=? AND is_active=1`,[project_id, item_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting quote_position !", data:[]})
    }
}
/**
 * 
 * @description get quote position
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuotePosition = async(id,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT DISTINCT tqqp.quote_position_id AS position_id,ROUND(SUM(tqqpe.hours) * tqqp.resource_count, 2) AS original_balance,
            tqqp.rate_per_unit,tqqp.rate_currency_code,mqu.unit_name AS unit_name, mqu.unit_suffix, mqu.unit_value_in_hrs, tqqp.resource_type_id AS resource_type,
            (CASE WHEN tqqp.position_name IS NOT NULL THEN tqqp.position_name ELSE mr.name END) AS position_name, tqqm.milestone_name, tqqm.milestone_id, tqqm.start_date AS milestone_start_date,
            tqqm.end_date AS milestone_end_date, tqqp.unit_id, IF(tqqp.position_status IS NULL, 1, tqqp.position_status) AS position_status
            FROM ${db}.t_qb_quote_service tqqs
            INNER JOIN ${db}.t_qb_quote_position tqqp ON tqqp.quote_service_id=tqqs.quote_service_id AND tqqs.is_active=1
            INNER JOIN ${db}.m_role mr ON mr.id=tqqp.position
            INNER JOIN ${db}.m_qb_uom AS mqu ON mqu.id = tqqp.unit_id
            LEFT JOIN ${db}.t_qb_quote_milestone tqqm ON tqqm.milestone_id = tqqp.milestone_id
            LEFT JOIN ${db}.t_qb_quote_position_effort tqqpe ON tqqpe.quote_position_id = tqqp.quote_position_id AND tqqpe.quote_id = tqqs.quote_header_id AND tqqpe.is_active = 1
            WHERE tqqs.quote_header_id IN (?) AND tqqp.is_active=1 AND mr.is_active=1  
            GROUP BY tqqp.quote_position_id`,[id])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get quote position"}
    }
}
/**
 * 
 * @description get reosurce request position
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceRequest = async(item_id,quote_ids,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT trr.request_id, trj.position,trr.work_experience AS experience,trr.work_location AS workLocation,trj.entity,trj.division,trj.sub_division AS subDivision,trr.quote_id,trr.quote_position_id AS parent_uniq FROM ${db}.t_rm_request trr
        INNER JOIN ${db}.t_rm_job_detail trj ON trj.request_id=trr.request_id 
        WHERE trr.project_id=? AND trr.quote_id IN(?) AND trr.is_active=1 AND trj.is_active=1`,[item_id,quote_ids])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get resource request"}
    }
}
/**
 * 
 * @description update costing sheet
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.updateCostingSheet = async(project_id, item_id,costing_sheet_id,item,quote_id,db, user, authorization)=>{
    try{   

        let result=await pool(`UPDATE ${db}.t_project_costing_header SET creation_type=?,quote_id=? WHERE id=? AND project_id=? AND project_item_id=? AND is_active=1`,[item,JSON.stringify(quote_id), costing_sheet_id,project_id,item_id])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while update costing sheet"}
    }
}
/**
 * 
 * @description get quote data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuoteDetails = async(id,db, user, authorization)=>{
    try{   

        let result=await pool(`SELECT quote_header_id AS id,quote_name AS name FROM ${db}.t_qb_quote_header WHERE quote_header_id IN (?) AND is_active=1`,[id])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get quote data"}
    }
}
/**
 * 
 * @description get total direct cost
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getTotalDirectCost = async(project_id, item_id,costing_sheet_id,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT SUM(direct_cost) AS direct_cost FROM ${db}.t_project_costing_direct_cost_items WHERE project_id=? AND project_item_id=? AND costing_header_id=?`,[project_id, item_id,costing_sheet_id])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get total direct cost"}
    }
}
/**
 * 
 * @description get total In direct cost
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getTotalInDirectCost = async(project_id, item_id,costing_sheet_id,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT SUM(total_quantity_cost) AS in_direct_cost FROM ${db}.t_project_costing_in_direct_cost_items WHERE project_id=? AND project_item_id=? AND costing_header_id=?`,[project_id, item_id,costing_sheet_id])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get total IN direct cost"}
    }
}
/**
 * 
 * @description release dcs
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.releaseDCS = async(project_id, item_id,costing_sheet_id,db, user, authorization)=>{
    try{   

        await pool(`UPDATE ${db}.t_project_costing_header SET status_id=1 WHERE project_id=? AND project_item_id=? AND status_id=2`,[project_id, item_id])
        let result=await pool(`UPDATE ${db}.t_project_costing_header SET status_id=2 WHERE project_id=? AND project_item_id=? AND id=?`,[project_id, item_id,costing_sheet_id])
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get total IN direct cost"}
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description costing sheet header creation
 * @returns 
 */
module.exports.createCostingSheetHeaderForQuote = async(data,name,quote,code, db, user)=>{
    try{
        let revenue_value = await utilService.covertToRespectiveCurrency(data.projectID,data.itemID, data.revenue, code, moment().format("YYYY-MM-DD"), db) 
        data['revenue_value'] = JSON.stringify(revenue_value);
        let planned_cost_value = await utilService.covertToRespectiveCurrency(data.projectID,data.itemID, data.planned_cost, code, moment().format("YYYY-MM-DD"), db) 
        data['planned_cost_value'] = JSON.stringify(planned_cost_value);
        let result = await pool(`INSERT INTO ${db}.t_project_costing_header (project_id,project_item_id,name,creation_type,status_id,currency,interval_type,total_revenue,total_revenue_value,gross_margin_percentage,planned_cost,planned_cost_value,created_by,quote_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,[data.projectID,data.itemID,name.costing_sheet_name,data.creation_type,1,data.code,'month',data.revenue,data.revenue_value,data.gross_margin_percentage,data.planned_cost,data.planned_cost_value,user.aid,JSON.stringify(quote)])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting createCostingSheetHeader !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description deleteCostingSheet
 * @returns 
 */
module.exports.deleteCostingSheet = async(data,db, user)=>{
    try{
        let result = await pool(`UPDATE ${db}.t_project_costing_header SET is_active=0 WHERE id=?`,[data.id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while deleteCostingSheet !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description delete Position
 * @returns 
 */
module.exports.deletePosition = async(id,db, user)=>{
    try{
        let result = await pool(`UPDATE ${db}.t_project_costing_direct_cost_items SET is_active=0 WHERE id=?`,[id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while delete position !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get resource name
 * @returns 
 */
module.exports.getResourceName = async(aid,current_date,db, user)=>{
    try{
        let result = await rpool(`SELECT first_name,middle_name,last_name FROM ${db}.t_e360_personal_details WHERE start_date<=? AND end_date>=? AND associate_id=?`,[current_date,current_date,aid])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while delete position !", data:[]})
    }
}
/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description Insert Position
 * @returns 
 */
module.exports.insertRequestPosition = async(project_id, item_id, data,costing_sheet_id,code,associate_id, db, user)=>{
    try{
        if(data.parent==null){
            data.parent=0
        }
        logger.info('success')
        let base_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.basePrice, code, moment().format("YYYY-MM-DD"), db) 
        data['base_price_value'] = JSON.stringify(base_price_value);
        let salary_price_value = await utilService.covertToRespectiveCurrency(project_id,item_id, data.salary, code, moment().format("YYYY-MM-DD"), db) 
        data['salary_price_value'] = JSON.stringify(salary_price_value);
        let result = await pool(`INSERT INTO ${db}.t_project_costing_direct_cost_items (costing_header_id,project_id,project_item_id,costing_type,costing_position,position,work_experience,work_location,base_price,base_price_value,salary,salary_value,entity_id,division_id,sub_division_id,billable,quote_id,parent,request_id,associate_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,[costing_sheet_id,project_id, item_id,'Costing Position',data.position,data.position,data.experience,data.workLocation,data.basePrice,data.base_price_value,data.salary,data.salary_price_value,data.entity,data.division,data.subDivision,data.isBillable==false?0:1,data.quote_id,data.parent,data.request_id,associate_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting position !", data:[]})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> K Vijay
 * @description get request id
 * @returns 
 */
module.exports.getRequestId = async(project_id, item_id,costing_sheet_id, db, user)=>{
    try{
        let result = await rpool(`SELECT request_id FROM ${db}.t_project_costing_direct_cost_items WHERE project_id=? AND project_item_id=? AND costing_header_id=? AND is_active=1`,[project_id, item_id,costing_sheet_id])
            return Promise.resolve({messType:"S", data: result})
       
       
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting request id !", data:[]})
    }
}
/**
 * 
 * @description get reosurce request position without duplicate
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceRequestWithoutDuplicate = async(item_id,quote_ids,request_ids,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT trr.request_id, trj.position,trr.work_experience AS experience,trr.work_location AS workLocation,trj.entity,trj.division,trj.sub_division AS subDivision,trr.quote_id,trr.quote_position_id AS parent_uniq FROM ${db}.t_rm_request trr
        INNER JOIN ${db}.t_rm_job_detail trj ON trj.request_id=trr.request_id 
        WHERE trr.project_id=? AND trr.quote_id IN(?) AND trr.request_id NOT IN(?) AND trr.is_active=1 AND trj.is_active=1`,[item_id,quote_ids,request_ids])
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get resource request"}
    }
}
/**
 * 
 * @description get reosurce request position associate id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getAssociatedAgainstRequestPosition = async(project_id,item_id,position,db, user, authorization)=>{
    try{   

        let result=await rpool(`SELECT associate_id FROM t_internal_stakeholders WHERE is_active=1 AND project_id=? AND item_id=? AND request_position_id=?`,[project_id,item_id,position])

        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return {messType:"E", message:"Error while get request position associate id"}
    }
}

/**
 * 
 * @param {*} db 
 * @description Milestone Type List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMilestoneTypeList = async(db)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.m_project_milestone_type WHERE is_active =1`)

        if(result.length>0)
        {
            return Promise.resolve({messType:"S",data:result})
        }
        else
        {
            return Promise.resolve({messType:"E",data:[]})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Milestone Type List"})
    }
}

/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description revert milestone from ytb
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.revertMilestoneFromYTB = async(milestone_id,db)=>{
    try{
    await pool(`UPDATE ${db}.t_milestones SET milestone_status_id=4 WHERE id=? AND is_active =1`,[milestone_id])
    await pool(`DELETE FROM ${db}.t_milestone_billing WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
    // await pool(`UPDATE ${db}.t_milestone_billing_advice_info SET is_active =0 WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
    return Promise.resolve({messType:"S"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Milestone Type List"})
    }
}
/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description getMilestoneGroupData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getMilestoneGroupData = async(milestone_id,db)=>{
    try{
    let result=await rpool(`SELECT milestone_group FROM ${db}.t_milestones WHERE is_active=1 AND id=?`,[milestone_id])
    return Promise.resolve({messType:"S",data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Milestone Type List"})
    }
}

/**
 * 
 * @param {*} db
 * @param {*} associate_id
 * @param {*} start_date
 * @param {*} end_date
 * @description getAllocatedHoursForEmployeeWithDates
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getAllocatedHoursForEmployeeWithDates = async(associate_id,start_date,end_date,db)=>{
    try{



        let result=await rpool(`SELECT SUM(mch.hours) AS allocated_hours
        FROM ${db}.t_e360_time_details ted
        JOIN ${db}.m_calendar_hours mch
        ON ted.holiday_calender = mch.calendar_id AND ted.work_schedule = mch.workschedule
        WHERE ted.associate_id =?
        AND mch.date BETWEEN ? AND ?
        AND mch.type = 'R'
        AND mch.date NOT IN 
        (
            SELECT DATE(leave_end_date) AS DATE
            FROM ${db}.t_e360_leave_details
            WHERE leave_end_date>=? AND leave_end_date<=? AND is_active=1 AND associate_id=?
        )`,[associate_id,start_date,end_date, start_date, end_date, associate_id])
       
        if(result.length>0)
            return Promise.resolve({messType:"S",data:result[0]})
        else
        return Promise.resolve({messType:"S",data:{allocated_hours: 0}})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve getAllocatedHoursForEmployeeWithDates"})
    }
}

/**
 * 
 * @description getIsaData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getIsaData = async(project_id,item_id,db, user, authorization)=>{
    try{   
        let current_date=moment().format('YYYY-MM-DD')
        // logger.info(data)
        let result=await rpool(`SELECT tis.associate_id,tepd.first_name,tepd.middle_name,tepd.last_name,tis.id,tis.billable,tis.identity, tis.rate_card_id FROM ${db}.t_internal_stakeholders tis
        INNER JOIN ${db}.t_e360_personal_details tepd ON tepd.associate_id=tis.associate_id AND tepd.start_date<=? AND tepd.end_date>=?
        WHERE tis.project_id=? AND tis.item_id=? AND tis.billable=1 AND tepd.is_active=1 AND tis.is_active=1`,[current_date,current_date,project_id,item_id])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getIsaData!"})
    }
}
/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description Fetch Billing Advice Data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.fetchBillingAdviceData = async(milestone_group,db)=>{
    try{
    let result=await rpool(`SELECT BA.associate_id,TS.oid,BA.planned_billable_hours,
    BA.actual_billable_hours, BA.per_hour_rate, BA.work_location, TS.sub_division_id, TS.division_id, TS.entity_id, TS.rate_card_id as position_id,TS.id as isaId,
    CONCAT(
      COALESCE(CONCAT(PD.first_name,' '), ''),
      COALESCE(CONCAT(PD.middle_name,' '), ''),
      COALESCE(PD.last_name, '')
  ) AS employee_name
    FROM ${db}.t_milestone_billing_advice_info AS BA
    LEFT JOIN ${db}.t_internal_stakeholders AS TS ON TS.id = BA.isa_id AND TS.is_active = 1
    LEFT JOIN ${db}.t_e360_personal_details PD ON PD.associate_id = BA.associate_id AND PD.end_date='9999-12-31' AND PD.is_active=1
    WHERE BA.milestone_id IN (?) AND BA.is_active = 1`,[milestone_group])
    return Promise.resolve({messType:"S",data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while Fetch Billing Advice Data"})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} associate_id
 * @param {*} start_date
 * @param {*} duration
 * @description getEndDateForEmployeeWithDuration
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getEndDateForEmployeeWithDuration = async(associate_id,start_date,duration,current_date,project_end_date,db)=>{
    try{

    let result=await rpool(`WITH time_details AS (
        SELECT holiday_calender, work_schedule
        FROM ${db}.t_e360_time_details
        WHERE associate_id = ? AND start_date<=? AND end_date>=?
    ),
    accumulated_hours AS (
        SELECT
            DATE,
            SUM(hours) OVER (ORDER BY DATE) AS cumulative_hours
        FROM ${db}.m_calendar_hours mch
        JOIN time_details td
        ON mch.calendar_id = td.holiday_calender AND mch.workschedule = td.work_schedule
        WHERE mch.date >= ?
          AND mch.type = 'R'
    )
    SELECT DATE AS end_date
    FROM accumulated_hours
    WHERE cumulative_hours >= ?
    ORDER BY DATE
    LIMIT 1;`,[associate_id,current_date,current_date,start_date,duration])

    if(moment(result[0].end_date).format('YYYY-MM-DD')>project_end_date){
        return Promise.resolve({messType:"NV",data:result})
    }
    else{
        return Promise.resolve({messType:"S",data:result})
    }
   
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve getEndDateForEmployeeWithDuration"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} position_id 
 * @param {*} isa_id 
 * @description getPositionCummulativeAlloctaedHours
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getPositionCummulativeAlloctaedHours = async(db,position_id,isa_id)=>{
    try{

    let query = `SELECT SUM(tis.allocated_hours) AS allocated_hours
    FROM ${db}.t_internal_stakeholders AS tis
    WHERE tis.rate_card_id = ? AND tis.is_active = 1 `;
    let query_input = [
        position_id
    ]
    if(isa_id){
        query += ` AND tis.id != ?`
        query_input.push(isa_id)
    }
    let result=await rpool(query,query_input)
    if(result && result.length > 0){
        result = result[0]
        return Promise.resolve({messType:"S",data:result})
    }
    else{
        return Promise.resolve({messType:"S",data:null})
    }
   
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieving getPositionCummulativeAlloctaedHours"})
    }
}
/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description Get RPA Project FTE Details
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getRPAProjectFTEDetails = async(milestone_group,db)=>{
    try{
    let result=await rpool(`SELECT milestone_name AS description,milestone_value FROM ${db}.t_milestones WHERE is_active=1 AND id IN (?)`,[milestone_group])
    return Promise.resolve({messType:"S",data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while Get RPA Project FTE Details"})
    }
}
/**
 *  
 * @description getIsaData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getIsaDataAll = async(project_id,item_id,db, user, authorization)=>{
    try{   
        let current_date=moment().format('YYYY-MM-DD')
        // logger.info(data)
        let result=await rpool(`SELECT tis.associate_id,tepd.first_name,tepd.middle_name,tepd.last_name,tis.id,tis.billable,tis.identity, tis.rate_card_id FROM ${db}.t_internal_stakeholders tis
        INNER JOIN ${db}.t_e360_personal_details tepd ON tepd.associate_id=tis.associate_id AND tepd.start_date<=? AND tepd.end_date>=?
        WHERE tis.project_id=? AND tis.item_id=? AND tepd.is_active=1 AND tis.is_active=1`,[current_date,current_date,project_id,item_id])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getIsaData!"})
    }
}
/**
 * 
 * @description getIsaData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getIsaDataFlexi = async(project_id,item_id,db, user, authorization)=>{
    try{   
        let current_date=moment().format('YYYY-MM-DD')
        // logger.info(data)
        let result=await rpool(`SELECT tis.associate_id,tepd.first_name,tepd.middle_name,tepd.last_name,tis.id,tis.billable,tis.identity, tis.rate_card_id FROM ${db}.t_internal_stakeholders tis
        INNER JOIN ${db}.t_e360_personal_details tepd ON tepd.associate_id=tis.associate_id AND tepd.start_date<=? AND tepd.end_date>=?
        WHERE tis.project_id=? AND tis.item_id=? AND (tis.billable=1 OR (tis.billable=0 AND tis.identity=2)) AND tepd.is_active=1 AND tis.is_active=1`,[current_date,current_date,project_id,item_id])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getIsaData!"})
    }
}
/**
 * 
 * @description getRateCardPosition
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getRateCardPosition = async(project_id,item_id,db, user, authorization)=>{
    try{   
        let customer_id=await rpool(`SELECT end_customer_id FROM ${db}.t_project_header WHERE id=?`,[project_id])
        let result=await rpool(`SELECT  mr.name AS position_name,mrmc.id AS rate_card_id, mrmc.id AS position_id, mrmc.quote_unit AS unit
        FROM ${db}.m_resource_manpower_cost mrmc
        LEFT JOIN ${db}.m_role mr ON mr.id = mrmc.position
        WHERE mrmc.account_id = ? AND mrmc.is_active =1;`,[customer_id[0].end_customer_id])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getRateCardPosition!"})
    }
}
/**
 * 
 * @description getResourceLoadingData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceLoadingData = async(project_id,item_id,quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT quote_position_id AS position_id,planned_quote_position_balance AS planned_hours,current_quote_position_balance AS balance ,version_id FROM ${db}.t_project_resource_loading_header WHERE project_id=? AND project_item_id=? AND quote_id=? AND is_active=1`,[project_id,item_id,quote_id])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getResourceLoadingData!"})
    }
}
/**
 * 
 * @description saveResourceLoadingData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.saveResourceLoadingData = async(data,db, user, authorization)=>{
    try{   
        logger.info(data)
        let result=await pool(`INSERT INTO ${db}.t_project_resource_loading(isa_id,project_id,project_item_id,quote_id,position,month,year,planned_quantity,actual_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE isa_id=DAT.isa_id,project_id=DAT.project_id,project_item_id=DAT.project_item_id,quote_id=DAT.quote_id,position=DAT.position,month=DAT.month,year=DAT.year,quantity=DAT.quantity,version_id=DAT.version_id`,[data])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while saveResourceLoadingData!"})
    }
}
/**
 * 
 * @description getProjectDates
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getProjectDates = async(project_id,project_item_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT planned_start_date,planned_end_date FROM ${db}.t_project_item WHERE id=?`,[project_item_id])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getProjectDates!"})
    }
}
/**
 * 
 * @description getQuoteDetailsForResourceLoading
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuoteDetailsForResourceLoading = async(quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT quote_name,quote_header_id AS quote_id,quote_currency AS currency,quote_amount AS quote_value FROM ${db}.t_qb_quote_header
        WHERE is_active=1 AND quote_header_id=?`,[quote_id])

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getProjectDates!"})
    }
}
/**
 * 
 * @description getQuotePositionBalance
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuotePositionList = async(quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT tqp.quote_position_id FROM ${db}.t_qb_quote_service tqs
        INNER JOIN ${db}.t_qb_quote_position tqp ON tqp.quote_service_id=tqs.quote_service_id
        WHERE tqs.is_active=1 AND tqs.quote_header_id=?`,[quote_id])
        return Promise.resolve({messType:"S", data: result})
     
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalance!"})
    }
}
/**
 * 
 * @description getQuotePositionBalanceFromResource
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuotePositionBalanceFromResource = async(quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT SUM(current_quote_position_balance) AS quantity,SUM(planned_quote_position_balance) AS planned_quantity,status_id FROM ${db}.t_project_resource_loading_header
        WHERE is_active=1 AND quote_id=?`,[quote_id])
        return Promise.resolve({messType:"S", data: result,status_id:result1[0].status_id})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalanceFromResource!"})
    }
}
/**
 * 
 * @description getQuotePositionBalanceFromResource
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuotePositionBalanceFromResource = async(quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT SUM(current_quote_position_balance) AS quantity,SUM(planned_quote_position_balance) AS planned_quantity,status_id FROM ${db}.t_project_resource_loading_header
        WHERE is_active=1 AND quote_id=?`,[quote_id])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalanceFromResource!"})
    }
}
/**
 * 
 * @description getActiveAndInactiveAllocation
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getActiveAndInactiveAllocation = async(project_id,project_item_id,quote_position_id,db, user, authorization)=>{
    try{   
        let active=await rpool(`SELECT COUNT(associate_id) AS active FROM ${db}.t_internal_stakeholders WHERE is_active=1 AND project_id=? AND item_id=? AND rate_card_id=? AND start_date<=? AND end_date>=?`,[project_id,project_item_id,quote_position_id,moment().format('YYYY-MM-DD'),moment().format('YYYY-MM-DD')])
        let total=await rpool(`SELECT COUNT(associate_id) AS total FROM ${db}.t_internal_stakeholders WHERE is_active=1 AND project_id=? AND item_id=? AND rate_card_id=?`,[project_id,project_item_id,quote_position_id])
        return Promise.resolve({messType:"S", active: active,total:total})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalanceFromResource!"})
    }
}
/**
 * 
 * @description getPlannedLoadingData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getPlannedLoadingData = async(quote_id,quote_position_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT month,year,planned_quantity AS planned FROM ${db}.t_project_resource_loading_planned WHERE quote_id=? AND quote_position_id=? AND is_active=1`,[quote_id,quote_position_id])
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalanceFromResource!"})
    }
}
/**
 * 
 * @description getActualLoadingData
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getActualLoadingData = async(quote_id,quote_position_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT month,year,actual_quantity AS actual FROM ${db}.t_project_resource_loading_actual WHERE quote_id=? AND quote_position_id=? AND is_active=1`,[quote_id,quote_position_id])
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getQuotePositionBalanceFromResource!"})
    }
}
/**
 * 
 * @description insertResourceLoadingHeader
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertResourceLoadingHeader = async(project_id,project_item_id,quote_id,version_id,status_id,action_id,item,db, user, authorization)=>{
    try{   
        let format_data=[]
        if(item){
           item['project_id']=project_id
           item['project_item_id']=project_item_id
           item['quote_id']=quote_id
           item['version_id']=version_id
           item['status_id']=status_id
           item['action_id']=action_id
           format_data.push([project_id,project_item_id,quote_id,item['position_id'],item['planned_hours'],item['balance'],action_id,version_id,status_id,user.aid])
        }
        // logger.info('--------------------format_data--------------------')
        // logger.info(format_data)
        let version_check_id=await rpool(`SELECT version_id FROM ${db}.t_project_resource_loading_header WHERE is_active=1 AND  project_id=? AND project_item_id=? AND quote_id=? AND quote_position_id=?`,[project_id,project_item_id,quote_id,item['position_id']])
        // await pool(`UPDATE ${db}.t_project_resource_loading_header SET is_active=0,action_id=2 WHERE project_id=? AND project_item_id=? AND quote_id=? AND quote_position_id=?`,[project_id,project_item_id,quote_id,item['position_id']])
        let result
        // logger.info('version_check_id')
        // logger.info(version_check_id)
        if(version_check_id.length>0){
            // logger.info('-------version----------')
            // logger.info(version_check_id[0])
            // logger.info(version_id)
            if(version_check_id[0].version_id==version_id){
                 result=await pool(`INSERT INTO ${db}.t_project_resource_loading_header(project_id,project_item_id,quote_id,quote_position_id,planned_quote_position_balance,current_quote_position_balance,action_id,version_id,status_id,changed_by)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE project_id=DAT.project_id,project_item_id=DAT.project_item_id,quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,planned_quote_position_balance=DAT.planned_quote_position_balance,current_quote_position_balance=DAT.current_quote_position_balance,action_id=DAT.action_id,version_id=DAT.version_id,status_id=DAT.status_id,changed_by=DAT.changed_by`,[format_data])
            }
            else{
                await pool(`UPDATE ${db}.t_project_resource_loading_header SET is_active=0 WHERE project_id=? AND project_item_id=? AND quote_id=? AND quote_position_id=?`,[project_id,project_item_id,quote_id,item['position_id']])
             result=await pool(`INSERT INTO ${db}.t_project_resource_loading_header(project_id,project_item_id,quote_id,quote_position_id,planned_quote_position_balance,current_quote_position_balance,action_id,version_id,status_id,changed_by)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE project_id=DAT.project_id,project_item_id=DAT.project_item_id,quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,planned_quote_position_balance=DAT.planned_quote_position_balance,current_quote_position_balance=DAT.current_quote_position_balance,action_id=DAT.action_id,version_id=DAT.version_id,status_id=DAT.status_id,changed_by=DAT.changed_by`,[format_data])
            }
    }
    else{
         result=await pool(`INSERT INTO ${db}.t_project_resource_loading_header(project_id,project_item_id,quote_id,quote_position_id,planned_quote_position_balance,current_quote_position_balance,action_id,version_id,status_id,changed_by)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE project_id=DAT.project_id,project_id=DAT.project_id,project_item_id=DAT.project_item_id,quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,planned_quote_position_balance=DAT.planned_quote_position_balance,current_quote_position_balance=DAT.current_quote_position_balance,action_id=DAT.action_id,version_id=DAT.version_id,status_id=DAT.status_id,changed_by=DAT.changed_by`,[format_data])
    }
       
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingHeader!"})
    }
}
/**
 * 
 * @description insertResourceLoadingPlanned
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertResourceLoadingPlanned= async(project_id,project_item_id,quote_id,version_id,status_id,item,db, user, authorization)=>{
    try{   
       let format_data=[]
        if(item){
            logger.info(item['monthList'])
            for(let items of item['monthList']){
                items['project_id']=project_id
                items['project_item_id']=project_item_id
                items['quote_id']=quote_id
                items['version_id']=version_id
                items['status_id']=status_id
                items['position_id']=item['position_id']
                format_data.push([quote_id,item['position_id'],items['month'],items['year'],items['planned'],version_id,item['is_position']])
            }
        }
        let result=[]
        let version_check_id=await rpool(`SELECT DISTINCT version_id FROM ${db}.t_project_resource_loading_planned WHERE is_active=1 AND quote_id=? AND quote_position_id=? ORDER BY version_id DESC`,[quote_id,item['position_id']])
        if(version_check_id.length>0){
            if(version_check_id[0].version_id==version_id){
                result=await pool(`INSERT INTO ${db}.t_project_resource_loading_planned(quote_id,quote_position_id,month,year,planned_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,planned_quantity=DAT.planned_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
            }
            else{
                
                await pool(`UPDATE ${db}.t_project_resource_loading_planned SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=1`,[quote_id,item['position_id']])
                result=await pool(`INSERT INTO ${db}.t_project_resource_loading_planned(quote_id,quote_position_id,month,year,planned_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,planned_quantity=DAT.planned_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
            }
        }
        else{
            await pool(`UPDATE ${db}.t_project_resource_loading_planned SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=1`,[quote_id,item['position_id']])
            result=await pool(`INSERT INTO ${db}.t_project_resource_loading_planned(quote_id,quote_position_id,month,year,planned_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,planned_quantity=DAT.planned_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
        }
       
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingPlanned!"})
    }
}
/**
 * 
 * @description insertResourceLoadingPlanned
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertResourceLoadingPlannedForEmployee= async(project_id,project_item_id,quote_id,version_id,status_id,item,db, user, authorization)=>{
    try{   
      let format_data=[]
        if(item){
            for(let items of item['monthList']){
                items['project_id']=project_id
                items['project_item_id']=project_item_id
                items['quote_id']=quote_id
                items['version_id']=version_id
                items['status_id']=status_id
                items['position_id']=item['position_id']
                format_data.push([quote_id,item['position_id'],items['isa_id'],items['employee_id'],items['month'],items['year'],items['planned'],version_id,item['is_position']])
            }
           
        }
        await pool(`UPDATE ${db}.t_project_resource_loading_planned SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=0`,[quote_id,item['position_id']])
        let result=await pool(`INSERT INTO ${db}.t_project_resource_loading_planned(quote_id,quote_position_id,isa_id,employee_id,month,year,planned_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,planned_quantity=DAT.planned_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingPlanned!"})
    }
}
/**
 * 
 * @description insertResourceLoadingPlanned
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertResourceLoadingActual= async(project_id,project_item_id,quote_id,version_id,status_id,item,db, user, authorization)=>{
    try{   
       let format_data=[]
        if(item){
            for(let items of item['monthList']){
                items['project_id']=project_id
                items['project_item_id']=project_item_id
                items['quote_id']=quote_id
                items['version_id']=version_id
                items['status_id']=status_id
                items['position_id']=item['position_id']
                format_data.push([quote_id,item['position_id'],items['month'],items['year'],items['actual'],version_id,item['is_position']])
            }
           
        }
        let result=[]
        let version_check_id=await rpool(`SELECT DISTINCT version_id FROM ${db}.t_project_resource_loading_actual WHERE is_active=1 AND quote_id=? AND quote_position_id=? ORDER BY version_id DESC`,[quote_id,item['position_id']])
        if(version_check_id.length>0){
            if(version_check_id[0].version_id==version_id){
                result=await pool(`INSERT INTO ${db}.t_project_resource_loading_actual(quote_id,quote_position_id,month,year,actual_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,actual_quantity=DAT.actual_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
            }
            else{
                await pool(`UPDATE ${db}.t_project_resource_loading_actual SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=1`,[quote_id,item['position_id']])
           result=await pool(`INSERT INTO ${db}.t_project_resource_loading_actual(quote_id,quote_position_id,month,year,actual_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,actual_quantity=DAT.actual_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
            }
        }
        else{
            await pool(`UPDATE ${db}.t_project_resource_loading_actual SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=1`,[quote_id,item['position_id']])
           result=await pool(`INSERT INTO ${db}.t_project_resource_loading_actual(quote_id,quote_position_id,month,year,actual_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,actual_quantity=DAT.actual_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
        }
       
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingPlanned!"})
    }
}
/**
 * 
 * @description insertResourceLoadingPlanned
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertResourceLoadingActualForEmployee= async(project_id,project_item_id,quote_id,version_id,status_id,item,db, user, authorization)=>{
    try{   
       let format_data=[]
        if(item){
            for(let items of item['monthList']){
                items['project_id']=project_id
                items['project_item_id']=project_item_id
                items['quote_id']=quote_id
                items['version_id']=version_id
                items['status_id']=status_id
                items['position_id']=item['position_id']
                format_data.push([quote_id,item['position_id'],items['isa_id'],items['employee_id'],items['month'],items['year'],items['actual'],version_id,item['is_position']])
            }
           
        }
        await pool(`UPDATE ${db}.t_project_resource_loading_actual SET is_active=0 WHERE quote_id=? AND quote_position_id=? AND is_position=0`,[quote_id,item['position_id']])
        let result=await pool(`INSERT INTO ${db}.t_project_resource_loading_actual(quote_id,quote_position_id,isa_id,employee_id,month,year,actual_quantity,version_id,is_position)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,month=DAT.month,year=DAT.year,actual_quantity=DAT.actual_quantity,version_id=DAT.version_id,is_position=DAT.is_position`,[format_data])
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingPlanned!"})
    }
}
/**
 * 
 * @description getResourceLoadingHeaderVersion
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceLoadingHeaderVersion= async(project_id,project_item_id,quote_id,db, user, authorization)=>{
    try{
        let result=await rpool(`SELECT SUM(current_quote_position_balance) AS total_quote_balance,version_id,changed_by,
        status_id,
        action_id,
        changed_on FROM ${db}.t_project_resource_loading_header WHERE project_id=? AND project_item_id=? AND quote_id=? GROUP BY version_id`,[project_id,project_item_id,quote_id])
        return Promise.resolve({messType:"S", data:result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while insertResourceLoadingPlanned!"})
    }
}
/**
 * 
 * @description getrrBalance
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getrrBalance = async(quote_position_ids,project_item_id,quote_id, resource_type_id, db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT tqqh.quote_header_id , tqqp.quote_position_id, tqqh.quote_name,mr.name AS position_name, ROUND(SUM(tqqpe.hours) * tqqp.resource_count, 2) AS quantity
        FROM ${db}.t_qb_quote_header AS tqqh
        LEFT JOIN ${db}.t_qb_quote_service AS tqqs ON tqqs.quote_header_id = tqqh.quote_header_id AND tqqs.is_active = 1
        LEFT JOIN ${db}.t_qb_quote_position AS tqqp ON tqqp.quote_service_id =  tqqs.quote_service_id AND tqqp.is_active = 1
        LEFT JOIN ${db}.t_qb_quote_position_effort tqqpe ON tqqpe.quote_position_id = tqqp.quote_position_id AND tqqpe.quote_id = tqqh.quote_header_id AND tqqpe.is_active = 1 
        INNER JOIN ${db}.m_role mr ON mr.id = tqqp.position AND mr.is_active = 1
        WHERE tqqh.quote_header_id = ? AND tqqh.is_active = 1 AND tqqp.resource_type_id =? GROUP BY tqqp.quote_position_id`,[quote_id, resource_type_id])
        return Promise.resolve({messType:"S", data: result})
     
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getrrBalance!"})
    }
}
/**
 * 
 * @description checkQuoteChangeandUpdate
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.checkQuoteChangeandUpdate = async(project_id,item_id,quote_id,db, user, authorization)=>{
    try{   
        let result=await rpool(`SELECT quote_position_id AS position_id,planned_quote_position_balance AS planned_hours,current_quote_position_balance AS balance ,version_id,status_id,changed_by FROM ${db}.t_project_resource_loading_header WHERE project_id=? AND project_item_id=? AND quote_id=? AND is_active=1`,[project_id,item_id,quote_id])
        // logger.info('test quote change 1')
        // logger.info(result)
      if(result.length>0){
        let quote_position_ids=_.pluck(result,"position_id")
        let check=0
        // logger.info('test quote change')
        // logger.info(result)
        for(let item of result){
            let result1=await rpool(`SELECT position_quantity_hours as quantity,quote_position_id FROM ${db}.t_rr_quote_position_details
        WHERE is_active=1 AND project_id=? AND position_type=2 AND quote_position_id =?`,[item_id,item['position_id']])
        logger.info(result1)
        if(result1.length>0){
            if(item['planned_hours']!=result1[0]['quantity']){
                check=check+1
                if(item['planned_hours']<result1[0]['quantity']){
                    item['diff']=result1[0]['quantity']-item['planned_hours']
                }
                else{
                    item['diff']=result1[0]['quantity']-item['planned_hours']
                }
                // item['diff']=item['planned_hours']-result1[0]['quantity']
                item['planned_hours']=result1[0]['quantity']
            }
        }

        }
        logger.info(check)
        if(check>0){
          let format_data=[]
          logger.info('success testing quote change')
          let updated_version
            for(let item of result){
                format_data.push([project_id,item_id,quote_id,item['position_id'],item['planned_hours'],item['balance'] + item['diff'],2,item['version_id']+1,item['status_id'],item['changed_by']])
                updated_version=item['version_id']+1
            }

            await pool(`UPDATE ${db}.t_project_resource_loading_header SET is_active=0 WHERE project_id=? AND project_item_id=? AND quote_id=? AND quote_position_id IN (?)`,[project_id,item_id,quote_id,quote_position_ids])
            result=await pool(`INSERT INTO ${db}.t_project_resource_loading_header(project_id,project_item_id,quote_id,quote_position_id,planned_quote_position_balance,current_quote_position_balance,action_id,version_id,status_id,changed_by)  VALUES ? AS DAT ON DUPLICATE KEY UPDATE project_id=DAT.project_id,project_id=DAT.project_id,project_item_id=DAT.project_item_id,quote_id=DAT.quote_id,quote_position_id=DAT.quote_position_id,planned_quote_position_balance=DAT.planned_quote_position_balance,current_quote_position_balance=DAT.current_quote_position_balance,action_id=DAT.action_id,version_id=DAT.version_id,status_id=DAT.status_id,changed_by=DAT.changed_by`,[format_data])
            await pool(`UPDATE ${db}.t_project_resource_loading_planned SET version_id=? WHERE quote_id=? AND quote_position_id IN (?) AND is_active=1`,[updated_version,quote_id,quote_position_ids])
            await pool(`UPDATE ${db}.t_project_resource_loading_actual SET version_id=? WHERE quote_id=? AND quote_position_id IN (?) AND is_active=1`,[updated_version,quote_id,quote_position_ids])
        }
    }
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getResourceLoadingData!"})
    }
}

/**
 * 
 * @description loadResourceLoadingFromQuote
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.loadResourceLoadingFromQuote = async(project_start_date,project_end_date,quote_id,db, user, authorization)=>{
    try {
        let result = await rpool(`SELECT 
        TQPE.quote_id,
        TQPE.quote_position_id, 
        MCH.month, 
        MCH.year, 
        MCH.type, 
        SUM(MCH.hours) AS total_max_hours, 
        SUM(TQPE.hours) AS planned
    FROM ${db}.m_calendar_hours MCH 
    INNER JOIN  ${db}.t_qb_quote_position_effort TQPE 
    ON 
        TQPE.date = MCH.date 
        AND TQPE.quote_id = ?
        AND TQPE.is_active = 1
    WHERE 
        MCH.calendar_id = 1 
        AND MCH.workschedule = 1 
        AND MCH.is_active = 1 
        AND MCH.date BETWEEN ? AND ?
    GROUP BY 
        TQPE.quote_position_id, MCH.month, MCH.year, MCH.type
    ORDER BY 
        MCH.year ASC, MCH.month ASC;
    `, [quote_id,project_start_date,project_end_date])

        return Promise.resolve({ messType: "S", data: result })
    }
    catch (err) {
        logger.info(err)
        return Promise.resolve({ messType: "E", message: "Error while getResourceLoadingData!" })
    }
}


/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @decription Get Billing Plan Version Id
 * @returns 
 */
module.exports.getBillingPlanVersionId = async(project_id, project_item_id, quote_id, resource_type_id, deliverable_id, db, user, authorization)=>{
    try{
        let query_params = [project_id, project_item_id, quote_id,  resource_type_id];

        let query ="";

        if(deliverable_id)
        {
            query_params.push(deliverable_id)
            query += ` AND deliverable_id =?`
        }

        let version_id = await rpool(`SELECT forecast_version_id, save_version_id FROM ${db}.t_project_billing_plan_header WHERE project_id = ? AND project_item_id = ? AND quote_id = ? AND resource_type_id =? AND is_active =1`+query+` ORDER BY id DESC LIMIT 1 `,query_params)

        if(version_id.length>0)
        {
            return Promise.resolve({messType:"S", data: version_id[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No Billing Plan Version found!"})
        }
    }
    catch(err){
        logger.info(err)

        return Promise.resolve({messType:"E", error: err, message:"Error while getting Billing Advice Version History!"})
    }
  }

/**
 * @param {*} milestone_id 
 * @param {*} db 
 * @description Get Billing Advice Version History
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingAdviceVersionHistory = async(milestone_id, db)=>{
    try{
        let database = mongo.client.db(db);

        let data = await database.collection('t_project_billing_advice_version').find({milestone_id: parseInt(milestone_id)}).toArray();

        let i =1;

        for(let d of data)
        {
            d['version_id'] = i;

            i = i+1;
        }

        return Promise.resolve({messType:"S", data: data})

        
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Version Id"})
    }
  }

  /**
}

/**
 * 
 * @param {*} formatHeaderData 
 * @param {*} db 
 * @description Insert Billing Plan Header
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertBillingPlanHeader = async(formatHeaderData, db)=>{
    try{
        let insert = await pool(`INSERT INTO ${db}.t_project_billing_plan_header (project_id, project_item_id, quote_id, resource_type_id, deliverable_id,
            action_type, forecast_version_id, save_version_id, status_id, created_by, modified_by, is_active) VALUES ?
            AS DAT ON DUPLICATE KEY UPDATE project_id = DAT.project_id, project_item_id = DAT.project_item_id, quote_id = DAT.quote_id, 
            resource_type_id = DAT.resource_type_id, deliverable_id = DAT.deliverable_id,
            action_type = DAT.action_type, forecast_version_id = DAT.forecast_version_id, save_version_id = DAT.save_version_id, status_id = DAT.status_id,
            modified_by = DAT.modified_by, is_active = DAT.is_active`,[formatHeaderData]);

        return Promise.resolve({messType:"S", data: insert})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while inserting Billing Plan Header Details"})
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} forecast_version_id 
 * @param {*} save_version_id 
 * @param {*} db 
 * @description Get Billing Plan Header Id
 * @returns 
 */
module.exports.getBillingPlanHeaderId = async(project_id, project_item_id, quote_id, resource_type_id, deliverable_id, forecast_version_id, save_version_id, db)=>{
    try{
        
        let query_params = [project_id, project_item_id, quote_id,  resource_type_id, forecast_version_id, save_version_id];

        let query ="";

        if(deliverable_id)
        {
            query_params.push(deliverable_id)
            query += ` AND deliverable_id =?`
        }

        let header_id = await pool(`SELECT id FROM ${db}.t_project_billing_plan_header WHERE project_id = ? AND project_item_id = ? AND quote_id =?  AND resource_type_id = ? AND forecast_version_id =? AND save_version_id = ? AND is_active = 1 `+ query,query_params);

        if(header_id.length>0)
        {
            return Promise.resolve({messType:"S", data: header_id[0]['id']})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Header Id"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Header Id"})
    }
}


/**
 * 
 * @param {*} formatItemData
 * @param {*} db 
 * @description Insert Billing Plan Item
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertBillingPlanItem = async(formatItemData, db)=>{
    try{
        let insert = await pool(`INSERT INTO ${db}.t_project_billing_plan_item (billing_plan_header_id, rate_card_id, planned_quantity, created_by, modified_by) VALUES ?
            AS DAT ON DUPLICATE KEY UPDATE billing_plan_header_id = DAT.billing_plan_header_id, rate_card_id = DAT.rate_card_id, planned_quantity = DAT.planned_quantity,
            modified_by = DAT.modified_by`,[formatItemData]);

        return Promise.resolve({messType:"S", data: insert})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while inserting Billing Plan Header Details"})
    }
}

/**
 * 
 * @param {*} billing_plan_header_id 
 * @param {*} db 
 * @description Get Billing Plan Item Id
 * @returns 
 */
module.exports.getBillingPlanItemId = async(billing_plan_header_id, db)=>{
    try{
        let result = await pool(`SELECT id, rate_card_id, planned_quantity FROM ${db}.t_project_billing_plan_item WHERE billing_plan_header_id =? AND is_active = 1`,[billing_plan_header_id]);

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Billing Plan Item Id"})
    }
}


/**
 * 
 * @param {*} formatItemData
 * @param {*} db 
 * @description insertBillingPlanDetails
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertBillingPlanDetails = async(formatItemDetailsData, db)=>{
    try{
        let insert = await pool(`INSERT INTO ${db}.t_project_billing_plan (billing_plan_item_id, month, year, planned_quantity, created_by, modified_by) VALUES ?
            AS DAT ON DUPLICATE KEY UPDATE billing_plan_item_id = DAT.billing_plan_item_id, month = DAT.month, year = DAT.year, planned_quantity = DAT.planned_quantity,
            modified_by = DAT.modified_by`,[formatItemDetailsData]);

        return Promise.resolve({messType:"S", data: insert})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while inserting Billing Plan Header Details"})
    }
}

/**
 * 
 * @param {*} billing_plan_header_id 
 * @param {*} db 
 * @description Revert Billing Plan Header
 * @returns 
 */
module.exports.revertBillingPlanHeader = async(billing_plan_header_id, db)=>{
    try{
        let revert = await pool(`UPDATE ${db}.t_project_billing_plan_header SET is_active = 0 WHERE id =?`,[billing_plan_header_id]);

        return Promise.resolve({messType:"S", data: revert})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while reverting Billing Plan HEader"})
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} db 
 * @description get Project Financial Details Quote Ids
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProjectFinancialDetailsQuoteIds = async(project_id, project_item_id, db)=>{
    try{
        let result = await rpool(`SELECT tpfd.quote_id, tpi.planned_start_date, tpi.planned_end_date, tpfd.project_id, tpfd.project_item_id
            FROM ${db}.t_project_financial_details tpfd
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tpfd.project_item_id
            WHERE tpfd.project_id = ? AND tpfd.project_item_id = ? AND tpfd.is_active = 1`,[project_id, project_item_id]);

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Project Financial Details Quote Ids"})
    }
}

/**
 * 
 * @param {*} quote_id 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Billing Plan Header For Quote
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanHeaderForQuote = async(quote_id, project_id, project_item_id, db, user, authorization)=>{
    try{
        let billingPlanResult = await rpool(`SELECT * FROM ${db}.t_project_billing_plan_header WHERE project_id = ? AND project_item_id = ? AND quote_id =? AND is_active = 1`,[project_id, project_item_id, quote_id]);

        if(billingPlanResult.length>0)
        {
            return Promise.resolve({messType:"S", message:"Billing Plan For Quote Id is present"})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No Billing Plan for Quote Id is present"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Header For Quote"})
    }
}




/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Billing Plan Version History
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanVersionHistory = async(project_id, project_item_id, quote_id, deliverable_id, resource_type_id, db, user, authorization)=>{
    try{
        let query_params = [project_id, project_item_id, quote_id,  resource_type_id];

        let query ="";

        if(deliverable_id)
        {
            query_params.push(deliverable_id)
            query += ` AND deliverable_id =?`
        }

        let result = await rpool(`SELECT * FROM ${db}.t_project_billing_plan_header WHERE project_id = ? AND project_item_id = ? AND quote_id = ? AND resource_type_id =? AND is_active = 1 `+ query +` ORDER BY id DESC`,query_params)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Version History!"})
    }
}


/**
 * 
 * @param {*} billing_plan_item_ids 
 * @param {*} db 
 * @description Get Billing Plan Details
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanPlannedDetails = async(billing_plan_item_ids, db)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.t_project_billing_plan WHERE billing_plan_item_id IN (?) AND is_active = 1`,[billing_plan_item_ids])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}


/**
 * 
 * @param {*} rate_card_id
 * @param {*} db 
 * @description Get Billing Plan Actual Details
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanActualDetails = async(project_item_id, rate_card_id, db)=>{
    try{
        let result = await rpool(`SELECT tra.item_id,tra.quote_position_id,DATE_FORMAT(tra.posting_date,"%m-%Y") AS month_year,tra.posting_date,
            SUM(tra.no_of_billable_actual_hours) AS actual_hours, SUM(tra.reporting_currency_1_value) AS reporting_currency_1_value, 
            SUM(tra.reporting_currency_2_value) AS reporting_currency_2_value, MONTH(tra.posting_date) AS month, YEAR(tra.posting_date) AS year
            FROM ${db}.t_revenue_actuals tra
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tra.item_id
            WHERE tra.is_active = 1 AND tra.quote_position_id IN (?) AND tra.item_id  = ? AND tpi.latest_billing_posting_period >= tra.posting_date
            GROUP BY item_id,quote_position_id, month_year;`,[rate_card_id, project_item_id])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description getEmployeeAllocationStatusForPosition
 * @returns 
 */
module.exports.getEmployeeAllocationStatusForPosition = async(rate_card_id, project_item_id, date, db)=>{
    try{
        let result = await rpool(`SELECT rate_card_id, COUNT(rate_card_id) AS employee_count, 1 AS is_active_allocation
        FROM ${db}.t_internal_stakeholders
        WHERE start_date<=? AND end_date>=? AND rate_card_id IN (?) AND is_active = 1 AND item_id = ?
        GROUP BY rate_card_id
        UNION
        SELECT rate_card_id, COUNT(rate_card_id) AS employee_count, 0 AS is_active_allocation
        FROM ${db}.t_internal_stakeholders
        WHERE rate_card_id IN (?) AND is_active = 1 AND item_id =?
        GROUP BY rate_card_id;`,[date, date, rate_card_id, project_item_id, rate_card_id, project_item_id])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}
        
/**
 * @param {*} item_id 
 * @param {*} db 
 * @param {*} user 
 * @description Get Project Milestone Details
 * <AUTHOR> Rajendran
 * @version 1.0
 * @returns 
 */
module.exports.getProjectMilestoneDetails = async(item_id, db, user)=>{
    try{
        let database = mongo.client.db(db)

        let milestone_config_details = await database.collection("m_pm_form_customize").find({type:"milestone-landing", is_active: true}).toArray();

        if(milestone_config_details.length == 0)
            return Promise.resolve({ messType: "E", message: "Milestone configuration details not found" });

        let project_currency = await rpool(`
            SELECT MC.currency_code
            FROM ${db}.t_project_currency TPC
            INNER JOIN ${db}.m_currency MC ON TPC.currency1_id = MC.currency_id
            WHERE TPC.item_id = ? AND MC.is_active = 1
            `,[item_id]);
        
        if(project_currency && project_currency.length == 0)
            return Promise.resolve({ messType: "E", message: "Project currency not found" });

        let milestone_details_query = await rpool(`
            SELECT id,milestone_name AS NAME, milestone_type, milestone_status_id AS milestone_status,milestone_group,milestone_value,project_id, project_item_id
            FROM ${db}.t_milestones 
            WHERE project_item_id=? AND is_active = 1
            `,[item_id]);

        let project_order_value_query = await rpool(`
            SELECT item_value, service_type_id
            FROm ${db}.t_project_item
            WHERE id = ? AND is_active = 1
            `,[item_id]);

        let project_billing_advice_value = await rpool(`
            SELECT milestone_id, SUM(VALUE) AS consumed_value
            FROM ${db}.t_milestone_billing_advice_info
            WHERE project_item_id = ? GROUP BY milestone_id
            `,[item_id]);

        project_billing_advice_value  = project_billing_advice_value.length > 0 ? project_billing_advice_value : null;

        if( project_order_value_query && project_order_value_query.length == 0)
            return Promise.resolve({ messType: "E", message: "Project Currency details not found" });
        
        return Promise.resolve({ messType: "S", milestoneConfig: milestone_config_details, milestoneData: milestone_details_query, projectOrderValue: project_order_value_query, projectBillingAdviceValue: project_billing_advice_value, projectCurrency: project_currency[0]?.currency_code});
        
    }
    catch (err) {
        logger.info(err)
        return Promise.resolve({ messType: "E", error: err,message: "Error while featching milestone details" })
    }

}


/**
 * 
 * @param {*} db 
 * @description Get Milestone Matrix
 * @returns 
 */
module.exports.getMilestoneMatrix = async(db)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.m_project_milestone_matrix WHERE is_active = 1`);

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Milestone Matrix"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Lates
 * @returns 
 */
module.exports.getLatestBillingPostingPeriod = async(item_id, db)=>{
    try{
        let result = await rpool(`SELECT latest_billing_posting_period FROM  ${db}.t_project_item WHERE id = ?`,[item_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]['latest_billing_posting_period']})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"no Data found!"})
        }

    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Latest Billing Posting Period!"})
    }
}

/**
 * @description Get the Bills Data
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getBillData = async (project_id, item_id, db,sort, user, authorization) => {
    try {
      
        let result = await rpool(`SELECT tm.id, tm.milestone_name AS label,tm.planned_start_date AS start_date,tm.planned_end_date AS end_date,tm.milestone_status_id AS financial_status_id,tm.milestone_value AS currencyList,mps.name AS financial_status,mps.status_color,tm.invoice_date,tm.gantt_id,tm.action_type, tm.quote_id, tm.po_number, tm.po_date, tm.payment_terms, tm.milestone_type
              FROM ${db}.t_milestones tm
              INNER JOIN ${db}.m_project_status mps ON tm.milestone_status_id=mps.id
       WHERE tm.is_active=1 AND tm.project_item_id=? AND tm.project_id=? AND tm.is_bill = 1 AND mps.is_active=1  ORDER BY tm.planned_end_date ${sort} `, [item_id, project_id]);
        for (let items of result) {
            items['start_date'] = moment(items['start_date']).format('YYYY-MM-DD')
            items['end_date'] = moment(items['end_date']).format('YYYY-MM-DD')
            items['display_start_date'] = moment(items['start_date']).format('DD-MMM-YYYY')
            items['display_end_date'] = moment(items['end_date']).format('DD-MMM-YYYY')
            items['invoice_date'] = moment(items['invoice_date']).format('YYYY-MM-DD')
            let currencyList = items['currencyList'] != "" && items['currencyList'] != null ? (typeof items['currencyList'] == "string" ? JSON.parse(items['currencyList']) : items['currencyList']) : []
            items['currencyListJSON'] = currencyList;
        }
        return Promise.resolve({ messType: "S", data: result })
    }
    catch (err) {
        logger.info(err)
        return Promise.resolve({ messType: "E", message: "Error while getting Bill Data!" })
    }
}

/**
 * 
 * @param {*} milestone_id 
 * @param {*} invoice_date 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Update Invoice Date
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updateInvoiceDate = async(milestone_id, invoice_date,old_data,new_data, db, user, authorization)=>{
    try{
        let update = await pool(`UPDATE ${db}.t_milestones SET invoice_date =? WHERE id = ?`,[invoice_date, milestone_id])   
        return Promise.resolve({messType:"S", data: update,message:"Invoice Date has been Updated Succesfully"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Invoice Date"})
    }
}

/**
 * 
 * @param {*} financialDetails 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} milestone_id 
 * @param {*} value 
 * @param {*} type 
 * @param {*} db 
 * @param {*} user 
 * @description Insert Billing Advice User Logs
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertBillingAdviceUserLogs = async(financialDetails, project_id, project_item_id, milestone_id, value, db, user)=>{
    try{
        let database = mongo.client.db(db);

        let val = {
            logged_by: user.aid,
            logged_on: moment().format(),
            financialDetails: financialDetails,
            project_id: project_id,
            project_item_id: project_item_id,
            milestone_id: milestone_id,
            value: value
        }

        let result = await database.collection("t_project_billing_advice_user_logs").insertOne(val)

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting LOgs in billing Advice logs"})
    }
}

/**
 * 
 * @param {*} item_id 
 * @param {*} db 
 * @description Get Project Validate Value
 * @returns 
 */
module.exports.getProjectValidateValue = async(item_id, db)=>{
    try{
        let result = await rpool(`SELECT with_opportunity, item_value FROM ${db}.t_project_item tpi WHERE tpi.is_active = 1 AND id =?`,[item_id])

        return Promise.resolve({messType:"S", data: result})

    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Project Validate Values"})
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} db 
 * @description Get Project Milestone Data
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProjectMilestoneData = async(project_id, item_id, db)=>{
    try{
        let milestoneData = await rpool(`SELECT milestone_value, id, milestone_type, planned_start_date, planned_end_date FROM ${db}.t_milestones WHERE project_item_id=? AND is_active = 1 AND milestone_status_id!=6`,[item_id])

        return Promise.resolve(milestoneData)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
    
}

/**
 * 
 * @param {*} quote_id 
 * @param {*} item_id 
 * @param {*} db 
 * <AUTHOR> Raam Baskar
 * @description Get Quote Validate Value
 * @returns 
 */
module.exports.getQuoteValidateValue = async(quote_id, item_id, db)=>{
    try{
        let quoteValue = await rpool(`SELECT purchase_order_value FROM ${db}.t_project_financial_details WHERE project_item_id =? AND quote_id = ?`,[item_id, quote_id]);

        return Promise.resolve({messType:"S", data: quoteValue})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving the Quote value"})
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Billin gAdvice Billing Plan Data
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingAdviceBillingPlanData = async(project_id, item_id, quote_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT tpbph.project_item_id AS item_id, tpbp.planned_quantity AS planned_hours,  tpbp.month, tpbp.year, tpbpi.rate_card_id AS position_id
        FROM ${db}.t_project_billing_plan_header tpbph
        INNER JOIN ${db}.t_project_billing_plan_item tpbpi ON tpbpi.billing_plan_header_id = tpbph.id
        INNER JOIN ${db}.t_project_billing_plan tpbp ON tpbp.billing_plan_item_id = tpbpi.id
        WHERE 
        tpbph.id IN (SELECT MAX(id) FROM ${db}.t_project_billing_plan_header WHERE status_id = 2 AND quote_id = ? AND project_item_id =? GROUP BY project_item_id) `,[quote_id, item_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result})
        }
        else
        {
            return Promise.resolve({messType:"E", data:[], message:"Billing Plan not forecasted!"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Billing Advice Billing Plan Data!"})
    }
}


/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description insert bill values in tables
 * <AUTHOR>
 * @returns 
 */
module.exports.insertMilestone = async (milestone_data, db, user) => {
    try {
       
        let milestoneResult = await pool(
            `INSERT INTO ${db}.t_milestones (milestone_name, planned_start_date, planned_end_date, milestone_value, project_item_id,project_id, milestone_status_id, gantt_id,is_active,is_bill,action_type, milestone_type, po_number, po_date) VALUES (?, ?, ?, ?, ?, ?, ?, ? , 1, 1, ? ,?, ?, ?)`,
            [milestone_data.description, milestone_data.start_date, milestone_data.end_date, milestone_data.value, milestone_data.item_id, milestone_data.project_id, milestone_data.status_id, milestone_data.attachment_id, milestone_data.action_type, milestone_data.milestone_type, milestone_data.po_number, milestone_data.po_date]
        );
        
        let milestoneId = milestoneResult?.insertId;
        if ((milestone_data.status_id >= 8 && milestone_data.status_id < 15) && milestoneId) 
        {
            await pool(`INSERT INTO  ${db}.t_milestone_billing (milestone_id,invoice_value) VALUES(?,?)`,[milestoneId, milestone_data.value]);
        }

        return Promise.resolve({ messType: "S", message:"Milestone added successfully!",data: { milestoneId, milestone: milestoneResult } });
    } 
    catch (err) 
    {
        logger.info(err);
        await pool(`UPDATE ${db}.t_milestones SET is_active = 0 WHERE milestone_id = ?`, [milestoneId]);
        return Promise.resolve({ messType: "E", message: "Error while inserting milestone!", data: [] });
    }
};

/** 
 * @param {*} db
 * @param {*} project_id
 * @param {*} project_item_id
 * @param {*} quote_id
 * @description insert bill values in tables
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getBillingPlanHeaderIds = async (project_id, project_item_id, quote_id,db) => {
    try {
        let result = await rpool(`SELECT TPBH.id, TPBH.action_type
                                  FROM ${db}.t_project_billing_plan_header TPBH
                                  WHERE TPBH.quote_id = ? AND TPBH.project_item_id = ? AND TPBH.is_active = 1  ORDER BY TPBH.id DESC`,[quote_id,project_item_id]);
        return result;

    }catch(err){
        logger.info(err);
        return Promise.resolve({ messType: "E", message: "Error while fetching billing plan header ids" });
    }
}

/** 
 * @param {*} db
 * @param {*} project_id
 * @param {*} project_item_id
 * @param {*} quote_id
 * @description get billing plan details
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getBillingPlanData = async (project_item_id, quote_id, header_ids,db) => {
    try {
        let result = await rpool(`SELECT TPBH.id, TPBH.action_type, TPBI.rate_card_id,TPBP.billing_plan_item_id, TPBP.month, TPBP.year,TPBP.planned_quantity,TQBP.position_name
                                  FROM ${db}.t_project_billing_plan_header TPBH
                                  LEFT JOIN ${db}.t_project_billing_plan_item TPBI ON TPBI.billing_plan_header_id = TPBH.id AND TPBI.is_active = 1 
                                  LEFT JOIN ${db}.t_project_billing_plan TPBP ON TPBP.billing_plan_item_id = TPBI.id AND TPBP.is_active = 1 
                                  LEFT JOIN ${db}.t_qb_quote_position TQBP ON TQBP.quote_position_id = TPBI.rate_card_id
                                  WHERE TPBH.quote_id = ? AND TPBH.project_item_id = ? AND TPBH.id IN (?) AND TPBH.is_active = 1`,[quote_id,project_item_id,header_ids]);
        return result;

    }catch(err){
        logger.info(err);
        return Promise.resolve({ messType: "E", message: "Error while fetching billing plan header ids" });
    }
}



/**
 * 
 * @param {*} milestoneId 
 * @param {*} db 
 * @description Get RPA PRoject Invoice Details
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getRPAProjectInvoiceDetails = async(milestoneId, db)=>{
    try{
        let result = await rpool(`SELECT tmid.description, tmid.rate, tmid.unit, tmid.quantity, tmid.amount, mpbau.name AS unit_name 
            FROM ${db}.t_milestone_invoice_details tmid
            LEFT JOIN ${db}.m_project_billing_advice_uom mpbau ON mpbau.id = tmid.unit
            WHERE tmid.milestone_id =? AND tmid.is_active = 1`,[milestoneId]);
        
        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result})
        }
        else
        {
            return Promise.resolve({messType:"E", data:[]})
        }
        
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting RPA Project Invoice Details!"})
    }
}

/**
 * @description Fetching Position Remaining Log Data
 * @param {*} db 
 * @param {*} item_id 
 * @param {*} position_id 
 * @returns 
 */
module.exports.getPositionRemainingLogData = async(db,item_id,position_id,start,limit)=>{
    try{
        let result = await rpool(`SELECT CONCAT("Remaining(",ROUND(trp.remaining_allocated_hours,1),") = Budget(", ROUND(trp.budget_hours,1),") - [Actual till date(",ROUND(trp.actual_hours,1), ") + Future Allocated(", ROUND(trp.future_allocated_hours,1),")]") AS action_by,
            trp.remaining_allocated_hours,trp.position_id,trp.action_type,
            trp.budget_hours, trp.actual_hours, trp.future_allocated_hours,
            DATE_FORMAT(trp.action_on, '%d-%b-%Y') AS action_date
            FROM ${db}.t_rr_projection_action_log AS trp
            WHERE trp.project_id = ? AND trp.position_id = ? AND trp.position_type_id = 2 
            ORDER BY trp.id DESC LIMIT ?,?; `,[item_id, position_id,start,limit])

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Position Remaining Log Data!"})
    }
}

/**
 * 
 * @param {*} quote_id 
 * @param {*} item_id 
 * @param {*} db 
 * <AUTHOR> Rajendran
 * @description Get Quote Value
 * @returns 
 */
module.exports.getQuoteValueBasedOnQuoteId = async(quote_id, item_id, db)=>{
    try{
        let quoteValue = await rpool(`SELECT quote_id,purchase_order_value FROM ${db}.t_project_financial_details WHERE project_item_id =? AND quote_id IN (?)`,[item_id, quote_id]);

        return Promise.resolve({messType:"S", data: quoteValue})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving the Quote value"})
    }
}
/** 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} milestone_id 
 * @param {*} value 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @descritpion Update billing Value
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getUpdatedBillingValue = async(project_id, project_item_id, milestone_id, value, currency_code, date, db, user, authorization)=>{
    try{
        let milestone_value = await utilService.covertToRespectiveCurrency(project_id,project_item_id, value, currency_code , date, db) 

          value = milestone_value;

        return Promise.resolve(value)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve('[]')
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Check Advance Milestone Present 
 * @returns 
 */
module.exports.checkAdvancePresent= async(project_id, project_item_id, db, user, authorization)=>{
    try{
        let advanceMilestone = await rpool(`SELECT id FROM ${db}.t_milestones WHERE project_id =? AND project_item_id =? AND milestone_type = 1 AND milestone_status_id >=8 AND is_active =1`,[project_id, project_item_id]);

        if(advanceMilestone.length==0)
        {
            return Promise.resolve({messType:"E", message:"No advance milestones are present, or they are not in YTB, Payment Received, or Billed status.!"})
        }
        else
        {
            return Promise.resolve({messType:"S"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while validating Advance Milestone"})
    }
}

/**
 * 
 * @param {*} query 
 * @param {*} filterQueryParams 
 * @param {*} db 
 * @description Get General Planned Value Query
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getGeneralPlannedValueQuery = async(query, filterQueryParams, db)=>{
    try{
        let result = await rpool(query, filterQueryParams)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting General Planned Value"})
    }
}


/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description Revert milestone from Accrued to Reverse Accrued
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.moveMilestoneToReverseAccrued = async(milestone_id,db)=>{
    try{
       
        let check_milestone_type = await rpool(`SELECT milestone_type FROM ${db}.t_milestones WHERE id = ? AND is_active = 1 `,[milestone_id]);
        if(check_milestone_type && check_milestone_type.length > 0){
            check_milestone_type = check_milestone_type[0]
            if(check_milestone_type['milestone_type'] == 2){

                return Promise.resolve({messType:"E", message:'Accrued Actual Efforts cannot be reverted' })
            }
        }
        else{
            return Promise.resolve({messType:"E", error:check_milestone_type, message:'Error in Checking Milestone type' })
        }

        await pool(`UPDATE ${db}.t_milestones SET milestone_status_id=16 WHERE id=? AND is_active =1`,[milestone_id])
        await pool(`DELETE FROM ${db}.t_milestone_billing WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
        // await pool(`UPDATE ${db}.t_milestone_billing_advice_info SET is_active =0 WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
        return Promise.resolve({messType:"S"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Milestone Type List"})
    }
}

/**
 * 
 * @param {*} milestone_id 
 * @param {*} db 
 * @descripiton Get Particular Milestone Data
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getParticularMilestoneData = async(milestone_id, db)=>{
    try{
        let milestone_data = await rpool(`SELECT milestone_name, planned_start_date, planned_end_date, project_item_id, project_id, action_type, po_number, po_date, quote_id, milestone_type, invoice_date, milestone_group, is_bill FROM ${db}.t_milestones WHERE id = ? `,[milestone_id]);

        if(milestone_data.length>0)
        {
            return Promise.resolve({messType:"S", data: milestone_data[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"Error while getting aprticular Milestone Data"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Lates
 * @returns 
 */
module.exports.getMISPostingPeriod = async(db)=>{
    try{
        let result = await rpool(`SELECT date FROM  ${db}.m_posting_period_current WHERE id = 1`)

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]['date']})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"no Data found!"})
        }

    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Latest Billing Posting Period!"})
    }
}

/**
 * 
 * @param {*} temp 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Insert Partial Invoice Milestone
 * @returns 
 */
module.exports.insertPartialInvoiceMilestone = async(milestone_data, db, user, authorization)=>{
    try{

        let milestoneResult = await pool(
            `INSERT INTO ${db}.t_milestones (milestone_name, planned_start_date, planned_end_date, milestone_value, project_item_id,project_id, milestone_status_id, gantt_id, action_type, po_number, po_date, quote_id, milestone_type, invoice_date, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
            [milestone_data.description, milestone_data.start_date, milestone_data.end_date, milestone_data.value, milestone_data.item_id, milestone_data.project_id, milestone_data.status_id, milestone_data.gantt_id, milestone_data.action_type, milestone_data.po_number, milestone_data.po_date, milestone_data.quote_id, milestone_data.milestone_type, milestone_data.invoice_date]
        );
        
        let milestoneId = milestoneResult?.insertId;
        if ((milestone_data.status_id >= 8 && milestone_data.status_id < 15) && milestoneId) 
        {
            await pool(`INSERT INTO  ${db}.t_milestone_billing (milestone_id,invoice_value) VALUES(?,?)`,[milestoneId, milestone_data.value]);
        }

        return Promise.resolve({ messType: "S", message:"Milestone added successfully!",data: milestoneId });
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting Partial Invoice Milestone!"})
    }
}


/**
 * 
 * @param {*} parent_milestone_id 
 * @param {*} milestone_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Update Parent Milestone Id
 * @returns 
 */
module.exports.updateParentMilestoneId =async(milestone_group, milestone_id, db, user, authorization)=>{
    try{
        let update = await pool(`UPDATE ${db}.t_milestones SET milestone_group = ? WHERE id =?`,[JSON.stringify(milestone_group), milestone_id])

        return Promise.resolve({messType:"S", data: update})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Parent Id"})
    }
}


/**
 * 
 * @param {*} temp 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Insert Partial Invoice Milestone
 * @returns 
 */
module.exports.insertCreditNoteMilestone = async(milestone_data, db, user, authorization)=>{
    try{
        let is_bill = milestone_data?.is_bill ? milestone_data.is_bill : 0
        let milestoneResult = await pool(
            `INSERT INTO ${db}.t_milestones (milestone_name, planned_start_date, planned_end_date, milestone_value, project_item_id,project_id, milestone_status_id, gantt_id, action_type, po_number, po_date, quote_id, milestone_type, invoice_date,is_bill, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, 1)`,
            [milestone_data.description, milestone_data.start_date, milestone_data.end_date, milestone_data.value, milestone_data.item_id, milestone_data.project_id, milestone_data.status_id, milestone_data.gantt_id, milestone_data.action_type, milestone_data.po_number, milestone_data.po_date, milestone_data.quote_id, milestone_data.milestone_type, milestone_data.invoice_date,is_bill]
        );
        
        let milestoneId = milestoneResult?.insertId;
        if ((milestone_data.status_id >= 8 && milestone_data.status_id < 15) && milestoneId) 
        {
            await pool(`INSERT INTO  ${db}.t_milestone_billing (milestone_id,invoice_value) VALUES(?,?)`,[milestoneId, milestone_data.value]);
        }

        return Promise.resolve({ messType: "S", message:"Milestone added successfully!",data: milestoneId });
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting Partial Invoice Milestone!"})
    }
}

/**
 * 
 * @param {*} approval_type_id 
 * @param {*} db 
 * @descripiton Get workflow details
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getWorkflowDetails = async(approval_type_id, db)=>{
    try{
        let workflow_details = await rpool(`SELECT id,is_project_role_based, associate_id, project_role_id, workflow_master_id, workflow_level, workflow_group_id FROM ${db}.m_project_workflow_master WHERE workflow_group_id = ? AND is_active = 1 ORDER BY workflow_level ASC`,[approval_type_id]);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"Error while getting workflow details"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * @param {*} db 
 * @param {*} internal_role_ids 
 * @param {*} project_id
 * @param {*} item_id
 * @description get active internal stakeholders 
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.getActiveInternalStakeholders = async(db,internal_role_ids, project_id, item_id)=>{
    try{   
        let current_date=moment().format('YYYY-MM-DD');
        let result=await rpool(`SELECT tis.associate_id FROM ${db}.t_internal_stakeholders tis
        INNER JOIN ${db}.t_e360_employment_details teed ON teed.associate_id=tis.associate_id AND teed.start_date<=? AND teed.end_date>=? AND teed.employment_status = 1
        WHERE tis.project_id=? AND tis.item_id=? AND teed.is_active=1 AND tis.is_active=1 AND tis.project_role_id IN (?) AND tis.start_date <= ? AND tis.end_date >= ?`,[current_date,current_date,project_id,item_id,internal_role_ids,current_date,current_date])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getIsaData!"})
    }
}

/**
 * @param {*} db 
 * @param {*} external_role_ids 
 * @param {*} project_id
 * @param {*} item_id
 * @description get active external stakeholders 
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.getActiveExternalStakeholders = async(db,external_role_ids, project_id, item_id)=>{
    try{   
        let current_date=moment().format('YYYY-MM-DD');
        let result=await rpool(`SELECT tpes.associate_id
        FROM ${db}.t_project_external_stakeholders tpes
        INNER JOIN ${db}.t_e360_employment_details teed ON teed.associate_id=tpes.associate_id AND teed.start_date<= ? AND teed.end_date>= ?  AND teed.employment_status = 1
        WHERE tpes.project_id = ? AND tpes.project_item_id = ? AND teed.is_active=1 AND tpes.is_active=1 AND tpes.project_role_id IN (?)
        `,[current_date,current_date,project_id,item_id,external_role_ids])
     
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getIsaData!"})
    }
}

/**
 * 
 * @param {*} data 
 * @param {*} db 
 * @description Insert workflow details
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.addProjectsWorkflowDetails = async(data, db)=>{
    try{

        let result = await pool(`INSERT INTO ${db}.t_project_workflow(approval_type,project_id,project_item_id,workflow_header_id,source_id,approvers,old_data,new_data,submitted_by,is_active) VALUES (?)`,[data])
        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting costing sheet header details"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @param {*} item_id
 * @param {*} source_id
 * @descripiton Get workflow details based on source id and approval type
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getWorkflowDetailsBasedOnSource = async(db, project_id, item_id, source_id, appr_oid)=>{
    try{
        let workflow_details = await rpool(`
            SELECT MPW.workflow_name, MPW.source_name, TPW.source_id,TPW.old_data,TPW.new_data, TPW.submitted_by, TPW.created_on, GROUP_CONCAT(TWI.appr_oid) AS appr_oid_array, 
            MPW.primary_color, MPW.secondary_color,TPW.workflow_header_id, TPI.item_name, TM.is_bill
            FROM ${db}.t_project_workflow TPW
            LEFT JOIN ${db}.t_workflow_item TWI ON TPW.workflow_header_id = TWI.workflow_header_id AND TWI.is_active = 1 AND TWI.status = 'S'
            LEFT JOIN ${db}.m_project_workflow_master MPW ON MPW.id = TPW.approval_type AND MPW.is_active = 1
            LEFT JOIN ${db}.t_project_item TPI ON TPI.id = TPW.project_item_id AND TPW.project_id = TPI.project_id AND TPI.is_active = 1
            LEFT JOIN ${db}.t_milestones TM ON TM.id = TPW.source_id AND TM.is_active = 1            
            WHERE TPW.project_id = ? AND TPW.project_item_id = ?  AND TPW.source_id = ? AND TPW.is_active = 1 AND TWI.appr_oid = ?
            `,[project_id,item_id,source_id,appr_oid]);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details[0]})
        }
        else
        {
            return Promise.resolve({messType:"S", message:"Error while getting workflow details", data: null})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @param {*} item_id
 * @param {*} source_id
 * @descripiton deactivate workflow details based on source id and approval type
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.deactivateWorkflowDetailsBasedOnSource = async(db, project_id, item_id, source_id, approval_type)=>{
    try{
        let workflow_details = await pool(`
            UPDATE ${db}.t_project_workflow
            SET is_active = 0
            WHERE project_id = ? AND project_item_id = ?  AND source_id = ? AND is_active = 1 AND approval_type = ?
            `,[project_id,item_id,source_id,approval_type]);

            return Promise.resolve({messType:"S", messText: "Workflow Details Updated"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} workflow_header_id 
 * @descripiton Get workflow details based on workflow header id
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getWorkflowDetailsBasedOnWorkflowId = async(db, workflow_header_id)=>{
    try{
        let workflow_details = await rpool(`
            SELECT TPW.source_id, TPW.new_data, TPW.submitted_by, TPW.created_on, TPW.workflow_header_id, TPW.approval_type, 
            TPW.project_id, TPW.project_item_id, TPW.old_data, TPW.new_data, MPW.workflow_name AS description, 
            MPW.workflow_group_id, MPW.workflow_level, TPW.approvers, TWI.status
            FROM ${db}.t_project_workflow TPW
            LEFT JOIN ${db}.m_project_workflow_master MPW ON MPW.id = TPW.approval_type AND MPW.is_active = 1
            LEFT JOIN ${db}.t_workflow_item TWI ON TWI.workflow_header_id = TPW.workflow_header_id AND TWI.is_active = 1
            WHERE TPW.workflow_header_id = ?
            `,[workflow_header_id]);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"Error while getting workflow details"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description revert milestone from ytb
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.revertMilestoneFromAccrual = async(milestone_id,db)=>{
    try{

        let check_milestone_type = await rpool(`SELECT milestone_type FROM ${db}.t_milestones WHERE id = ? AND is_active = 1 `,[milestone_id]);
        if(check_milestone_type && check_milestone_type.length > 0){
            check_milestone_type = check_milestone_type[0]
            if(check_milestone_type['milestone_type'] == 2){

                return Promise.resolve({messType:"E", message:'The requested functionality is not applicable for this operation.' })
            }
        }
        else{
            return Promise.resolve({messType:"E", error:check_milestone_type, message:'Error in Checking Milestone type' })
        }

        await pool(`UPDATE ${db}.t_milestones SET milestone_status_id=4 WHERE id=? AND is_active =1`,[milestone_id])
        await pool(`DELETE FROM ${db}.t_milestone_billing WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
        // await pool(`UPDATE ${db}.t_milestone_billing_advice_info SET is_active =0 WHERE milestone_id = ? AND is_active = 1`,[milestone_id])
        return Promise.resolve({messType:"S"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve milestone from Accrual"})
    }
}

/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Update PO Master Remaining
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updatePOMasterRemaining = async(project_id, project_item_id, currencyCode, db, user, authorization)=>{
    try{
        let update = await pool(`UPDATE ${db}.t_project_po_master_details SET remaining = (
            JSON_EXTRACT(po_value, 
            CONCAT((SELECT JSON_UNQUOTE(JSON_SEARCH (po_value ->>'$[*].currency_code','one',?))), '.value')) - consumed) 
            WHERE project_id =? AND project_item_id = ? AND is_active = 1`,[currencyCode, project_id, project_item_id])

        return Promise.resolve({messType:"S", update: update})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating PO Master Remaining!"})
    }
}

/**
 * @param {*} query 
 * @param {*} filterQueryParams 
 * @param {*} db 
 * @description Get General Planned Value Query
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getGeneralPlannedValueQuery = async(query, filterQueryParams, db)=>{
    try{
        let result = await rpool(query, filterQueryParams)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting General Planned Value"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @descripiton Get workflow details for projects
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectWorkflowDetails = async(db, search_params, sort_query, filter_query,skip, limit, user)=>{
    try{
        let workflow_details_query = mysql.format(`
            SELECT MPW.source_name AS source_type, TPW.source_id,  MPW.workflow_name AS description, TPI.item_name AS project_name,
            TPW.submitted_by AS submitted_by_aid, TPW.created_on AS submitted_on, TWI.workflow_header_id, MPW.detail_view_type AS type, MPW.primary_color, MPW.secondary_color, 
            TPW.project_id, TPW.project_item_id,RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(TEPD.first_name,' '), ''),COALESCE(CONCAT(TEPD.middle_name,' '), ''),COALESCE(TEPD.last_name, '')))) AS submitted_by, TM.is_bill
            FROM ${db}.t_workflow_item TWI
            LEFT JOIN ${db}.t_workflow_header TWH ON TWI.workflow_header_id = TWH.id AND TWH.is_active = 1
            LEFT JOIN ${db}.m_workflow MW ON MW.id = TWH.workflow_id AND MW.is_active = 1
            INNER JOIN ${db}.t_project_workflow TPW ON TPW.workflow_header_id = TWI.workflow_header_id AND TPW.is_active = 1 
            LEFT JOIN ${db}.m_project_workflow_master MPW ON MPW.id = TPW.approval_type AND MPW.is_active = 1
            LEFT JOIN ${db}.t_project_item TPI ON TPI.id = TPW.project_item_id AND TPW.project_id = TPI.project_id AND TPI.is_active = 1
            LEFT JOIN ${db}.t_e360_personal_details TEPD ON TEPD.associate_id = TPW.submitted_by AND TEPD.end_date >= ? AND TEPD.start_date <= ?
            LEFT JOIN ${db}.t_milestones TM ON TM.id = TPW.source_id AND TM.is_active = 1
             WHERE TWI.is_active = 1 AND TWI.appr_oid = ? AND TWI.status = 'S' AND TWH.is_workflow_complete = 0 AND MW.application_id = 915
            `,
            [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD"), user.oid])

        if(search_params && search_params !== ""){
            workflow_details_query += `AND(MPW.source_name LIKE '%${search_params}%' OR TPW.source_id LIKE '%${search_params}%' OR MPW.workflow_name LIKE '%${search_params}%' OR TPI.item_name LIKE '%${search_params}%' OR TEPD.first_name LIKE '%${search_params}%' OR TEPD.middle_name LIKE '%${search_params}%' OR TEPD.last_name LIKE '%${search_params}%')`
        }

        if(filter_query && filter_query !== ""){
            workflow_details_query += filter_query
        }

        if(sort_query && sort_query !== ""){
                workflow_details_query += sort_query
        }

        workflow_details_query += ` LIMIT ${limit} OFFSET ${skip}`

        let workflow_details = await rpool (workflow_details_query);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details})
        }
        else
        {
            return Promise.resolve({messType:"S", data: []})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} user
 * @descripiton Get workflow details for projects count
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectWorkflowDetailsCount = async(db, search_params, filter_query, user)=>{
    try{
        let workflow_details_query = mysql.format(`
            SELECT COUNT(*) AS count
            FROM ${db}.t_workflow_item TWI
            LEFT JOIN ${db}.t_workflow_header TWH ON TWI.workflow_header_id = TWH.id AND TWH.is_active = 1
            LEFT JOIN ${db}.m_workflow MW ON MW.id = TWH.workflow_id AND MW.is_active = 1
            INNER JOIN ${db}.t_project_workflow TPW ON TPW.workflow_header_id = TWI.workflow_header_id AND TPW.is_active = 1 
            LEFT JOIN ${db}.m_project_workflow_master MPW ON MPW.id = TPW.approval_type AND MPW.is_active = 1
            LEFT JOIN ${db}.t_project_item TPI ON TPI.id = TPW.project_item_id AND TPW.project_id = TPI.project_id AND TPI.is_active = 1
            LEFT JOIN ${db}.t_e360_personal_details TEPD ON TEPD.associate_id = TPW.submitted_by AND TEPD.end_date >= ? AND TEPD.start_date <= ?
             WHERE TWI.is_active = 1 AND TWI.appr_oid = ? AND TWI.status = 'S' AND TWH.is_workflow_complete = 0 AND MW.application_id = 915
            `,
            [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD"), user.oid])

        if(search_params && search_params !== ""){
            workflow_details_query += `AND(MPW.source_name LIKE '%${search_params}%' OR TPW.source_id LIKE '%${search_params}%' OR MPW.workflow_name LIKE '%${search_params}%' OR TPI.item_name LIKE '%${search_params}%' OR TEPD.first_name LIKE '%${search_params}%' OR TEPD.middle_name LIKE '%${search_params}%' OR TEPD.last_name LIKE '%${search_params}%')`
        }

        if(filter_query && filter_query !== ""){
            workflow_details_query += filter_query
        }

        let workflow_details = await rpool (workflow_details_query);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details[0]?.count ? workflow_details[0]?.count : 0})
        }
        else
        {
            return Promise.resolve({messType:"E", err: true})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}


module.exports.getPeopleAllocationWorkflowDetailsCount = async(db, search_params, filter_query, user)=>{
    try{
        // Debug: Log the user.oid to check what value is being passed
        logger.info(`Debug - getPeopleAllocationWorkflowDetailsCount - user.oid: ${user.oid}, db: ${db}`);

        let workflow_details_query = mysql.format(`
            SELECT COUNT(*) AS count
FROM (
  SELECT trmar.id
  FROM ${db}.t_rm_request trmr
  JOIN ${db}.t_rm_allocated_resources trmar 
    ON trmr.request_id = trmar.request_id AND trmar.is_active = 1
  JOIN ${db}.t_workflow_header twh 
    ON trmar.workflow_id = twh.id AND twh.is_active = 1
  JOIN ${db}.t_workflow_item twi 
    ON twh.id = twi.workflow_header_id AND twi.is_active = 1
  JOIN ${db}.m_workflow mw 
    ON mw.id = twh.workflow_id AND mw.is_active = 1
  WHERE twi.appr_oid = ? 
    AND twi.status = 'S' 
    AND twi.is_workflow_item_complete = 0 
    AND twh.is_workflow_complete = 0 
    AND mw.application_id = 460
) AS filtered_allocations
            `,
            [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD"), user.oid])

        // Debug: Log the complete query
        logger.info(`Debug - getPeopleAllocationWorkflowDetailsCount - Query: ${workflow_details_query}`);

        if(search_params && search_params !== ""){
            workflow_details_query += ` AND (trmr.request_id LIKE '%${search_params}%' OR TEPD.first_name LIKE '%${search_params}%' OR TEPD.middle_name LIKE '%${search_params}%' OR TEPD.last_name LIKE '%${search_params}%')`
        }

        if(filter_query && filter_query !== ""){
            workflow_details_query += filter_query
        }

        let workflow_details = await rpool (workflow_details_query);

        // Debug: Log the result
        logger.info(`Debug - getPeopleAllocationWorkflowDetailsCount - Result: ${JSON.stringify(workflow_details)}`);

        if(workflow_details.length>0)
        {
            return Promise.resolve({messType:"S", data: workflow_details[0]?.count ? workflow_details[0]?.count : 0})
        }
        else
        {
            return Promise.resolve({messType:"E", err: true})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting People Allocation Workflow Count!"})
    }
}

module.exports.getPeopleAllocationWorkflowDetails = async (db, search_params, sort_query, filter_query, skip, limit, user) => {
    try {
        let base_query = `
            WITH ranked_requests AS (
                SELECT
                    trmr.request_id as source_id,
                    'People Allocation' AS source_type,
                    trmr.requested_by AS submitted_by_aid,
                    trmr.created_on AS submitted_on,
                    twi.workflow_header_id,
                    'people_allocation' AS TYPE,
                    '#4CAF50' AS primary_color,
                    '#E8F5E8' AS secondary_color,
                    twi.id AS workflow_item_id,
                    trmar.resource_aid AS employee_allocated,
                    trmar.resource_aid AS associate_id,
                    trmr.project_id AS project_item_id,
                    tpi.project_id,
                    tpi.item_name AS project_name,
                    trmar.allocated_by,
                    trmar.utilization_capacity,
                    mem.employee_name,
                    
                    ROW_NUMBER() OVER (
                        PARTITION BY trmr.request_id 
                        ORDER BY twi.id DESC
                    ) AS rn
                FROM ${db}.t_rm_request trmr
                JOIN ${db}.t_rm_allocated_resources trmar 
                    ON trmr.request_id = trmar.request_id 
                    AND trmar.is_active = 1
                JOIN ${db}.t_workflow_header twh 
                    ON trmar.workflow_id = twh.id 
                    AND twh.is_active = 1
                JOIN ${db}.t_workflow_item twi 
                    ON twh.id = twi.workflow_header_id 
                    AND twi.is_active = 1
                JOIN ${db}.m_workflow mw 
                    ON mw.id = twh.workflow_id 
                    AND mw.is_active = 1
                JOIN ${db}.m_employee_master mem
                    ON mem.associate_id = trmar.resource_aid
                JOIN ${db}.t_project_item tpi 
                    ON tpi.id = trmr.project_id 
                    AND tpi.is_active = 1
                WHERE 
                    twi.appr_oid = ?
                    AND twi.status = 'S'
                    AND twi.is_workflow_item_complete = 0
                    AND twh.is_workflow_complete = 0
                    AND mw.application_id = 460
        `;

        // Add search condition inside CTE
        if (search_params && search_params.trim() !== "") {
            base_query += `
                AND (
                    CAST(trmr.request_id AS CHAR) LIKE '%${search_params}%'
                    OR mem.employee_name LIKE '%${search_params}%'
                )
            `;
        }

        base_query += `)
            SELECT * FROM ranked_requests 
        `;

        // Add filter
        if (filter_query && filter_query.trim() !== "") {
            base_query += ` ${filter_query}`;
        }

        // Add sorting
        if (typeof sort_query === 'string') {
            sort_query = sort_query.trim();
        } else {
            sort_query = ''; // or apply a default value
        }


        // Pagination
        if (skip !== undefined && limit !== undefined) {
            base_query += ` LIMIT ${skip}, ${limit}`;
        }

        // Pass user.aid as the bind parameter for `?`
        const workflow_details = await rpool(base_query, [user.aid]);

        if (workflow_details.length > 0) {
            return { messType: "S", data: workflow_details };
        } else {
            return { messType: "S", data: [] };
        }

    } catch (err) {
        logger.info(err);
        return { messType: "E", error: err, message: "Error while getting People Allocation Workflow Details!" };
    }
};

/**
 * @description Update People Allocation Workflow Request
 * @param {string} db - Database name
 * @param {string} approval_type - 'A' for approve, 'R' for reject
 * @param {number} workflow_header_id - Workflow header ID
 * @param {object} comments - Comments object
 * @param {object} user - User object
 * @param {string} authorization - Authorization header
 * <AUTHOR> Rajendran
 * @returns {Promise<object>}
 */
module.exports.updateInboxPeopleAllocationWorkflowRequest = async (db, approval_type, workflow_header_id, comments, user, authorization) => {
  try {
    if (!workflow_header_id || !approval_type) {
      return { messType: 'E', messText: "Workflow Details Not Found" };
    }

    // Get the workflow item along with the related allocation details
    const workflowDetailsResult = await rpool(
      `SELECT
        twi.*,
        trmr.request_id,
        trmar.resource_aid as associate_id,
        trmar.utilization_capacity as allocated_hours,
        trmar.is_head,
        trmar.reports_to
       FROM ${db}.t_workflow_item twi
       JOIN ${db}.t_workflow_header twh ON twi.workflow_header_id = twh.id AND twh.is_active = 1
       JOIN ${db}.t_rm_allocated_resources trmar ON trmar.workflow_id = twh.id AND trmar.is_active = 1
       JOIN ${db}.t_rm_request trmr ON trmr.request_id = trmar.request_id AND trmr.is_active = 1
       WHERE twi.workflow_header_id = ? AND twi.appr_oid = ? AND twi.is_active = 1 AND twi.status = 'S'
       LIMIT 1`,
      [workflow_header_id, user.oid]
    );

    if (!workflowDetailsResult || workflowDetailsResult.length === 0) {
      return { messType: 'E', messText: "No active workflow item found for approval." };
    }

    const workflowDetails = workflowDetailsResult[0];

    if (['A', 'R'].includes(workflowDetails.status)) {
      return {
        messType: 'W',
        messText: `Request is already ${workflowDetails.status === 'A' ? 'approved' : 'rejected'}`
      };
    }

    // Prepare the request parameters according to the backend expectation
    const requestParams = {
      request_id: workflowDetails.request_id,
      action_type: approval_type,
      workflow_id: workflow_header_id,
      reject_reason: approval_type === 'R' ? (comments?.reason || 'Rejected') : null,
      associate_id: workflowDetails.associate_id,
      allocated_hours: workflowDetails.allocated_hours || 0,
      is_head: workflowDetails.is_head || 0,
      reports_to: workflowDetails.reports_to || null
    };

    // Use the axios service to make the API call
    const pv2AxiosService = require('../services/pv2AxiosService');
    const result = await pv2AxiosService.updateInboxPeopleAllocationWorkflowRequest(requestParams, db, authorization);

    return result;

  } catch (err) {
    logger.info("Approval Error:", err?.response?.data || err.message);
    return {
      messType: "E",
      messText: "Error while processing the request",
      error: err?.response?.data || err.message
    };
  }
};





/**
 * 
 * @param {*} db 
 * @param {*} user
 * @descripiton Get workflow details for projects count
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.checkOpportunityBlanketPO = async(db, project_id, item_id,quote_id, user, authorization)=>{
    try{
        let opportunity_details = await rpool(`
            SELECT TCO.blanket_po
            FROM ${db}.t_project_financial_details TPFD
            LEFT JOIN ${db}.t_c4c_opportunity TCO ON TCO.opportunity_id = TPFD.opportunity_id AND TCO.is_active = 1 
            WHERE TPFD.project_id = ? AND TPFD.project_item_id = ? AND TPFD.quote_id = ? AND TPFD.is_active = 1
            `,
            [project_id, item_id, quote_id]);

        if(opportunity_details.length>0)
        {
            return Promise.resolve({messType:"S", data: opportunity_details[0]?.blanket_po ? true : false})
        }
        else
        {
            return Promise.resolve({messType:"E", err: true})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Particular Milestone Data!"})
    }
}


/**
 * 
 * @param {*} project_id 
 * @param {*} item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Fixed Billing Plan Quote Details
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getFixedBillingPlanQuoteDetails = async(project_id, item_id, quote_id, db, user, authorization)=>{
    try{
        let query_params = [item_id]
        let quote_query = quote_id ? `AND quote_id = ?` : ''

        if(quote_query!='')
        {
            query_params.push(quote_id)
        }



        let billingPlanResult = await rpool(`SELECT tpbph.id AS billing_plan_header_id, tpbpd.name AS deliverable_name, tpbpd.planned_quantity AS deliverable_planned_quantity,
        tpbph.project_id, tpbph.project_item_id, tpbph.quote_id, tpbph.deliverable_id, tpbph.resource_type_id, tpbph.status_id, tpbph.action_type,
        mc.currency_code AS currency,  tpbpd.milestone_id, tpbpd.line_item_ids
        FROM ${db}.t_project_billing_plan_header tpbph
        INNER JOIN ${db}.t_project_billing_plan_deliverable tpbpd ON tpbph.deliverable_id = tpbpd.id
        INNER JOIN ${db}.t_project_currency tpc ON tpc.item_id = tpbph.project_item_id
        INNER JOIN ${db}.m_currency mc ON mc.currency_id = tpc.currency1_id
        WHERE tpbph.is_active =1 AND tpbpd.is_active = 1 
        AND tpbph.id IN (SELECT MAX(id) FROM ${db}.t_project_billing_plan_header WHERE project_item_id =? `+ quote_query+`  GROUP BY deliverable_id, project_item_id)
        GROUP BY tpbph.deliverable_id`,query_params)

        return Promise.resolve(billingPlanResult)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * @param {*} project_item_id 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Billing Plan Milestones
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanMilestones = async(project_item_id, quote_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id, planned_end_date, MONTH(planned_end_date) AS milestone_month, YEAR(planned_end_date) AS milestone_year 
        FROM ${db}.t_milestones 
        WHERE milestone_type IN (4, 2) AND quote_id = ? AND project_item_id = ?  
        AND is_active= 1 AND milestone_status_id NOT IN (6)
        AND milestone_status_id IN (8, 9, 10, 11, 15)`,[quote_id, project_item_id])

        return Promise.resolve(result)


    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Update PO Master Remaining
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updatePOMasterRemaining = async(project_id, project_item_id, currencyCode, db, user, authorization)=>{
    try{
        let update = await pool(`UPDATE ${db}.t_project_po_master_details SET remaining = (
            JSON_EXTRACT(po_value, 
            CONCAT((SELECT JSON_UNQUOTE(JSON_SEARCH (po_value ->>'$[*].currency_code','one',?))), '.value')) - consumed) 
            WHERE project_id =? AND project_item_id = ? AND is_active = 1`,[currencyCode, project_id, project_item_id])

        return Promise.resolve({messType:"S", update: update})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating PO Master Remaining!"})
    }
}


/**
 * 
 * @param {*} data 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Insert Fixed Billing Plan Deliverable
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertFixedBillingPlanDeliverable = async(data, db, user, authorization)=>{
    try{
        let insert = await pool(`INSERT INTO ${db}.t_project_billing_plan_deliverable (name, milestone_id, start_date, end_date, line_item_ids, planned_quantity) VALUES (?,?,?,?,?,?)`,data);

        return Promise.resolve({messType:"S", data: insert.insertId})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err,message:"Error while inserting Fixed Billing Plan Deliverable!"})
    }
}

/**
 * @param {*} query 
 * @param {*} filterQueryParams 
 * @param {*} db 
 * @description Get General Planned Value Query
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getGeneralPlannedValueQuery = async(query, filterQueryParams, db)=>{
    try{
        let result = await rpool(query, filterQueryParams)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting General Planned Value"})
    }
}



/**
 * 
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Billing Plan Version History
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getFixedBillingPlanVersionHistory = async(project_id, project_item_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT tpbph.*, tpbpd.name AS deliverable_name, tpbpd.milestone_id FROM ${db}.t_project_billing_plan_header tpbph
            INNER JOIN ${db}.t_project_billing_plan_deliverable tpbpd ON tpbph.deliverable_id = tpbpd.id
            WHERE project_id = ? AND project_item_id = ? AND tpbph.is_active = 1 AND tpbpd.is_active = 1
            ORDER BY id DESC`,[project_id, project_item_id])

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Version History!"})
    }
}


/**
 * 
 * @param {*} quote_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * <AUTHOR> Raam Baskar
 * @description Get Quote Position Deliverable
 * @returns 
 */
module.exports.getQuotePositionDeliverable = async(id, db, user, authorization)=>{
    try{
        let result=await rpool(`SELECT ROUND(SUM(tqqpe.hours * tqqp.resource_count), 2) AS original_balance,
            tqqp.resource_type_id AS resource_type,
            tqqm.milestone_name, tqqm.milestone_id, 
            tqqm.start_date AS milestone_start_date, tqqm.end_date AS milestone_end_date, tqqm.revenue, tqqh.quote_currency AS currency
            FROM ${db}.t_qb_quote_header tqqh
            INNER JOIN ${db}.t_qb_quote_service tqqs ON tqqs.quote_header_id = tqqh.quote_header_id
            INNER JOIN ${db}.t_qb_quote_position tqqp ON tqqp.quote_service_id=tqqs.quote_service_id AND tqqs.is_active=1
            INNER JOIN ${db}.m_role mr ON mr.id=tqqp.position
            INNER JOIN ${db}.m_qb_uom AS mqu ON mqu.id = tqqp.unit_id
            INNER JOIN ${db}.t_qb_quote_milestone tqqm ON tqqm.milestone_id = tqqp.milestone_id
            LEFT JOIN ${db}.t_qb_quote_position_effort tqqpe ON tqqpe.quote_position_id = tqqp.quote_position_id AND tqqpe.quote_id = tqqs.quote_header_id AND tqqpe.is_active = 1
            WHERE tqqs.quote_header_id IN (?) AND tqqp.is_active=1 AND mr.is_active=1  AND tqqm.is_active = 1
            GROUP BY tqqm.milestone_id`,[id])

    
        
 
        return {messType:"S", data: result}
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Quote Position Deliverable"})
    }
}

/**
 * 
 * @param {*} deliverable_id 
 * @param {*} line_item_ids 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Save Attach Dependencies
 * @returns 
 */
module.exports.saveAttachDependencies = async(deliverable_id, line_item_ids, db, user,authorization) =>{
    try{
        let update = await pool(`UPDATE ${db}.t_project_billing_plan_deliverable SET line_item_ids = ? WHERE id =?`,[JSON.stringify(line_item_ids), deliverable_id])

        return Promise.resolve({messType:"S", result: update})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while saving attach dependencies"})
    }
}

/*
 * @param {*} project_id 
 * @param {*} project_item_id 
 * @param {*} milestone_array 
 * @param {*} db 
 * @description Get Billing Advice Data
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.getBillingAdviceData = async(project_id, project_item_id, milestone_array, db)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.t_milestone_billing_advice_info WHERE project_id = ? AND project_item_id = ? AND milestone_id IN (?) AND is_active = 1`,[project_id, project_item_id, milestone_array])

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while getting Billing Plan Version History!"})
    }
}


/**
 * 
 * @param {*} resourceType 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Reosurce Type Config
 * @returns 
 */
module.exports.getResourceTypeConfig = async(resourceType, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.m_billing_plan_resource_type WHERE quote_resource_type IN (?) AND is_active = 1`,[resourceType]);

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} deliverable_id 
 * @param {*} db 
 * @description Get Deliverable Milestone
 * @returns 
 */
module.exports.getDeliverableMilestone = async(deliverable_id, db)=>{
    try{
        let result = await pool(`SELECT * FROM ${db}.t_project_billing_plan_deliverable WHERE id = ?`,[deliverable_id]);

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}



/**
 * 
 * @param {*} unitType 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Unit Type Config
 * @returns 
 */
module.exports.getUnitTypeConfig = async(unitType, db, user, authorization)=>{
    try{    
        let result = await rpool(`SELECT * FROM ${db}.m_billing_plan_uom WHERE quote_unit IN (?)`,[unitType]);

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} project_item_id 
 * @param {*} quote_id 
 * @param {*} milestone_type_id 
 * @param {*} rate_card_ids 
 * @description Get Non Man power cost/ License Actual Data
 * @param {*} db 
 * @returns 
 */
module.exports.getNPMilestoneList = async(project_item_id, quote_id, milestone_type_id, rate_card_ids, db)=>{
    try{
        let result = await rpool(`SELECT MONTH(tm.planned_end_date) AS month, YEAR(tm.planned_end_date) AS year, tmbai.rate_card_id, tm.project_item_id AS item_id, SUM(tmbai.actual_billable_hours) AS actual_hours, DATE_FORMAT(tm.planned_end_date,"%m-%Y") AS month_year, tmbai.rate_card_id AS quote_position_id
                FROM ${db}.t_milestones tm
                INNER JOIN ${db}.t_milestone_billing_advice_info tmbai ON tmbai.milestone_id = tm.id 
                WHERE tm.is_active = 1 AND tmbai.is_active = 1 AND tm.quote_id = ? AND tm.project_item_id = ? AND tm.milestone_type = ? AND tmbai.rate_card_id IN (?) AND tm.milestone_status_id IN (8,9, 10, 11, 15)
                GROUP BY tmbai.rate_card_id, month_year, tm.project_item_id`,[quote_id, project_item_id, milestone_type_id, rate_card_ids])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}


/**
 * 
 * @param {*} resourceType 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Reosurce Type Config
 * @returns 
 */
module.exports.getResourceTypeConfigId = async(resourceType, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT * FROM ${db}.m_billing_plan_resource_type WHERE id IN (?) AND is_active = 1`,[resourceType]);

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])

    }
}


/**
 * 
 * @param {*} item_id 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description Get Future Planned Billing Plan
 * @returns 
 */
module.exports.getFuturePlannedBillingPlan = async(item_id, deliverable_id, quote_id, rate_card_id, milestone_end_date, db)=>{
    try
    {
        let query =""
        let queryParams = [ item_id]
        if(deliverable_id){
            query += " AND deliverable_id =?"
            queryParams.push(deliverable_id)
        }

        queryParams=[...queryParams,...[quote_id, milestone_end_date, rate_card_id]]

        let result = mysql.format(`SELECT SUM(tpbp.planned_quantity) AS planned_hours,  tpbpi.rate_card_id
            FROM ${db}.t_project_billing_plan_header tpbph
            INNER JOIN ${db}.t_project_billing_plan_item tpbpi ON tpbpi.billing_plan_header_id = tpbph.id
            INNER JOIN ${db}.t_project_billing_plan tpbp ON tpbp.billing_plan_item_id = tpbpi.id
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tpbph.project_item_id
            WHERE 
            tpbph.id IN (SELECT MAX(id) FROM ${db}.t_project_billing_plan_header WHERE status_id = 2 AND project_item_id = ? `+query+` AND quote_id =? GROUP BY project_item_id) 
            AND ? <= DATE(CONCAT(tpbp.year,"-",tpbp.month,"-" ,1))
            AND tpbpi.rate_card_id IN (?)
            GROUP BY tpbpi.rate_card_id`,queryParams)

        logger.info(result)

        result = await rpool(result)

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}


/**
 * 
 * @param {*} item_id 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description Get Total Planned Billing Plan
 * @returns 
 */
module.exports.getTotalPlannedBillingPlan = async(item_id, deliverable_id, quote_id, rate_card_id, db)=>{
    try
    {
        let query =""
        let queryParams = [ item_id]
        if(deliverable_id){
            query += " AND deliverable_id =?"
            queryParams.push(deliverable_id)
        }

        queryParams=[...queryParams,...[quote_id, rate_card_id]]

        let result = await rpool(`SELECT SUM(tpbp.planned_quantity) AS planned_hours,  tpbpi.rate_card_id
            FROM ${db}.t_project_billing_plan_header tpbph
            INNER JOIN ${db}.t_project_billing_plan_item tpbpi ON tpbpi.billing_plan_header_id = tpbph.id
            INNER JOIN ${db}.t_project_billing_plan tpbp ON tpbp.billing_plan_item_id = tpbpi.id
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tpbph.project_item_id
            WHERE 
            tpbph.id IN (SELECT MAX(id) FROM ${db}.t_project_billing_plan_header WHERE status_id = 2 AND project_item_id = ? `+query+` AND quote_id = ? GROUP BY project_item_id) 
            AND  tpbpi.rate_card_id IN (?)
            GROUP BY tpbpi.rate_card_id`,queryParams)

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} item_id 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description Get Current Revenue Amoutn
 * @returns 
 */
module.exports.getCurrentRevenueAmount = async(item_id, rate_card_id, db)=>{
    try{
        let result = await rpool(`SELECT (SUM(tra.credit)  - SUM(tra.debit) ) AS current_revenue, tra.quote_position_id
            FROM ${db}.t_revenue_actuals AS tra
            INNER JOIN ${db}.t_qb_quote_position AS qbp ON qbp.quote_position_id = tra.quote_position_id
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tra.item_id
            WHERE tra.is_active = 1 AND  tra.posting_date <= tpi.latest_billing_posting_period AND qbp.quote_position_id  IN (?) AND tra.item_id = ?
            GROUP BY qbp.quote_position_id`,[rate_card_id, item_id])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}
/**
 * 
 * @param {*} db
 * @param {*} milestone_id
 * @description revert milestone from ytb
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProjectItemIdBasedOnMilestone = async(milestone_id,db)=>{
    try{
        let data = await rpool(`SELECT project_item_id FROM ${db}.t_milestones WHERE id = ?`, [milestone_id])

        return  Promise.resolve(data[0]?.project_item_id ? data[0]?.project_item_id : null)
    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while retrieve projects details"})
    }
}


/**
 * 
 * @param {*} temp 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Insert WriteOff Milestone
 * @returns 
 */
module.exports.insertWriteOffMilestone = async(milestone_data, db, user, authorization)=>{
    try{

        let milestoneResult = await pool(
            `INSERT INTO ${db}.t_milestones (milestone_name, planned_start_date, planned_end_date, milestone_value, project_item_id,project_id, milestone_status_id, gantt_id, action_type, po_number, po_date, quote_id, milestone_type, invoice_date, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
            [milestone_data.description, milestone_data.start_date, milestone_data.end_date, milestone_data.value, milestone_data.item_id, milestone_data.project_id, milestone_data.status_id, milestone_data.gantt_id, milestone_data.action_type, milestone_data.po_number, milestone_data.po_date, milestone_data.quote_id, milestone_data.milestone_type, milestone_data.invoice_date]
        );
        
        let milestoneId = milestoneResult?.insertId;
        if ((milestone_data.status_id >= 8 && milestone_data.status_id < 15) && milestoneId) 
        {
            await pool(`INSERT INTO  ${db}.t_milestone_billing (milestone_id,invoice_value) VALUES(?,?)`,[milestoneId, milestone_data.value]);
        }

        return Promise.resolve({ messType: "S", message:"Milestone added successfully!",data: milestoneId });
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting Partial Invoice Milestone!"})
    }
}

/**
 * 
 * @param {*} milestone_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @deescription Get Previous Milestone Duration
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getPreviousMilestoneDuration = async(milestone_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT planned_start_date, planned_end_date, project_item_id, quote_id FROM ${db}.t_milestones WHERE id = ?`,[milestone_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No Previous Milestone found for the duration!"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Previous Milestone Duration"})
    }
}


/**
 * 
 * @param {*} month 
 * @param {*} year 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Milestone of the Month & Year
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMilestoneofMonthYear = async(month, year, project_item_id, quote_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id, milestone_status_id, planned_start_date, planned_end_date FROM ${db}.t_milestones WHERE YEAR(planned_end_date) = ? AND MONTH(planned_end_date)=? AND milestone_type = 4 AND is_active = 1 AND milestone_status_id !=6 AND project_item_id =?`,[year, month, project_item_id, quote_id]);

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No previous milestone found!"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Milestone for the Previous Month"})
    }
}

/**
 * 
 * @param {*} item_id 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description Get Current Month Planned Billing Plan
 * @returns 
 */
module.exports.getCurrentMonthPlannedBillingPlan = async(item_id, deliverable_id, quote_id, rate_card_id, milestone_end_date, db)=>{
    try
    {
        let date = moment(milestone_end_date).startOf("month").format("YYYY-MM-DD");

        let query =""
        let queryParams = [ item_id]
        if(deliverable_id){
            query += " AND deliverable_id =?"
            queryParams.push(deliverable_id)
        }

        queryParams=[...queryParams,...[quote_id, date, rate_card_id]]

        let result = mysql.format(`SELECT SUM(tpbp.planned_quantity) AS planned_hours,  tpbpi.rate_card_id
            FROM ${db}.t_project_billing_plan_header tpbph
            INNER JOIN ${db}.t_project_billing_plan_item tpbpi ON tpbpi.billing_plan_header_id = tpbph.id
            INNER JOIN ${db}.t_project_billing_plan tpbp ON tpbp.billing_plan_item_id = tpbpi.id
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tpbph.project_item_id
            WHERE 
            tpbph.id IN (SELECT MAX(id) FROM ${db}.t_project_billing_plan_header WHERE status_id = 2 AND project_item_id = ? `+query+` AND quote_id =? GROUP BY project_item_id) 
            AND ? = DATE(CONCAT(tpbp.year,"-",tpbp.month,"-" ,1))
            AND tpbpi.rate_card_id IN (?)
            GROUP BY tpbpi.rate_card_id`,queryParams)

        logger.info(result)

        result = await rpool(result)

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])

    }
}

/**
 * @param {*} milestone_id
 * @param {*} project_item_id
 * @param {*} db 
 * @description Get Milestone details for matrxi config comparision
 * @returns 
 */
module.exports.getMilestoneDetailsForMatrix = async(milestone_id, project_item_id, db)=>{
    try{
        let result = await rpool(`SELECT tm.id, tm.milestone_type, tm.project_item_id, tpi.service_type_id, tm.milestone_status_id, tm.is_bill, 
                                tm.milestone_value, tpc.currency1_id, mc.currency_code
                                FROM ${db}.t_milestones tm
                                LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tm.project_item_id AND tpi.is_active = 1
                                LEFT JOIN ${db}.t_project_currency tpc ON tm.project_item_id = tpc.item_id AND tm.project_id = tpc.project_id
                                LEFT JOIN ${db}.m_currency mc ON mc.currency_id = tpc.currency1_id
                                WHERE tm.id = ? AND tm.project_item_id = ?`,[milestone_id, project_item_id])

        return Promise.resolve(result && result.length > 0 ? result[0] : null)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Milestone Matrix"})
    }
}

/**
 * 
 * @param {*} project_id
 * @param {*} project_item_id
 * @param {*} milestone_id
 * @param {*} db 
 * @description Get biling advice sum for the milestone
 * @returns 
 */
module.exports.getBillingadviceValue = async(project_id, project_item_id, milestone_id, db)=>{
    try{
        let result = await rpool(`SELECT SUM(value) AS value
        FROM ${db}.t_milestone_billing_advice_info
        WHERE project_id = ? AND  project_item_id = ? AND milestone_id = ?  AND is_active = 1`,[project_id,project_item_id,milestone_id])

        return Promise.resolve(result && result.length > 0 && result[0]?.value ?  result[0].value : null)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting billing advice value"})
    }
}

/**
 * 
 * @param {*} rate_card_id
 * @param {*} db 
 * @description Get Billing Plan Actual Details
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getBillingPlanActualDetailsPreviousMonth = async(project_item_id, rate_card_id, milestone_end_date, db)=>{
    try{
        let date = moment(milestone_end_date).startOf('month').format("YYYY-MM-DD")
        let result = await rpool(`SELECT tra.item_id,tra.quote_position_id,DATE_FORMAT(tra.posting_date,"%m-%Y") AS month_year,tra.posting_date,
            SUM(tra.no_of_billable_actual_hours) AS actual_hours, SUM(tra.reporting_currency_1_value) AS reporting_currency_1_value, 
            SUM(tra.reporting_currency_2_value) AS reporting_currency_2_value, MONTH(tra.posting_date) AS month, YEAR(tra.posting_date) AS year
            FROM ${db}.t_revenue_actuals tra
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tra.item_id
            WHERE tra.is_active = 1 AND tra.quote_position_id IN (?) AND tra.item_id  = ? AND tpi.latest_billing_posting_period >= tra.posting_date AND tra.posting_date <= ?
            GROUP BY item_id,quote_position_id, month_year;`,[rate_card_id, project_item_id, date])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} rate_card_id 
 * @param {*} db 
 * @description Get Previous Month Quote Position Data
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getPreviousMonthQuotePositionData = async(rate_card_id, db)=>{
    try{
        let baselineQuotePosition = await rpool(`SELECT DISTINCT tqqp.quote_position_id AS quote_position_id,ROUND(SUM(tqqpe.hours) * tqqp.resource_count, 2) AS original_balance,
            tqqp.rate_per_unit AS per_hour_rate, 0 AS revenue_till_date, 0 AS actual_till_date
            FROM ${db}.t_qb_quote_service tqqs
            INNER JOIN ${db}.t_qb_quote_position tqqp ON tqqp.quote_service_id=tqqs.quote_service_id AND tqqs.is_active=1
            LEFT JOIN ${db}.t_qb_quote_position_effort tqqpe ON tqqpe.quote_position_id = tqqp.quote_position_id AND tqqpe.quote_id = tqqs.quote_header_id AND tqqpe.is_active = 1
            WHERE  tqqp.is_active=1 AND tqqp.quote_position_id IN (?)
            GROUP BY tqqp.quote_position_id`,[rate_card_id])

        let actualQuotePosition = await rpool(`SELECT rate_card_id AS quote_position_id, revised_per_hour_rate AS per_hour_rate,total_quote_position_hours AS original_balance, revenue_till_date, actual_till_date
            FROM ${db}.t_milestone_billing_advice_position tmbap
            WHERE  tmbap.is_active =1 
            AND tmbap.id IN (
            SELECT MAX(tmbap1.id) 
            FROM ${db}.t_milestone_billing_advice_position tmbap1
            INNER JOIN ${db}.t_milestones tm ON tm.id = tmbap1.milestone_id
            WHERE tmbap1.rate_card_id = tmbap.rate_card_id 
            AND tm.milestone_status_id IN (8,9,10,11,15) AND tmbap1.is_active = 1 
            AND tm.is_active = 1 AND tmbap1.rate_card_id IN (?))`,[rate_card_id])
        
        
        let outputData = [];

        for(let baseline of baselineQuotePosition)
        {
            let actual = _.where(actualQuotePosition,{quote_position_id: baseline['quote_position_id']})

            if(actual.length>0)
            {
                outputData.push(actual[0])
            }
            else
            {
                outputData.push(baseline)
            }


        }

        return Promise.resolve(outputData)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * @param {*} milestone_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Milestone Meta Information
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMilestoneMetaInformation = async(milestone_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT tm.milestone_status_id, tm.milestone_type, tpi.service_type_id
            FROM ${db}.t_milestones tm
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tm.project_item_id WHERE tm.id =?`,[milestone_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No milestone found with the ID"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Milestone Meta Information!"})
    }
}

/**
 * 
 * @param {*} currency_from 
 * @param {*} currency_to 
 * @description Get Latest Currency Conversion Rates
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getLatestCurrencyConversionRates = async(currency_from, currency_to)=>{
    try{
        let result = await rpool(`SELECT currency_from, currency_to, conversion_rate FROM kebs_master.t_currency_conversion WHERE conversion_id IN 
        (SELECT MAX(conversion_id) FROM  kebs_master.t_currency_conversion 
        WHERE currency_from IN (?) AND currency_to IN (?)  AND conversion_type_id = 3
        GROUP BY currency_from, currency_to)`,[currency_from, currency_to])
  
        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
  }
  
  /**
   * 
   * @param {*} db 
   * @description Get Financial Data Reporting Currencies
   * <AUTHOR> Raam Baskar
   * @returns 
   */
  module.exports.getFinancialDataReportingCurrencies = async(db)=>{
    try{
        let result = await rpool(`SELECT tenant_db_name , reporting_currencies_info FROM kebs_master.m_kebs_tenant_info WHERE tenant_db_name = ?;`,[db]);
  
        if(result.length>0)
        {
            let reporting_currencies = result[0]['reporting_currencies_info']!="null" && result[0]['reporting_currencies_info']!="" && result[0]['reporting_currencies_info']!=null ? typeof result[0]['reporting_currencies_info']=="string" ? JSON.parse(result[0]['reporting_currencies_info']) : result[0]['reporting_currencies_info']: []
  
            if(reporting_currencies.length>0)
            {
                let currencies = _.uniq(_.pluck(reporting_currencies,"currency_code"))
  
                return Promise.resolve(currencies.length> 0 ? currencies : ["USD", "INR"])
            }
            else
            {
                return Promise.resolve(["USD", "INR"])
            }
        }
        else
        {
            return Promise.resolve(["USD", "INR"])
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve(["USD", "INR"])
    }
  }

/**
 * @param {*} milestone_id 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Milestone Meta Information
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMilestoneMetaInformation = async(milestone_id, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT tm.milestone_status_id, tm.milestone_type, tpi.service_type_id
            FROM ${db}.t_milestones tm
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tm.project_item_id WHERE tm.id =?`,[milestone_id])

        if(result.length>0)
        {
            return Promise.resolve({messType:"S", data: result[0]})
        }
        else
        {
            return Promise.resolve({messType:"E", message:"No milestone found with the ID"})
        }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Milestone Meta Information!"})
    }
}

module.exports.getLicenseBillingPlanActualDetails = async(project_item_id, rate_card_id, db)=>{
    try{
        let result = await rpool(`SELECT tra.item_id,tra.quote_position_id,DATE_FORMAT(tra.posting_date,"%m-%Y") AS month_year,tra.posting_date,
            SUM(tra.no_of_billable_actual_hours) AS actual_hours, SUM(tra.reporting_currency_1_value) AS reporting_currency_1_value, 
            SUM(tra.reporting_currency_2_value) AS reporting_currency_2_value, MONTH(tra.posting_date) AS month, YEAR(tra.posting_date) AS year
            FROM ${db}.t_revenue_actuals tra
            INNER JOIN ${db}.t_project_item tpi ON tpi.id = tra.item_id
            WHERE tra.is_active = 1 AND tra.resource_type=3 AND tra.quote_position_id IN (?) AND tra.item_id  = ? AND tpi.latest_billing_posting_period >= tra.posting_date
            GROUP BY item_id,quote_position_id, month_year;`,[rate_card_id, project_item_id])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}
