(window.webpackJsonp=window.webpackJsonp||[]).push([[871,761],{"2Pgj":function(e,t,n){"use strict";var i;n.d(t,"a",(function(){return a}));var o=new Uint8Array(16);function a(){if(!i&&!(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(o)}},"7Cbv":function(e,t,n){"use strict";var i=n("2Pgj"),o=n("WM9j");t.a=function(e,t,n){var a=(e=e||{}).random||(e.rng||i.a)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){n=n||0;for(var s=0;s<16;++s)t[n+s]=a[s];return t}return Object(o.a)(a)}},BuRe:function(e,t,n){"use strict";var i=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;t.a=function(e){return"string"==typeof e&&i.test(e)}},H44p:function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var i=n("xG9w"),o=n("fXoL"),a=n("flaP"),s=n("ofXK"),r=n("Qu3c"),l=n("NFeN");function c(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",9),o["\u0275\u0275elementStart"](1,"div",10),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div"),o["\u0275\u0275elementStart"](5,"p",11),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"p",12),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.label),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",13),o["\u0275\u0275elementStart"](1,"span"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",14),o["\u0275\u0275elementStart"](1,"span",15),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",16),o["\u0275\u0275elementStart"](1,"span",15),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function m(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",17),o["\u0275\u0275elementStart"](1,"span",18),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-icon",19),o["\u0275\u0275text"](1,"loop"),o["\u0275\u0275elementEnd"]())}function g(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",1),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().change()})),o["\u0275\u0275template"](1,c,9,4,"div",2),o["\u0275\u0275template"](2,u,3,2,"div",3),o["\u0275\u0275template"](3,d,3,3,"div",4),o["\u0275\u0275template"](4,p,3,3,"div",5),o["\u0275\u0275template"](5,m,3,3,"div",6),o["\u0275\u0275elementStart"](6,"div",7),o["\u0275\u0275template"](7,h,2,0,"mat-icon",8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","big"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","small"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","medium"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","large"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","overview"==e.type),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",e.toDisplay)}}let v=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&o["\u0275\u0275template"](0,g,8,6,"div",0),2&e&&o["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,r.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},OQIA:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("ofXK"),o=n("fXoL");let a=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule]]}),e})()},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("xG9w"),o=n("fXoL");let a=(()=>{class e{transform(e,t,n){let o=i.findWhere(t,{field_name:e,type:n});return!!o&&!!o.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},TroI:function(e,t,n){"use strict";n.r(t),n.d(t,"QuoteModule",(function(){return Xo}));var i=n("ofXK"),o=n("bTqV"),a=n("NFeN"),s=n("Qu3c"),r=n("kmnG"),l=n("bSwM"),c=n("qFsG"),u=n("3Pt+"),d=n("iadO"),p=n("0IaG"),m=n("STbY"),h=n("5+WD"),g=n("rDax"),v=n("tyNb"),f=n("mrSG"),y=n("1yaQ"),_=n("FKr1"),b=n("+rOU"),C=n("XNiG"),x=n("quSY"),E=n("PqYM"),S=n("1G5W"),w=n("Kj3r"),q=n("/uUt"),D=n("6eBy"),O=n("R0Ic"),I=n("wd/R"),P=n.n(I),M=n("7Cbv"),k=n("fXoL"),F=n("jr6c"),T=n("1A3m"),A=n("0BBf"),R=n("JqCM"),Q=n("LcQX"),L=n("pgif"),j=n("LRne"),N=n("JIr8"),V=n("xG9w"),U=n("tk/3"),$=n("flaP");let G=(()=>{class e{constructor(e,t){this._http=e,this.roleService=t,this._onDestroy=new C.b,this.quotesList$=new C.b,this.quotesList=this.quotesList$.asObservable().pipe(Object(S.a)(this._onDestroy)),this.isQuoteLoading=!1}flagQuote(e){return Object(f.c)(this,void 0,void 0,(function*(){let t,n;if(e!=this.currentFlaggedQuote){for(let i of this._quotesList)e==i.quote_header_id&&(i.flag=!0,t=i),this.currentFlaggedQuote==i.quote_header_id&&(i.flag=!1,n=i);this.currentFlaggedQuote=e,this._http.post("api/qb/quote/setQuoteFlag",{add:t.quote_header_id,remove:n.quote_header_id}).pipe(Object(N.a)(e=>Object(j.a)({messType:"E"}))).subscribe(e=>{"E"==e.messType&&(t.flag=!t.flag,this.currentFlaggedQuote=n.quote_header_id)}),this._emitQuoteList()}}))}getQuoteDetails(e,t=!1){return new Promise((n,i)=>{this.isQuoteLoading=!0,this._http.post("/api/qb/quote/getQuotesList",{opportunity_id:e,showDeleted:t}).pipe(Object(N.a)(e=>(this.isQuoteLoading=!1,console.log(e),i(e),Object(j.a)({messText:"Couldn't get quote, try again later!",messType:"E"})))).subscribe(e=>{this._quotesList=e.data||[],this._quotesList.forEach(e=>{e.value=e.quote_value?JSON.parse(e.quote_value):[],e.flag&&(this.currentFlaggedQuote=e.quote_header_id)}),this._quotesListForSearch=this._quotesList,this._emitQuoteList(),this.isQuoteLoading=!1,n(!0)})})}_emitQuoteList(){this.quotesList$.next(this._quotesList)}destroy(){this._onDestroy.next(),this._onDestroy.complete()}deleteQuoteFromList(e){this._quotesList=this._quotesList.filter(t=>t.quote_header_id!=e),this._emitQuoteList()}updateQuoteActive(e,t,n){return this._http.post("/api/qb/quote/updateQuoteActive",{opportunityId:e,quoteId:t,isActive:n})}updateChangeRequestQuoteActive(e,t,n){return this._http.post("/api/qb/quote/updateChangeRequestQuoteActive",{opportunityId:e,quoteId:t,isActive:n})}updateActiveFlagInList(e,t=0){for(const n of this._quotesList)n.flag=n.quote_header_id===e?t:0;this._emitQuoteList()}updateChangeRequestFlagInList(e,t=0){for(const n of this._quotesList)n.change_request_flag=n.quote_header_id===e?t:0;this._emitQuoteList()}clearQuoteList(){this._quotesList=[],this._emitQuoteList()}searchQuoteList(e=null){if(e){const t=new RegExp(e,"i");this._quotesList=this._quotesListForSearch.filter(e=>t.test(e.quote_name)||t.test(e.last_modified_by_name)||t.test(e.quote_header_id))}else this._quotesList=this._quotesListForSearch;this._emitQuoteList()}checkCRActivateAccess(){return V.where(this.roleService.roles,{application_id:36,object_id:29404,operation:"*"}).length>0}checkQuoteActivateAccess(){return V.where(this.roleService.roles,{application_id:36,object_id:29412,operation:"*"}).length>0}checkCREnabled(){return V.where(this.roleService.roles,{application_id:36,object_id:29415,operation:"*"}).length>0}getQuoteApprovers(e){return this._http.post("/api/qb/quote/getQuoteApprovers",{details:e})}getQuoteStatus(e){return this._http.post("/api/qb/quote/getReviewerStatus",{details:e})}submitForQuoteApproval(e){return this._http.post("/api/qb/quote/submitForQuoteApproval",{details:e})}approveOrReject(e){return this._http.post("/api/qb/quote/updateApprovalWorkFlow",{details:e})}checkQuoteEditableBasedOnConfig(e){return this._http.post("/api/qb/quote/checkQuoteEditableBasedOnConfig",{quote_header_id:e})}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275inject"](U.c),k["\u0275\u0275inject"]($.a))},e.\u0275prov=k["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var B=n("yu80"),z=n("TU8p"),W=n("c9BX"),H=n("Xa2L"),X=n("TmG/"),Y=n("me71"),K=n("mS9j"),J=n("vBGi");const Z=["sectionTemplateRef"],ee=["resourceCostRef"],te=["revSummaryRef"],ne=["quoteNameRef"],ie=["approveOrReject"],oe=["submitForApproval"],ae=["scrollFrame"],se=function(e){return{shakeDetailQuote:e}};function re(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",41),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]();return t.openApproverDialog(t.wholeQuoteData)})),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngStyle",e.getStatusCss(null==e.wholeQuoteData?null:e.wholeQuoteData.quote_status))("ngClass",k["\u0275\u0275pureFunction1"](4,se,(null==e.wholeQuoteData?null:e.wholeQuoteData.isApprover)&&1===(null==e.wholeQuoteData?null:e.wholeQuoteData.quote_status)))("matTooltip",null!=e.wholeQuoteData&&e.wholeQuoteData.isApprover&&1===(null==e.wholeQuoteData?null:e.wholeQuoteData.quote_status)?"Click to Approve/Reject Quote Activation":"Click to check the status of the approval"),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.wholeQuoteData.status_name," ")}}function le(e,t){1&e&&k["\u0275\u0275element"](0,"span",42)}function ce(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span",43),k["\u0275\u0275text"](1,"Change Request"),k["\u0275\u0275elementEnd"]())}function ue(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span",43),k["\u0275\u0275text"](1,"(Active)"),k["\u0275\u0275elementEnd"]())}function de(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-label"),k["\u0275\u0275text"](1,"DD MMM YYYY"),k["\u0275\u0275elementEnd"]())}function pe(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"mat-datepicker-toggle",44),k["\u0275\u0275elementStart"](1,"mat-icon",45),k["\u0275\u0275text"](2,"edit_calendar"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](31);k["\u0275\u0275property"]("for",e)}}function me(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-label"),k["\u0275\u0275text"](1,"DD MMM YYYY"),k["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"mat-datepicker-toggle",44),k["\u0275\u0275elementStart"](1,"mat-icon",45),k["\u0275\u0275text"](2,"edit_calendar"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](40);k["\u0275\u0275property"]("for",e)}}function ge(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",46),k["\u0275\u0275text"](1,"expand_more"),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](48);k["\u0275\u0275property"]("matMenuTriggerFor",e)}}function ve(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",47),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"]().selectCurrency(n)})),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function fe(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",65),k["\u0275\u0275elementStart"](1,"div",19),k["\u0275\u0275text"](2,"Total Order Value"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",66),k["\u0275\u0275element"](4,"input",67),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function ye(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",68),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).openMilestoneDialog()})),k["\u0275\u0275namespaceSVG"](),k["\u0275\u0275elementStart"](1,"svg",69),k["\u0275\u0275element"](2,"path",70),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275property"]("disabled",e.isDataLoading)("matTooltip",(null==e.quoteMilestoneTagging?null:e.quoteMilestoneTagging.toolTip)||"Tag Milestone")}}function _e(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",71),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).cancelChanges()})),k["\u0275\u0275text"](1," Cancel "),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275property"]("disabled",e.isDataLoading)}}function be(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275text"](1),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.isEditMode?"Save":"Edit")}}function Ce(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",72),k["\u0275\u0275element"](1,"mat-spinner",73),k["\u0275\u0275elementEnd"]()),2&e&&(k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("diameter",20))}function xe(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",74),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).openActivityLog()})),k["\u0275\u0275elementStart"](1,"span",63),k["\u0275\u0275text"](2,"Activity Log"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Ee(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",75),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).deleteQuote()})),k["\u0275\u0275elementStart"](1,"span",63),k["\u0275\u0275text"](2,"Delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Se(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",16),k["\u0275\u0275elementStart"](1,"div",48),k["\u0275\u0275template"](2,fe,5,2,"div",49),k["\u0275\u0275elementStart"](3,"div",50),k["\u0275\u0275template"](4,ye,3,2,"button",51),k["\u0275\u0275template"](5,_e,2,1,"button",52),k["\u0275\u0275elementStart"](6,"button",53),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().saveQuote()})),k["\u0275\u0275template"](7,be,2,1,"ng-container",54),k["\u0275\u0275template"](8,Ce,2,1,"ng-template",null,55,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"button",56,57),k["\u0275\u0275elementStart"](12,"mat-icon",58),k["\u0275\u0275text"](13,"more_vert"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"mat-menu",59,60),k["\u0275\u0275template"](16,xe,3,0,"button",61),k["\u0275\u0275elementStart"](17,"button",62),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().openCustomizeDialog()})),k["\u0275\u0275elementStart"](18,"span",63),k["\u0275\u0275text"](19,"Customize Fields"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](20,Ee,3,0,"button",64),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275reference"](9),t=k["\u0275\u0275reference"](15),n=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",n.checkFieldShouldbeRestricted("totalRevenue")),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",(null==n.quoteMilestoneTagging?null:n.quoteMilestoneTagging.enable)&&n.milestoneApplicable),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!n.isQuoteCreateMode&&n.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("disabled",n.isDataBeingSaved||n.isDataLoading||!n.isCUDEnabled||n.quoteForm.get("activeChangeRequest").value||1===(null==n.wholeQuoteData?null:n.wholeQuoteData.quote_status)||n.editRestricted)("matTooltip",1===(null==n.wholeQuoteData?null:n.wholeQuoteData.quote_status)?"Cannot Edit while under review":n.quoteForm.get("activeChangeRequest").value?"Cannot edit active change request":n.editRestricted?n.editRestrictedMsg?n.editRestrictedMsg:"Edit not allowed":n.isEditMode?n.isChangesMade?"Changes made Kindly Save":"Save Quote":"Edit Quote")("ngClass",n.isEditMode?"save-btn":"preview-btn")("matBadgeHidden",!(n.isEditMode&&!n.isQuoteCreateMode&&n.isChangesMade)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!n.isDataBeingSaved)("ngIfElse",e),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("matMenuTriggerFor",t),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("ngIf",n.quoteId),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",n.quoteId&&n.isCUDEnabled)}}function we(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"button",97),k["\u0275\u0275elementStart"](1,"mat-icon",58),k["\u0275\u0275text"](2,"drag_indicator"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function qe(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",98),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](2).deleteService(t)})),k["\u0275\u0275elementStart"](1,"mat-icon",99),k["\u0275\u0275text"](2,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function De(e,t){}function Oe(e,t){}function Ie(e,t){if(1&e&&k["\u0275\u0275template"](0,Oe,0,0,"ng-template",102),2&e){k["\u0275\u0275nextContext"](6);const e=k["\u0275\u0275reference"](56);k["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Pe(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275text"](1),k["\u0275\u0275template"](2,Ie,1,1,void 0,21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," label-class p-0"),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.label," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isMandatory)}}function Me(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,Pe,3,5,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"!=(null==e?null:e.fieldType))}}function ke(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," label-class d-flex"),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function Fe(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,ke,2,4,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"==(null==e?null:e.fieldType))}}function Te(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",100),k["\u0275\u0275elementStart"](1,"div",101),k["\u0275\u0275text"](2),k["\u0275\u0275template"](3,De,0,0,"ng-template",102),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](4,Me,2,1,"ng-container",103),k["\u0275\u0275template"](5,Fe,2,1,"ng-container",103),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3),t=k["\u0275\u0275reference"](56);k["\u0275\u0275property"]("ngClass",e.isEditMode?"":"pt-3"),k["\u0275\u0275advance"](1),k["\u0275\u0275classMapInterpolate1"]("col-",e.positionField.col," label-class"),k["\u0275\u0275property"]("ngClass",e.isEditMode?"d-flex justify-content-center":""),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.positionField.label," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngTemplateOutlet",t),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.customizeFields),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.customizeFields)}}function Ae(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"button",97),k["\u0275\u0275elementStart"](1,"mat-icon",58),k["\u0275\u0275text"](2,"drag_indicator"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function Re(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," explore "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Non Man Power")}function Qe(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," workspace_premium "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","License")}function Le(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," person "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Man Power")}function je(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",118),2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("matTooltip",e.get("positionName").value)("list",e.get("isNonManpower").value?t.nmpList:e.get("isLicense").value?t.licenseList:t.skillList)("hideMatLabel",!0)("placeholder","Search "+t.positionField.label)("required",!0)("hasNoneOption",!1)("disabled",!e.get("isPositionFieldEnabled").value)}}function Ne(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",119),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275property"]("matTooltip",e.get("positionName").value),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("positionName").value)}}function Ve(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",121),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);let i=null,o=null;k["\u0275\u0275property"]("required",e.isMandatory)("hasNoneOption",!e.isMandatory)("list",e.isMasterDataFromPosition?t.get(e.key+"MasterData").value:"unit"==e.key?null!=t&&null!=(i=t.get("isLicense"))&&i.value?n.licenseUnit:null!=t&&null!=(i=t.get("isNonManpower"))&&i.value?n.nonManPowerUnit:n.manPowerUnit:e.masterData)("hideMatLabel",!0)("placeholder","Search "+e.label)("formControlName",e.key)("disabled",!((null==e||!e.enableVariableName||null!=(o=t.get(null==e?null:e.enableVariableName))&&o.value)&&(n.wEMappingEnabled||n.sDMappingEnabled?"entity"==e.key?n.wEMappingEnabled:"division"!=e.key||n.sDMappingEnabled:"division"==e.key?t.get("entity").value:"subDivision"!=e.key||t.get("entity").value&&t.get("division").value)))}}function Ue(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275pipe"](3,"displayValue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);let i=null;k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind4"](3,1,null==(i=t.get(null==e?null:e.key))?null:i.value,"unit"==(null==e?null:e.key)?null!=t&&null!=(i=t.get("isLicense"))&&i.value?n.licenseUnit:null!=t&&null!=(i=t.get("isNonManpower"))&&i.value?n.nonManPowerUnit:n.manPowerUnit:null==e?null:e.masterData,"name","id")," ")}}function $e(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,Ve,1,7,"app-input-search",120),k["\u0275\u0275template"](2,Ue,4,6,"ng-container",21),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",(null==e||!e.isForManpowerOnly||!t.get("isNonManpower").value&&!t.get("isLicense").value)&&n.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!(null!=e&&e.isForManpowerOnly&&(t.get("isNonManpower").value||t.get("isLicense").value)||n.isEditMode))}}function Ge(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",127),k["\u0275\u0275text"](1),k["\u0275\u0275pipe"](2,"displayValue"),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](5).$implicit,t=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind4"](2,1,e.get("unit").value,t.unitList,"unit_suffix","id")," ")}}function Be(e,t){}function ze(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",127),k["\u0275\u0275template"](1,Be,0,0,"ng-template",102),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"](8);const e=k["\u0275\u0275reference"](58);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngTemplateOutlet",e)}}function We(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"mat-form-field",124),k["\u0275\u0275element"](1,"input",125),k["\u0275\u0275template"](2,Ge,3,6,"span",126),k["\u0275\u0275template"](3,ze,2,1,"span",126),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275styleProp"]("opacity",t.get(null==e?null:e.enableVariableName).value?1:.5)("pointer-events",t.get(null==e?null:e.enableVariableName).value?"auto":"none"),k["\u0275\u0275advance"](1),k["\u0275\u0275propertyInterpolate"]("placeholder",e.label),k["\u0275\u0275property"]("formControlName",e.key)("decimalPart",null==e?null:e.decimalPart)("currency",n.quoteForm.get("quoteCurrency").value)("allowNegative",e.allowNegative),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","quantity"==e.key&&!t.get("isLicense").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","ratePerUnit"==e.key||"costPerUnit"==e.key)}}function He(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275element"](2,"input",128),k["\u0275\u0275pipe"](3,"displayValue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("formControlName",e.key)("currency",n.quoteForm.get("quoteCurrency").value)("showSuffix","noOfResources"!=e.key)("decimalPart",null==e?null:e.decimalPart)("suffix","quantity"!=e.key||t.get("isLicense").value?"":k["\u0275\u0275pipeBind4"](3,5,t.get("unit").value,n.unitList,"unit_suffix","id"))}}function Xe(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](7);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Ye(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,We,4,11,"mat-form-field",123),k["\u0275\u0275template"](2,He,4,10,"ng-container",21),k["\u0275\u0275template"](3,Xe,3,1,"ng-container",21),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);let i=null,o=null;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!n.isEditMode&&!n.checkFieldShouldbeMasked(e.key,null!=(i=t.get("isNonManpower"))&&i.value?2:null!=(i=t.get("isLicense"))&&i.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.checkFieldShouldbeMasked(e.key,null!=(o=t.get("isNonManpower"))&&o.value?2:null!=(o=t.get("isLicense"))&&o.value?3:1))}}function Ke(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275template"](1,$e,3,2,"ng-container",21),k["\u0275\u0275template"](2,Ye,4,3,"ng-container",21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," p-0"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","dropdown"===e.fieldType),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","number"===e.fieldType)}}function Je(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,Ke,3,5,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"!=e.fieldType)}}function Ze(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275element"](1,"input",129),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"](4);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formControlName",e.key)("currency",t.quoteForm.get("quoteCurrency").value)}}function et(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275element"](1,"input",130),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formControlName",e.key)}}function tt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](6);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function nt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275template"](1,Ze,2,2,"ng-container",21),k["\u0275\u0275template"](2,et,2,1,"ng-container",21),k["\u0275\u0275template"](3,tt,3,1,"ng-container",21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);let i=null,o=null,a=null;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," pr-0 revenue-value"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","display"===e.fieldType&&"quotePositionId"!=e.key&&!n.checkFieldShouldbeMasked(e.key,null!=(i=t.get("isNonManpower"))&&i.value?2:null!=(i=t.get("isLicense"))&&i.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","display"===e.fieldType&&"quotePositionId"==e.key&&!n.checkFieldShouldbeMasked(e.key,null!=(o=t.get("isNonManpower"))&&o.value?2:null!=(o=t.get("isLicense"))&&o.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.checkFieldShouldbeMasked(e.key,null!=(a=t.get("isNonManpower"))&&a.value?2:null!=(a=t.get("isLicense"))&&a.value?3:1))}}function it(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,nt,4,6,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"==e.fieldType)}}function ot(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",131),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index,n=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](2).addPosition(n,t,"positions")})),k["\u0275\u0275elementStart"](1,"mat-icon",132),k["\u0275\u0275text"](2,"add"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function at(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",133),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index,n=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](2).deletePosition(n,t,"positions")})),k["\u0275\u0275elementStart"](1,"mat-icon",134),k["\u0275\u0275text"](2,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function st(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",132),k["\u0275\u0275text"](1,"content_copy"),k["\u0275\u0275elementEnd"]())}function rt(e,t){1&e&&k["\u0275\u0275element"](0,"mat-spinner",138)}function lt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",135),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index,n=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](2).duplicatePosition(n,t,"positions")})),k["\u0275\u0275template"](1,st,2,0,"mat-icon",136),k["\u0275\u0275template"](2,rt,1,0,"mat-spinner",137),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275propertyInterpolate"]("matTooltip",e.get("positionDuplicationLoader").value?"Duplicating...":"Duplicate Position"),k["\u0275\u0275property"]("disabled",e.get("positionDuplicationLoader").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.get("positionDuplicationLoader").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.get("positionDuplicationLoader").value)}}function ct(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",139),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](),n=t.$implicit,i=t.index;return k["\u0275\u0275nextContext"](3).openPositionEffort(n,i,1)})),k["\u0275\u0275elementStart"](1,"mat-icon",132),k["\u0275\u0275text"](2,"tune"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function ut(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"button",144),k["\u0275\u0275elementStart"](1,"mat-icon",58),k["\u0275\u0275text"](2,"drag_indicator"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function dt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," explore "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Non Man Power")}function pt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," workspace_premium "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","License")}function mt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",117),k["\u0275\u0275text"](1," person "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Man Power")}function ht(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",118),2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("matTooltip",e.get("positionName").value)("list",n.nmpList)("hideMatLabel",!0)("placeholder","Search "+n.positionField.label)("required",!0)("hasNoneOption",!1)("disabled",!t.get("isPositionFieldEnabled").value)}}function gt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",119),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275property"]("matTooltip",e.get("positionName").value),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("positionName").value)}}function vt(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",121),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"]().$implicit,i=k["\u0275\u0275nextContext"](3);let o=null;k["\u0275\u0275property"]("required",e.isMandatory)("hasNoneOption",!e.isMandatory)("list",e.isMasterDataFromPosition?t.get(e.key+"MasterData").value:e.masterData)("hideMatLabel",!0)("placeholder","Search "+e.label)("formControlName",e.key)("disabled",!((null==e||!e.enableVariableName||null!=(o=n.get(null==e?null:e.enableVariableName))&&o.value)&&(i.wEMappingEnabled||i.sDMappingEnabled?"entity"==e.key?i.wEMappingEnabled:"division"!=e.key||i.sDMappingEnabled:"division"==e.key?n.get("entity").value:"subDivision"!=e.key||n.get("entity").value&&n.get("division").value)))}}function ft(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275pipe"](3,"displayValue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit;let n=null;k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate"](k["\u0275\u0275pipeBind4"](3,1,null==(n=t.get(e.key))?null:n.value,e.masterData,"name","id"))}}function yt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,vt,1,7,"app-input-search",120),k["\u0275\u0275template"](2,ft,4,6,"ng-container",21),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"](5);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!(null!=e&&e.isForManpowerOnly)&&t.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!(null!=e&&e.isForManpowerOnly||t.isEditMode))}}function _t(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",127),k["\u0275\u0275text"](1),k["\u0275\u0275pipe"](2,"displayValue"),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](5).$implicit,t=k["\u0275\u0275nextContext"](4);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind4"](2,1,e.get("unit").value,t.unitList,"unit_suffix","id")," ")}}function bt(e,t){}function Ct(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",127),k["\u0275\u0275template"](1,bt,0,0,"ng-template",102),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"](9);const e=k["\u0275\u0275reference"](58);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngTemplateOutlet",e)}}function xt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"mat-form-field",124),k["\u0275\u0275element"](1,"input",146),k["\u0275\u0275template"](2,_t,3,6,"span",126),k["\u0275\u0275template"](3,Ct,2,1,"span",126),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"](2).$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275propertyInterpolate"]("placeholder",e.label),k["\u0275\u0275property"]("formControlName",e.key)("disabledD",!t.get(null==e?null:e.enableVariableName).value)("currency",n.quoteForm.get("quoteCurrency").value)("readonly","quotePositionId"===e.key)("allowNegative",e.allowNegative),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","quantity"==e.key),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","ratePerUnit"==e.key||"costPerUnit"==e.key)}}function Et(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275element"](2,"input",147),k["\u0275\u0275pipe"](3,"displayValue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3).$implicit,t=k["\u0275\u0275nextContext"]().$implicit,n=k["\u0275\u0275nextContext"](4);k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("formControlName",e.key)("currency",n.quoteForm.get("quoteCurrency").value)("showSuffix","noOfResources"!=e.key&&"quotePositionId"!=e.key)("suffix","quantity"==e.key?k["\u0275\u0275pipeBind4"](3,4,t.get("unit").value,n.unitList,"unit_suffix","id"):"")}}function St(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](8);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function wt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,xt,4,8,"mat-form-field",145),k["\u0275\u0275template"](2,Et,4,9,"ng-container",21),k["\u0275\u0275template"](3,St,3,1,"ng-container",21),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"](2).$implicit,n=k["\u0275\u0275nextContext"](3);let i=null,o=null,a=null;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.isEditMode&&!n.checkFieldShouldbeMasked(e.key,null!=(i=t.get("isNonManpower"))&&i.value?2:null!=(i=t.get("isLicense"))&&i.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!n.isEditMode&&!n.checkFieldShouldbeMasked(e.key,null!=(o=t.get("isNonManpower"))&&o.value?2:null!=(o=t.get("isLicense"))&&o.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.checkFieldShouldbeMasked(e.key,null!=(a=t.get("isNonManpower"))&&a.value?2:null!=(a=t.get("isLicense"))&&a.value?3:1))}}function qt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275template"](1,yt,3,2,"ng-container",21),k["\u0275\u0275template"](2,wt,4,3,"ng-container",21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," p-0"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","dropdown"===e.fieldType),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","number"===e.fieldType)}}function Dt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,qt,3,5,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"!=e.fieldType)}}function Ot(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275element"](1,"input",129),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"](5);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formControlName",e.key)("currency",t.quoteForm.get("quoteCurrency").value)}}function It(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275element"](1,"input",130),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formControlName",e.key)}}function Pt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",122),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](7);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Mt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275template"](1,Ot,2,2,"ng-container",21),k["\u0275\u0275template"](2,It,2,1,"ng-container",21),k["\u0275\u0275template"](3,Pt,3,1,"ng-container",21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"](2).$implicit,n=k["\u0275\u0275nextContext"](3);let i=null,o=null,a=null;k["\u0275\u0275classMapInterpolate1"]("col-",e.col," pr-0 revenue-value"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","display"===e.fieldType&&"quotePositionId"!=e.key&&!n.checkFieldShouldbeMasked(e.key,null!=(i=t.get("isNonManpower"))&&i.value?2:null!=(i=t.get("isLicense"))&&i.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","display"===e.fieldType&&"quotePositionId"==e.key&&!n.checkFieldShouldbeMasked(e.key,null!=(o=t.get("isNonManpower"))&&o.value?2:null!=(o=t.get("isLicense"))&&o.value?3:1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",n.checkFieldShouldbeMasked(e.key,null!=(a=t.get("isNonManpower"))&&a.value?2:null!=(a=t.get("isLicense"))&&a.value?3:1))}}function kt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,Mt,4,6,"div",104),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isVisible.value&&"display"==e.fieldType)}}function Ft(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",131),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index,n=k["\u0275\u0275nextContext"]().$implicit,i=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](2).addPosition(i,t,"nmpData",n)})),k["\u0275\u0275elementStart"](1,"mat-icon",132),k["\u0275\u0275text"](2,"add"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Tt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",133),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index,n=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](3).deletePosition(n,t,"nmpData")})),k["\u0275\u0275elementStart"](1,"mat-icon",134),k["\u0275\u0275text"](2,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function At(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",132),k["\u0275\u0275text"](1,"content_copy"),k["\u0275\u0275elementEnd"]())}function Rt(e,t){1&e&&k["\u0275\u0275element"](0,"mat-spinner",149)}function Qt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",135),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2).index,n=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](2).duplicatePosition(n,t,"positions")})),k["\u0275\u0275template"](1,At,2,0,"mat-icon",136),k["\u0275\u0275template"](2,Rt,1,0,"mat-spinner",148),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2).$implicit;k["\u0275\u0275propertyInterpolate"]("matTooltip",e.get("positionDuplicationLoader").value?"Duplicating...":"Duplicate Position"),k["\u0275\u0275property"]("disabled",e.get("positionDuplicationLoader").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.get("positionDuplicationLoader").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.get("positionDuplicationLoader").value)}}function Lt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",139),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](),n=t.$implicit,i=t.index;return k["\u0275\u0275nextContext"](4).openPositionEffort(n,i,2)})),k["\u0275\u0275elementStart"](1,"mat-icon",132),k["\u0275\u0275text"](2,"tune"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function jt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0,89),k["\u0275\u0275elementStart"](1,"div",140),k["\u0275\u0275elementStart"](2,"div"),k["\u0275\u0275template"](3,ut,3,0,"button",141),k["\u0275\u0275elementStart"](4,"div",107),k["\u0275\u0275template"](5,dt,2,1,"mat-icon",108),k["\u0275\u0275template"](6,pt,2,1,"mat-icon",108),k["\u0275\u0275template"](7,mt,2,1,"mat-icon",108),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](8,ht,1,7,"app-input-search",109),k["\u0275\u0275template"](9,gt,2,2,"ng-template",null,142,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](11,Dt,2,1,"ng-container",103),k["\u0275\u0275template"](12,kt,2,1,"ng-container",103),k["\u0275\u0275elementStart"](13,"div",143),k["\u0275\u0275template"](14,Ft,3,0,"button",112),k["\u0275\u0275template"](15,Tt,3,0,"button",113),k["\u0275\u0275template"](16,Qt,3,4,"button",114),k["\u0275\u0275template"](17,Lt,3,0,"button",115),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=k["\u0275\u0275reference"](10),i=k["\u0275\u0275nextContext"]().$implicit,o=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("formGroupName",e),k["\u0275\u0275advance"](2),k["\u0275\u0275classMapInterpolate1"]("col-",o.positionField.col," d-flex pr-0"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",i.get("isNonManpower").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i.get("isLicense").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!i.get("isLicense").value&&!i.get("isNonManpower").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode)("ngIfElse",n),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngForOf",o.customizeFields),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",o.customizeFields),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",o.isEditMode&&i.get("isAddPositionInlineEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode&&i.get("isDeletePositionEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode&&i.get("isClonePositionEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i.get("isCustomiseSlotEnabled").value)}}const Nt=function(e,t){return{"border-bottom":e,"border-top":t}};function Vt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0,89),k["\u0275\u0275elementStart"](1,"div",105),k["\u0275\u0275listener"]("cdkDragStarted",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](2).onPositionDragStart(t)})),k["\u0275\u0275elementStart"](2,"div",106),k["\u0275\u0275elementStart"](3,"div"),k["\u0275\u0275template"](4,Ae,3,0,"button",91),k["\u0275\u0275elementStart"](5,"div",107),k["\u0275\u0275template"](6,Re,2,1,"mat-icon",108),k["\u0275\u0275template"](7,Qe,2,1,"mat-icon",108),k["\u0275\u0275template"](8,Le,2,1,"mat-icon",108),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](9,je,1,7,"app-input-search",109),k["\u0275\u0275template"](10,Ne,2,2,"ng-template",null,110,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](12,Je,2,1,"ng-container",103),k["\u0275\u0275template"](13,it,2,1,"ng-container",103),k["\u0275\u0275elementStart"](14,"div",111),k["\u0275\u0275template"](15,ot,3,0,"button",112),k["\u0275\u0275template"](16,at,3,0,"button",113),k["\u0275\u0275template"](17,lt,3,4,"button",114),k["\u0275\u0275template"](18,ct,3,0,"button",115),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerStart"](19,116),k["\u0275\u0275template"](20,jt,18,16,"ng-container",79),k["\u0275\u0275elementContainerEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=k["\u0275\u0275reference"](11),o=k["\u0275\u0275nextContext"]().$implicit,a=k["\u0275\u0275nextContext"](2);let s=null,r=null,l=null;k["\u0275\u0275property"]("formGroupName",n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngStyle",k["\u0275\u0275pureFunction2"](19,Nt,!a.isEditMode&&e.get("nmpData").controls.length&&n!=o.get("positions").controls.length-1?"1px solid #DADCE2":"none",!a.isEditMode&&e.get("nmpData").controls.length&&0!=n?"1px solid #DADCE2":"none"))("cdkDragDisabled",a.isDragDisabled(o,e)),k["\u0275\u0275advance"](2),k["\u0275\u0275classMapInterpolate1"]("col-",a.positionField.col," d-flex pr-0"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",a.isEditMode),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",null==(s=e.get("isNonManpower"))?null:s.value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",null==(r=e.get("isLicense"))?null:r.value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!(null!=(l=e.get("isLicense"))&&l.value||e.get("isNonManpower").value)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",a.isEditMode)("ngIfElse",i),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngForOf",a.customizeFields),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",a.customizeFields),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",a.isEditMode&&e.get("isAddPositionInlineEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",a.isEditMode&&e.get("isDeletePositionEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",a.isEditMode&&e.get("isClonePositionEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.get("isCustomiseSlotEnabled").value),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e.get("nmpData").controls)}}const Ut=function(e){return{color:e}};function $t(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0,89),k["\u0275\u0275elementStart"](1,"div",90),k["\u0275\u0275template"](2,we,3,0,"button",91),k["\u0275\u0275elementStart"](3,"div",92),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,qe,3,0,"button",93),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",94),k["\u0275\u0275listener"]("cdkDropListDropped",(function(n){k["\u0275\u0275restoreView"](e);const i=t.index;return k["\u0275\u0275nextContext"](2).dropPosition(n,i)})),k["\u0275\u0275elementContainerStart"](7,95),k["\u0275\u0275template"](8,Te,6,9,"div",96),k["\u0275\u0275template"](9,Vt,21,22,"ng-container",79),k["\u0275\u0275elementContainerEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=k["\u0275\u0275nextContext"](2);k["\u0275\u0275property"]("formGroupName",n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("cdkDragDisabled",!i.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngStyle",k["\u0275\u0275pureFunction1"](11,Ut,e.get("isSection").value?"#45546E":"#cf0001"))("matTooltip",e.get("serviceName").value),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("serviceName").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i.isEditMode&&e.get("isDeleteServiceEnabled").value),k["\u0275\u0275advance"](1),k["\u0275\u0275propertyInterpolate1"]("id","positionDropZone",n,""),k["\u0275\u0275property"]("cdkDropListConnectedTo",i.positionDropZones),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",e.get("positions").controls.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.get("positions").controls)}}function Gt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span",165),k["\u0275\u0275text"](1," Discounts "),k["\u0275\u0275elementEnd"]())}function Bt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Discount Name"),k["\u0275\u0275elementEnd"]())}function zt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-form-field",124),k["\u0275\u0275element"](1,"input",167),k["\u0275\u0275elementEnd"]())}function Wt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,zt,2,0,"mat-form-field",160),k["\u0275\u0275elementContainerEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](9),t=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.isEditMode)("ngIfElse",e)}}function Ht(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",169),2&e){const e=k["\u0275\u0275nextContext"](5);k["\u0275\u0275property"]("list",e.discountsList)("hideMatLabel",!0)("placeholder","Search Discount")}}function Xt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,Ht,1,3,"app-input-search",168),k["\u0275\u0275elementContainerEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](9),t=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.isEditMode)("ngIfElse",e)}}function Yt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275text"](1),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.get("discountName").value," ")}}function Kt(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275text"](1),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.get("discountName").value," ")}}function Jt(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",122),k["\u0275\u0275template"](1,Yt,2,1,"ng-container",21),k["\u0275\u0275template"](2,Kt,2,1,"ng-container",21),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.get("isCustom").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.get("isCustom").value)}}function Zt(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Discount %"),k["\u0275\u0275elementEnd"]())}function en(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-form-field",124),k["\u0275\u0275element"](1,"input",170),k["\u0275\u0275elementStart"](2,"span",127),k["\u0275\u0275text"](3,"%"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function tn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",122),k["\u0275\u0275text"](1),k["\u0275\u0275elementStart"](2,"span",171),k["\u0275\u0275text"](3,"%"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("discountPercentage").value)}}function nn(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Discount Value"),k["\u0275\u0275elementEnd"]())}function on(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"button",172),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](3).addDiscountAndTax(t,"discounts")})),k["\u0275\u0275elementStart"](2,"mat-icon",132),k["\u0275\u0275text"](3,"add"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"button",173),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](3).deleteDiscountAndTax(t,"discounts")})),k["\u0275\u0275elementStart"](5,"mat-icon",134),k["\u0275\u0275text"](6,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"button",174),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](3).toggleCustomDiscount(t)})),k["\u0275\u0275elementStart"](8,"mat-icon",175),k["\u0275\u0275text"](9),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](8),k["\u0275\u0275property"]("ngStyle",k["\u0275\u0275pureFunction1"](2,Ut,e.get("isCustom").value?"#ff6d5c":"#6E7B8F")),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("isCustom").value?"toggle_on":"toggle_off")}}function an(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",166),k["\u0275\u0275text"](2,"Total Discount"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",176),k["\u0275\u0275element"](4,"input",177),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](4);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("formControl",e.quoteForm.get("totalDiscount"))("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function sn(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0,89),k["\u0275\u0275elementStart"](1,"div",153),k["\u0275\u0275elementStart"](2,"div",154),k["\u0275\u0275template"](3,Gt,2,0,"span",155),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",156),k["\u0275\u0275template"](5,Bt,2,0,"div",157),k["\u0275\u0275template"](6,Wt,2,2,"ng-container",21),k["\u0275\u0275template"](7,Xt,2,2,"ng-container",21),k["\u0275\u0275template"](8,Jt,3,2,"ng-template",null,158,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",159),k["\u0275\u0275template"](11,Zt,2,0,"div",157),k["\u0275\u0275template"](12,en,4,0,"mat-form-field",160),k["\u0275\u0275template"](13,tn,4,1,"ng-template",null,161,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"div",162),k["\u0275\u0275template"](16,nn,2,0,"div",157),k["\u0275\u0275elementStart"](17,"div",101),k["\u0275\u0275element"](18,"input",163),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](19,"div",154),k["\u0275\u0275template"](20,on,10,4,"ng-container",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](21,"div",154),k["\u0275\u0275elementStart"](22,"div",164),k["\u0275\u0275template"](23,an,5,3,"ng-container",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=k["\u0275\u0275reference"](14),o=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("formGroupName",n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",0==n?"pt-2":""),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",0==n),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",0==n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.get("isCustom").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.get("isCustom").value),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",0==n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode)("ngIfElse",i),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",0==n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",o.isEditMode?"dt-class pt-2":"display-class"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("currency",o.quoteForm.get("quoteCurrency").value)("showSuffix",!0),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",o.isEditMode),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",0==n)}}function rn(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span",165),k["\u0275\u0275text"](1," Taxes "),k["\u0275\u0275elementEnd"]())}function ln(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Tax Name"),k["\u0275\u0275elementEnd"]())}function cn(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-input-search",182),2&e){const e=k["\u0275\u0275nextContext"](4);k["\u0275\u0275property"]("list",e.taxesList)("hideMatLabel",!0)("placeholder","Search Tax")}}function un(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",122),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("taxName").value)}}function dn(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Tax %"),k["\u0275\u0275elementEnd"]())}function pn(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-form-field",124),k["\u0275\u0275element"](1,"input",183),k["\u0275\u0275elementStart"](2,"span",127),k["\u0275\u0275text"](3,"%"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function mn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",122),k["\u0275\u0275text"](1),k["\u0275\u0275elementStart"](2,"span",171),k["\u0275\u0275text"](3,"%"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.get("taxPercentage").value)}}function hn(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",166),k["\u0275\u0275text"](1,"Tax Value"),k["\u0275\u0275elementEnd"]())}function gn(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"button",184),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](3).addDiscountAndTax(t,"taxes")})),k["\u0275\u0275elementStart"](2,"mat-icon",132),k["\u0275\u0275text"](3,"add"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"button",185),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().index;return k["\u0275\u0275nextContext"](3).deleteDiscountAndTax(t,"taxes")})),k["\u0275\u0275elementStart"](5,"mat-icon",134),k["\u0275\u0275text"](6,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}}function vn(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",166),k["\u0275\u0275text"](2,"Total Tax"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",176),k["\u0275\u0275element"](4,"input",177),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](4);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("formControl",e.quoteForm.get("totalTax"))("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function fn(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0,89),k["\u0275\u0275elementStart"](1,"div",153),k["\u0275\u0275elementStart"](2,"div",154),k["\u0275\u0275template"](3,rn,2,0,"span",155),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",156),k["\u0275\u0275template"](5,ln,2,0,"div",157),k["\u0275\u0275template"](6,cn,1,3,"app-input-search",178),k["\u0275\u0275template"](7,un,2,1,"ng-template",null,179,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",159),k["\u0275\u0275template"](10,dn,2,0,"div",157),k["\u0275\u0275template"](11,pn,4,0,"mat-form-field",160),k["\u0275\u0275template"](12,mn,4,1,"ng-template",null,180,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"div",162),k["\u0275\u0275template"](15,hn,2,0,"div",157),k["\u0275\u0275elementStart"](16,"div",101),k["\u0275\u0275element"](17,"input",181),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"div",154),k["\u0275\u0275template"](19,gn,7,0,"ng-container",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](20,"div",154),k["\u0275\u0275elementStart"](21,"div",164),k["\u0275\u0275template"](22,vn,5,3,"ng-container",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=k["\u0275\u0275reference"](8),i=k["\u0275\u0275reference"](13),o=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("formGroupName",e),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",0==e?"pt-2":""),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",0==e),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",0==e),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode)("ngIfElse",n),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",0==e),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",o.isEditMode)("ngIfElse",i),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",0==e),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",o.isEditMode?"dt-class pt-2":"display-class"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("currency",o.quoteForm.get("quoteCurrency").value)("showSuffix",!0),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",o.isEditMode),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",0==e)}}function yn(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275element"](1,"div",150),k["\u0275\u0275elementContainerStart"](2,151),k["\u0275\u0275template"](3,sn,24,15,"ng-container",79),k["\u0275\u0275elementContainerEnd"](),k["\u0275\u0275elementContainerStart"](4,152),k["\u0275\u0275template"](5,fn,23,15,"ng-container",79),k["\u0275\u0275elementContainerEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngForOf",e.quoteForm.get("discounts").controls),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e.quoteForm.get("taxes").controls)}}function _n(e,t){1&e&&k["\u0275\u0275element"](0,"div",154)}function bn(e,t){1&e&&k["\u0275\u0275element"](0,"div",154)}function Cn(e,t){1&e&&k["\u0275\u0275element"](0,"div",154)}function xn(e,t){1&e&&k["\u0275\u0275element"](0,"div",154)}function En(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275element"](1,"input",191),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function Sn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function wn(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",192,193),k["\u0275\u0275elementStart"](3,"mat-icon",194),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275reference"](2);return k["\u0275\u0275nextContext"](3).openRevSummaryOverlay(t)})),k["\u0275\u0275text"](4,"expand_less"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function qn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",186),k["\u0275\u0275elementStart"](1,"span",187),k["\u0275\u0275text"](2,"Total Revenue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,En,2,2,"span",188),k["\u0275\u0275template"](4,Sn,2,1,"span",188),k["\u0275\u0275template"](5,wn,5,0,"div",189),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalRevenue","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",null==e.quote_footer_splitup_configuration?null:e.quote_footer_splitup_configuration.showResourceTypeRevenueSplitUp)}}function Dn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275element"](1,"input",196),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function On(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function In(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",192,197),k["\u0275\u0275elementStart"](3,"mat-icon",194),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275reference"](2);return k["\u0275\u0275nextContext"](3).openCostOverlay(t)})),k["\u0275\u0275text"](4,"expand_less"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Pn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",186),k["\u0275\u0275elementStart"](1,"span",195),k["\u0275\u0275text"](2,"Total Cost"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,Dn,2,2,"span",188),k["\u0275\u0275template"](4,On,2,1,"span",188),k["\u0275\u0275template"](5,In,5,0,"div",189),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalCost","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",null==e.quote_footer_splitup_configuration?null:e.quote_footer_splitup_configuration.showResourceTypeCostSplitUp)}}function Mn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275element"](1,"input",198),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!0)}}function kn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",190),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Fn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",186),k["\u0275\u0275elementStart"](1,"span",195),k["\u0275\u0275text"](2,"Total GM"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,Mn,2,2,"span",188),k["\u0275\u0275template"](4,kn,2,1,"span",188),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalGM","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalGM","FOOTER"))}}function Tn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",186),k["\u0275\u0275elementStart"](1,"span",19),k["\u0275\u0275text"](2,"Total GM %"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"span",190),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",e.quoteForm.get("totalGMPercentage").value,"")}}function An(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",201),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](3).addButtonClick(t)})),k["\u0275\u0275elementStart"](1,"mat-icon",202),k["\u0275\u0275text"](2,"add"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"span",203),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate"](null==e?null:e.label)}}function Rn(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,An,5,1,"div",200),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.has_project_integrated&&(6==(null==e?null:e.id)?0==n.quoteForm.get("discounts").controls.length:7!=(null==e?null:e.id)||0==n.quoteForm.get("taxes").controls.length))}}function Qn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",199),k["\u0275\u0275template"](1,Rn,2,1,"ng-container",103),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.addButtonsList)}}function Ln(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",204),k["\u0275\u0275elementStart"](1,"div",205),k["\u0275\u0275elementStart"](2,"div",206),k["\u0275\u0275elementStart"](3,"div",207),k["\u0275\u0275text"](4," Create Section "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"div",154),k["\u0275\u0275elementStart"](6,"button",208),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).closeSectionDialog()})),k["\u0275\u0275elementStart"](7,"mat-icon",209),k["\u0275\u0275text"](8,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",210),k["\u0275\u0275text"](10," Add Section Name "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](11,"div",211),k["\u0275\u0275elementStart"](12,"mat-form-field",212),k["\u0275\u0275element"](13,"input",213),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"div",214),k["\u0275\u0275elementStart"](15,"button",215),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).closeSectionDialog()})),k["\u0275\u0275text"](16," Cancel "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](17,"button",216),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).addSection()})),k["\u0275\u0275text"](18," Create "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](13),k["\u0275\u0275property"]("formControl",e.addSectionFormControl),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("disabled",!e.addSectionFormControl.value)("ngClass",e.addSectionFormControl.value?"create-btn":"create-btn-disabled")}}function jn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",223),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("manpowerCost").value?null:e.quoteForm.get("manpowerCost").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("manpowerCost").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function Nn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Vn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function Un(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",225),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("nonManpowerCost").value?null:e.quoteForm.get("nonManpowerCost").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("nonManpowerCost").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function $n(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Gn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function Bn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",226),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("licenseCost").value?null:e.quoteForm.get("licenseCost").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("licenseCost").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function zn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Wn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function Hn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",227),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("totalCost").value?null:e.quoteForm.get("totalCost").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("totalCost").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function Xn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function Yn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function Kn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",217),k["\u0275\u0275elementStart"](1,"div",205),k["\u0275\u0275elementStart"](2,"div",48),k["\u0275\u0275elementStart"](3,"div",218),k["\u0275\u0275text"](4,"Manpower Cost"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,jn,2,3,"span",219),k["\u0275\u0275template"](6,Nn,2,1,"span",219),k["\u0275\u0275template"](7,Vn,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](8,"div",48),k["\u0275\u0275elementStart"](9,"div",218),k["\u0275\u0275text"](10,"Non Manpower Cost"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](11,Un,2,3,"span",219),k["\u0275\u0275template"](12,$n,2,1,"span",219),k["\u0275\u0275template"](13,Gn,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"div",48),k["\u0275\u0275elementStart"](15,"div",218),k["\u0275\u0275text"](16,"License Cost"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](17,Bn,2,3,"span",219),k["\u0275\u0275template"](18,zn,2,1,"span",219),k["\u0275\u0275template"](19,Wn,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](20,"div",221),k["\u0275\u0275elementStart"](21,"div",218),k["\u0275\u0275text"](22,"Total Cost"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](23,Hn,2,3,"span",219),k["\u0275\u0275template"](24,Xn,2,1,"span",219),k["\u0275\u0275template"](25,Yn,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost",1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalCost",1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost","FOOTER")),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost",2)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalCost",2)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost",2)),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost",3)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalCost",3)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost",3)),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalCost","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalCost","FOOTER"))}}function Jn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",228),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("manpowerRevenue").value?null:e.quoteForm.get("manpowerRevenue").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("manpowerRevenue").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function Zn(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function ei(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function ti(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",229),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("nonManpowerRevenue").value?null:e.quoteForm.get("nonManpowerRevenue").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("nonManpowerRevenue").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function ni(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function ii(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function oi(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",230),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("licenseRevenue").value?null:e.quoteForm.get("licenseRevenue").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("licenseRevenue").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function ai(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function si(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function ri(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275element"](1,"input",231),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",(null==e.quoteForm.get("totalRevenue").value?null:e.quoteForm.get("totalRevenue").value.toString().length)>9?e.formatNumberByCurrency(e.quoteForm.get("totalRevenue").value,e.quoteForm.get("quoteCurrency").value):"")("currency",e.quoteForm.get("quoteCurrency").value)("showSuffix",!1)}}function li(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",222),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.masking_configuration.maskDisplay||"****"," ")}}function ci(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span",224),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.quoteForm.get("quoteCurrency").value)}}function ui(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",217),k["\u0275\u0275elementStart"](1,"div",205),k["\u0275\u0275elementStart"](2,"div",48),k["\u0275\u0275elementStart"](3,"div",218),k["\u0275\u0275text"](4,"Manpower Revenue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,Jn,2,3,"span",219),k["\u0275\u0275template"](6,Zn,2,1,"span",219),k["\u0275\u0275template"](7,ei,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](8,"div",48),k["\u0275\u0275elementStart"](9,"div",218),k["\u0275\u0275text"](10,"Non Manpower Revenue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](11,ti,2,3,"span",219),k["\u0275\u0275template"](12,ni,2,1,"span",219),k["\u0275\u0275template"](13,ii,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"div",48),k["\u0275\u0275elementStart"](15,"div",218),k["\u0275\u0275text"](16,"License Revenue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](17,oi,2,3,"span",219),k["\u0275\u0275template"](18,ai,2,1,"span",219),k["\u0275\u0275template"](19,si,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](20,"div",221),k["\u0275\u0275elementStart"](21,"div",218),k["\u0275\u0275text"](22,"Total Revenue"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](23,ri,2,3,"span",219),k["\u0275\u0275template"](24,li,2,1,"span",219),k["\u0275\u0275template"](25,ci,2,1,"span",220),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalRevenue",1)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",1)),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",2)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalRevenue",2)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",2)),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",3)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalRevenue",3)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue",3)),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.checkFieldShouldbeMasked("totalRevenue","FOOTER")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.checkFieldShouldbeMasked("totalRevenue","FOOTER"))}}function di(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",232),k["\u0275\u0275elementStart"](1,"div",233),k["\u0275\u0275elementStart"](2,"div",206),k["\u0275\u0275elementStart"](3,"div",207),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"div",154),k["\u0275\u0275elementStart"](6,"button",208),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).closeQuoteOverlay()})),k["\u0275\u0275elementStart"](7,"mat-icon",209),k["\u0275\u0275text"](8,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",211),k["\u0275\u0275elementStart"](10,"mat-form-field",212),k["\u0275\u0275element"](11,"input",213),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](12,"div",214),k["\u0275\u0275elementStart"](13,"button",215),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).closeQuoteOverlay()})),k["\u0275\u0275text"](14," Cancel "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"button",234),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).changeQuoteName()})),k["\u0275\u0275text"](16," Save "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ","CHANGE_REQUEST"!==e.quoteForm.get("quoteType").value?"Change Quote Name":"Change Request Name"," "),k["\u0275\u0275advance"](7),k["\u0275\u0275property"]("formControl",e.quoteNameFormControl)}}function pi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",76,77),k["\u0275\u0275elementStart"](3,"div",78),k["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().dropService(t)})),k["\u0275\u0275template"](4,$t,10,13,"ng-container",79),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,yn,6,2,"ng-container",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",80),k["\u0275\u0275element"](7,"div",18),k["\u0275\u0275template"](8,_n,1,0,"div",81),k["\u0275\u0275template"](9,bn,1,0,"div",81),k["\u0275\u0275template"](10,Cn,1,0,"div",81),k["\u0275\u0275template"](11,xn,1,0,"div",81),k["\u0275\u0275template"](12,qn,6,3,"div",82),k["\u0275\u0275template"](13,Pn,6,3,"div",82),k["\u0275\u0275template"](14,Fn,5,2,"div",82),k["\u0275\u0275template"](15,Tn,5,1,"div",82),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](16,Qn,2,1,"div",83),k["\u0275\u0275template"](17,Ln,19,3,"ng-template",84,85,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](19,Kn,26,12,"ng-template",84,86,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](21,ui,26,12,"ng-template",84,87,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](23,di,17,2,"ng-template",84,88,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](),t=k["\u0275\u0275reference"](12);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("@listAnimation",void 0),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngForOf",e.servicesFormArr.controls),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.quoteForm.get("discounts").controls.length||e.quoteForm.get("taxes").controls.length),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",!e.isRevenueActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.isCostActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.isGMActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.isGMPerActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isRevenueActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isCostActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isGMActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isGMPerActive),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.isEditMode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t)}}function mi(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span",235),k["\u0275\u0275text"](1,"*"),k["\u0275\u0275elementEnd"]())}function hi(e,t){if(1&e&&k["\u0275\u0275text"](0),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275textInterpolate1"](" ",e.quoteForm.get("quoteCurrency").value," ")}}function gi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",236),k["\u0275\u0275element"](2,"img",237),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",238),k["\u0275\u0275elementStart"](4,"span",239),k["\u0275\u0275text"](5,"No Services Here"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"span",240),k["\u0275\u0275text"](7," Create a quote by listing down the "),k["\u0275\u0275element"](8,"br"),k["\u0275\u0275text"](9," services and see the whole picture. "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",241),k["\u0275\u0275elementStart"](11,"button",242),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().addService()})),k["\u0275\u0275text"](12," Add Services "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}}function vi(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",262),k["\u0275\u0275text"](1," - "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","-")}function fi(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",263),k["\u0275\u0275element"](1,"app-user-image",264),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",e.oid)("matTooltip",e.employee_name)}}function yi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",245),k["\u0275\u0275elementStart"](1,"div"),k["\u0275\u0275elementStart"](2,"div",246),k["\u0275\u0275elementStart"](3,"div",247),k["\u0275\u0275elementStart"](4,"mat-icon"),k["\u0275\u0275text"](5," send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275text"](6," \xa0 Send Quote For Approval "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"button",248),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(t,"SUBMIT_CANCEL")})),k["\u0275\u0275elementStart"](8,"mat-icon"),k["\u0275\u0275text"](9,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",249),k["\u0275\u0275elementStart"](11,"div",48),k["\u0275\u0275elementStart"](12,"div",250),k["\u0275\u0275elementStart"](13,"div",251),k["\u0275\u0275text"](14,"Quote Name:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"div",250),k["\u0275\u0275elementStart"](16,"div",252),k["\u0275\u0275text"](17),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"div",48),k["\u0275\u0275elementStart"](19,"div",250),k["\u0275\u0275elementStart"](20,"div",251),k["\u0275\u0275text"](21,"Quote Value:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"div",253),k["\u0275\u0275text"](23),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](24,"div",254),k["\u0275\u0275elementStart"](25,"div",255),k["\u0275\u0275elementStart"](26,"span"),k["\u0275\u0275text"](27," Approvers \xa0 "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](28,"div",256),k["\u0275\u0275template"](29,vi,2,1,"div",257),k["\u0275\u0275template"](30,fi,2,2,"div",258),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](31,"div",259),k["\u0275\u0275elementStart"](32,"button",260),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(t.quoteDetails,"SUBMIT_CANCEL")})),k["\u0275\u0275text"](33," Cancel "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](34,"button",261),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(null==t?null:t.quoteDetails,"SUBMIT")})),k["\u0275\u0275text"](35," Send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](17),k["\u0275\u0275textInterpolate2"](" QT/",null==e?null:e.quoteDetails.quote_header_id," - ",null==e?null:e.quoteDetails.quote_name," "),k["\u0275\u0275advance"](6),k["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.quoteDetails.quote_revenue_amount)+" "+(null==e||null==e.quoteDetails?null:e.quoteDetails.quote_currency)," "),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("ngIf",0==t.reviewersList.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",t.reviewersList),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("disabled",0==t.reviewersList.length)("matTooltip",0==t.reviewersList.length?"No Approvers Found!":"")}}function _i(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",265),k["\u0275\u0275element"](1,"mat-spinner",266),k["\u0275\u0275elementEnd"]())}function bi(e,t){if(1&e&&(k["\u0275\u0275template"](0,yi,36,7,"div",243),k["\u0275\u0275template"](1,_i,2,0,"div",244)),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.miniLoader),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.miniLoader)}}function Ci(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",255),k["\u0275\u0275elementStart"](1,"span",255),k["\u0275\u0275text"](2," Submitted By \xa0 "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function xi(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",278),k["\u0275\u0275element"](1,"app-user-image",279),k["\u0275\u0275text"](2,"\xa0\xa0\xa0 "),k["\u0275\u0275elementStart"](3,"div"),k["\u0275\u0275elementStart"](4,"div",280),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",281),k["\u0275\u0275element"](7,"app-user-profile",282),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",null==e.submitterDetails?null:e.submitterDetails.oid),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"]("A.ID ",(null==e||null==e.submitterDetails?null:e.submitterDetails.associate_id)||(null==t.submitterDetails?null:t.submitterDetails.associate_id),""),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("oid",null==e.submitterDetails?null:e.submitterDetails.oid)}}function Ei(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",262),k["\u0275\u0275text"](1," - "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","-")}function Si(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",263),k["\u0275\u0275element"](1,"app-user-image",264),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",e.oid)("matTooltip",n.getApproverTooltip(e))}}function wi(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",262),k["\u0275\u0275text"](1," info "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Comments are required for rejecting document")}function qi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",259),k["\u0275\u0275elementStart"](1,"button",260),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2).$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(t.quoteDetails,"REJECT")})),k["\u0275\u0275text"](2," Reject "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"button",283),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2).$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(null==t?null:t.quoteDetails,"APPROVE")})),k["\u0275\u0275text"](4," Approve "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Di(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",245),k["\u0275\u0275elementStart"](1,"div"),k["\u0275\u0275elementStart"](2,"div",246),k["\u0275\u0275elementStart"](3,"div",247),k["\u0275\u0275elementStart"](4,"mat-icon"),k["\u0275\u0275text"](5," send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275text"](6),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"button",267),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(t,"CANCEL")})),k["\u0275\u0275elementStart"](8,"mat-icon"),k["\u0275\u0275text"](9,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",249),k["\u0275\u0275template"](11,Ci,3,0,"div",268),k["\u0275\u0275template"](12,xi,8,3,"div",269),k["\u0275\u0275elementStart"](13,"div",270),k["\u0275\u0275elementStart"](14,"div",271),k["\u0275\u0275text"](15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"div",272),k["\u0275\u0275elementStart"](17,"div",256),k["\u0275\u0275template"](18,Ei,2,1,"div",257),k["\u0275\u0275template"](19,Si,2,2,"div",258),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](20,"div",273),k["\u0275\u0275text"](21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"div",48),k["\u0275\u0275elementStart"](23,"div",250),k["\u0275\u0275elementStart"](24,"div",251),k["\u0275\u0275text"](25,"Quote Name:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](26,"div",250),k["\u0275\u0275elementStart"](27,"div",252),k["\u0275\u0275text"](28),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](29,"div",48),k["\u0275\u0275elementStart"](30,"div",250),k["\u0275\u0275elementStart"](31,"div",251),k["\u0275\u0275text"](32,"Quote Value:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](33,"div",253),k["\u0275\u0275text"](34),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](35,"div",254),k["\u0275\u0275elementStart"](36,"div",274),k["\u0275\u0275elementStart"](37,"span",275),k["\u0275\u0275text"](38," Comments "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](39,wi,2,1,"mat-icon",257),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](40,"textarea",276),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).commentToSubmitter=t})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](41,qi,5,0,"div",277),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](6),k["\u0275\u0275textInterpolate1"](" \xa0 ","Quote Approval"," "),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover),k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate1"](" ",e.quoteDetails.isApprover?"Approvers for Quote Activation Request":"Approvers "," "),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",0==t.reviewersList.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",t.reviewersList),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngStyle",t.getStatusCss(null==e.quoteDetails?null:e.quoteDetails.quote_status)),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.quoteDetails.status_name||"-"," "),k["\u0275\u0275advance"](7),k["\u0275\u0275textInterpolate2"](" QT/",null==e?null:e.quoteDetails.quote_header_id," - ",null==e?null:e.quoteDetails.quote_name," "),k["\u0275\u0275advance"](6),k["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.quoteDetails.quote_revenue_amount)+" "+(null==e||null==e.quoteDetails?null:e.quoteDetails.quote_currency)," "),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",(null==e||null==e.quoteDetails?null:e.quoteDetails.isApprover)&&t.allow_approval),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngModel",t.commentToSubmitter)("readonly",!(null!=e.quoteDetails&&e.quoteDetails.isApprover)||3==e.quoteDetails.quote_status||2==e.quoteDetails.quote_status),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover&&1===e.quoteDetails.quote_status)}}function Oi(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",265),k["\u0275\u0275element"](1,"mat-spinner",266),k["\u0275\u0275elementEnd"]())}function Ii(e,t){if(1&e&&(k["\u0275\u0275template"](0,Di,42,15,"div",243),k["\u0275\u0275template"](1,Oi,2,0,"div",244)),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.miniLoader),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.miniLoader)}}let Pi=(()=>{class e{constructor(e,t,i,o,a,s,r,l,c,d,p,m,h,g){this.route=e,this.$router=t,this.fb=i,this.dialog=o,this.viewContainerRef=a,this.overlay=s,this._quoteMainService=r,this._toaster=l,this.masterDataService=c,this.spinner=d,this._util=p,this.opportunityService=m,this._landingPageService=h,this._ticket=g,this._onDestroy=new C.b,this.valueChangeSubscription=new x.a,this.routeDataSubscription=new x.a,this.quoteCreateDataSubscription=new x.a,this.opportunityId=null,this.skillList=[],this.positionList=[],this.nmpList=[],this.licenseList=[],this.experienceList=[],this.workLocationList=[],this.nationalityList=[],this.entityList=[],this.divisionList=[],this.subDivisionList=[],this.unitList=[],this.otherUnitList=[],this.manPowerUnit=[],this.nonManPowerUnit=[],this.licenseUnit=[],this.discountsList=[],this.taxesList=[],this.orgMappingList=[],this.wEMappingList=[],this.sDMappingList=[],this.businessTypeList=[],this.geographicalRegionList=[],this.positionStatusList=[],this.addButtonsList=[],this.positionDropZones=[],this.initialServiceDragIndex=null,this.allocation_details=[],this.has_date_change_access=!1,this.editRestricted=!1,this.editRestrictedMsg="",this.project_access_field_config=[],this.newly_added_quote_project_integration_config=[],this.addSectionFormControl=new u.j(null),this.quoteNameFormControl=new u.j(null),this.costSummary={resourceCost:0,nonManpowerCost:0,total:0},this.revenueSummary={revenue:0,cost:0,grossMargin:0},this.isEditMode=!0,this.isDataLoading=!1,this.isDataBeingSaved=!1,this.isQuoteCreateMode=!1,this.isChangesMade=!1,this.isCUDEnabled=!0,this.isRevenueActive=!0,this.isCostActive=!0,this.isGMActive=!0,this.isGMPerActive=!0,this.wEMappingEnabled=!1,this.sDMappingEnabled=!1,this.customizeFields=[],this.mandatoryFields=[],this.fieldConfig=[],this.milestoneFieldConfig=[],this.quoteId=null,this.calendarId=null,this.workScheduleId=null,this.defaultCurrency=null,this.conversionTypeId=null,this.positionField={label:"Position",col:3},this.checkProjectCreated=!1,this.isPositionFieldEnabled=!0,this.isUnitEnabled=!0,this.isWorkLocationEnabled=!0,this.isQuantityEnabled=!0,this.isNoOfResourcesEnabled=!0,this.isRatePerUnitEnabled=!0,this.isCostPerUnitEnabled=!0,this.isAddServiceEnabled=!0,this.isDeleteServiceEnabled=!0,this.isAddPositionEnabled=!0,this.isAddPositionInlineEnabled=!0,this.isDeletePositionEnabled=!0,this.isClonePositionEnabled=!0,this.isCustomiseSlotEnabled=!0,this.interServicePositionDragNDrop=!0,this.isNationalityEnabled=!0,this.isWorkExperienceEnabled=!0,this.isAddSectionEnabled=!0,this.isTypeOfBusinessEnabled=!0,this.isRevenueRegionEnabled=!0,this.isPositionStatusEnabled=!0,this.isPositionStatusToLost=!0,this.isMilestoneEnabled=!0,this.isNonManPowerButtonEnabled=!0,this.isLicenseButtonEnabled=!0,this.isDiscountButtonEnabled=!0,this.isTaxButtonEnabled=!0,this.isMilestoneButtonEnabled=!0,this.deliveryStartDateEnabled=!0,this.deliveryEndDateEnabled=!0,this.isQuoteActive=!0,this.currencyList=[],this.milestoneList=[],this.quote_currency_change=!1,this.commentToSubmitter="",this.submitterDetails=[],this.manpowerNonManpowerMapping=!1,this.geographicRevenueRegionMapping=!1,this.calendarCombinationConfig=[],this.revenueRegionMapping=[],this.oppQuoteDetails=[],this.quote_footer_splitup_configuration={},this.quoteMilestoneTagging=!1,this.isChangeRequestActive=!1,this.previousMilestoneId=null,this.positionMilestoneDropZones=[],this.milestoneApplicable=!1,this.lumpsum_quote_config=[],this.service_based_unit=!1,this.getQuoteConfiguration=()=>{this.masterDataService.quoteConfiguration.pipe(Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t,n,i,o,a,s,r,l;const c=e;for(const e of c)if(e&&e.quote_config_name&&e.hasOwnProperty("quote_config_value"))switch(e.quote_config_name){case"quote_mandatory_fields":this.mandatoryFields=e.quote_config_value;break;case"quote_field_config":this.fieldConfig=e.quote_config_value;break;case"quote_action_button_config":if(e.quote_config_value){const n=e.quote_config_value;for(const e of n){e.has_project_integrated=!0;const n=e.notApplicableForQuoteType||[],i=(null===(t=this.wholeQuoteData)||void 0===t?void 0:t.quote_type)||this.quoteForm.get("quoteType").value,o=!n.includes(i);e.isActive&&o&&this.addButtonsList.push(e)}}break;case"quote_calendar_id":this.calendarId=e.quote_config_value;break;case"quote_work_schedule_id":this.workScheduleId=e.quote_config_value;break;case"quote_currency":this.defaultCurrency=e.quote_config_value;break;case"quote_currency_conversion_type_id":this.conversionTypeId=e.quote_config_value;break;case"quote_work_location_entity_mapping_enabled":this.wEMappingEnabled=e.quote_config_value||!1;break;case"quote_service_division_mapping_enabled":this.sDMappingEnabled=e.quote_config_value||!1;break;case"quote_date_field_config":if(this.has_date_change_access=e.quote_config_value||!1,this.has_date_change_access)try{if(this.projectDetailsForOpportunity&&"S"==this.projectDetailsForOpportunity.messType&&(null===(n=this.projectDetailsForOpportunity.messData)||void 0===n?void 0:n.length)>0){const e=c.find(e=>"quote_project_integration_config"==e.quote_config_name);if(e){const t=e.quote_config_value,n=t.find(e=>"delivery_start_date"==e.key),a=t.find(e=>"delivery_end_date"==e.key);this.deliveryStartDateEnabled=null!==(i=null==n?void 0:n.editEnabled)&&void 0!==i&&i,this.deliveryEndDateEnabled=null!==(o=null==a?void 0:a.editEnabled)&&void 0!==o&&o}}}catch(u){console.error("Error Retrieving Project Details for this Opportunity",u)}this.has_date_change_access||(this.deliveryStartDateEnabled=!1,this.deliveryEndDateEnabled=!1);break;case"quote_project_integration_config":this.project_access_field_config=e.quote_config_value;break;case"newly_added_quote_project_integration_config":this.newly_added_quote_project_integration_config=e.quote_config_value;break;case"quote_currency_change":this.quote_currency_change=e.quote_config_value;const d=c.find(e=>"quote_project_integration_config"===e.quote_config_name),p="S"===(null===(a=this.projectDetailsForOpportunity)||void 0===a?void 0:a.messType)&&(null===(r=null===(s=this.projectDetailsForOpportunity)||void 0===s?void 0:s.messData)||void 0===r?void 0:r.length)>0;if(this.quote_currency_change&&p&&d){const e=null===(l=d.quote_config_value)||void 0===l?void 0:l.find(e=>"quote_currency_change"===e.key);this.quote_currency_change=(null==e?void 0:e.editEnabled)||!1}break;case"quote_mp_nmp_mapping":this.manpowerNonManpowerMapping=e.quote_config_value||!1;break;case"quote_geograpical_revenue_region_mapping_enable":this.geographicRevenueRegionMapping=e.quote_config_value||!1;break;case"quote_milestone_tagging":this.quoteMilestoneTagging=e.quote_config_value||!1;break;case"quote_milestone_field_config":this.milestoneFieldConfig=e.quote_config_value||[];break;case"lumpsum_quote_config":this.lumpsum_quote_config=e.quote_config_value||[];break;case"quote_masking_configuration":this.masking_configuration=e.quote_config_value||null;break;case"quote_footer_splitup":this.quote_footer_splitup_configuration=e.quote_config_value||{};break;case"service_based_unit":this.service_based_unit=!0}this.sDMappingEnabled&&this.addButtonsList.length&&(this.addButtonsList=this.addButtonsList.filter(e=>1!=e.id)),this.initializeMasterData()})))},this.getQuoteAccessPrivilege=()=>{let e=this._landingPageService.checkCREnabled();this._quoteMainService.getQuoteAccessPrivilege(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{var n,i;t&&"S"==t.messType&&t.data?("CHANGE_REQUEST"===(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.quote_type)&&"CHANGE_REQUEST"==this.quoteForm.get("quoteType").value||(this.isCUDEnabled=t.data.cud_access_enabled||!1),("CHANGE_REQUEST"===(null===(i=this.wholeQuoteData)||void 0===i?void 0:i.quote_type)||"CHANGE_REQUEST"==this.quoteForm.get("quoteType").value&&e)&&(this.isCUDEnabled=!0)):this.isCUDEnabled=!1},e=>{this.isCUDEnabled=!1,console.log(e),this._toaster.showError("Error","Error in getting Quote Edit Config",this.opportunityService.longInterval)})},this.patchDateChange=(e,t)=>{for(const n of this.servicesFormArr.controls)for(const i of n.get("positions").controls){i.get(e).patchValue(t,{emitEvent:!1}),i.get("isPositionEffortChanged").patchValue(!0,{emitEvent:!1});for(const n of i.get("nmpData").controls)n.get(e).patchValue(t,{emitEvent:!1}),n.get("isPositionEffortChanged").patchValue(!0,{emitEvent:!1})}},this.initializeMasterData=()=>{this.toggleSpinner(!0),Promise.all([this.getNationalityList(),this.getWorkLocation(),this.getCurrencyList(),this.getPositionList(),this.getWorkExperience(),this.getTypeOfBusiness(),this.getPositionStatusList(),this.quoteApproveStatus(),this.getUOM(),this.getDiscountDetails(),this.getTaxDetails(),this.getDivisionList(),this.getSubDivisionList(),this.getEntity(),this.getOrgMappingList(),this.getOppMetaDetails(),this.getGeographicalRegion(),this.checkMilestoneApplicable()]).then(e=>Object(f.c)(this,void 0,void 0,(function*(){var e,t,n;if(this.resolveFieldConfig(),this.getUserFieldConfig(),this.quoteId&&(yield this.getQuoteDetails()),this.getQuoteAccessPrivilege(),this.getCalendarCombinationConfig(),this.getRevenueRegionCombinationConfig(),this.alloteFieldConfig(),yield this.getNonManpowerList(),yield this.getLicenseList(),this.wholeQuoteData&&(this.patchQuoteDetails(this.wholeQuoteData),this.wholeQuoteData&&"object"==typeof this.wholeQuoteData)){const e=this.quoteApproveStatusList.find(e=>e.id===this.wholeQuoteData.quote_status);this.wholeQuoteData.status_name=e?e.status_name:null}(null===(t=null===(e=this.wholeQuoteData)||void 0===e?void 0:e.change_request_merged)||void 0===t?void 0:t.length)&&(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.discountAndTax.length)&&this.quoteForm.get("quoteId").value&&this.quoteForm.get("totalOrderValue"),this.milestoneApplicable&&(this.getMilestoneListQB(),this.patchMilestoneDetailsUsingForm()),this.wEMappingEnabled&&this.getWEMappingList(),this.sDMappingEnabled&&this.getSDMappingList(),this.valueChangeSubscription.add(this.quoteForm.valueChanges.pipe(Object(w.a)(100),Object(q.a)(),Object(S.a)(this._onDestroy)).subscribe(e=>{e&&(this.isChangesMade=!0)})),this.toggleSpinner(!1)}))),this.quoteForm.get("deliveryEndDate").valueChanges.pipe(Object(q.a)(),Object(S.a)(this._onDestroy)).subscribe(e=>{var t,n;if(!e)return;const i=P()(e).toISOString();let o=this.getDurationInDays(null===(t=this.quoteForm.get("deliveryStartDate"))||void 0===t?void 0:t.value,e);if(this.quoteForm.get("quoteDuration").patchValue(o),(null===(n=this.quoteForm.get("quoteDuration"))||void 0===n?void 0:n.value)!=this.quoteDuration)for(const a of this.servicesFormArr.controls)for(const e of a.get("positions").controls){e.get("endDate").patchValue(i,{emitEvent:!1});for(const t of e.get("nmpData").controls)t.get("endDate").patchValue(i,{emitEvent:!1}),this.getPositionQuantity(t);this.getPositionQuantity(e)}}),this.quoteForm.get("deliveryStartDate").valueChanges.pipe(Object(q.a)(),Object(S.a)(this._onDestroy)).subscribe(e=>{if(!e)return;(this.isQuoteCreateMode?this.quoteForm.get("deliveryEndDate").value:this.initial_delivery_end_date)&&this.quoteForm.patchValue({deliveryEndDate:P()(e).add(this.quoteDuration,"days")},{emitEvent:!1});const t=P()(e).toISOString(),n=P()(e).add(this.quoteDuration,"days").toISOString();for(const i of this.servicesFormArr.controls)for(const e of i.get("positions").controls){e.get("startDate").patchValue(t,{emitEvent:!1}),e.get("endDate").patchValue(n,{emitEvent:!1});for(const i of e.get("nmpData").controls)i.get("startDate").patchValue(t,{emitEvent:!1}),i.get("endDate").patchValue(n,{emitEvent:!1})}})},this.resolveFieldConfig=()=>{for(const e of this.fieldConfig){if(e.isActive=this.checkFieldShouldbeRestricted(e.key),"totalRevenue"===e.key?this.isRevenueActive=e.isActive&&this.checkFieldShouldbeRestricted("totalRevenue"):"totalCost"===e.key?this.isCostActive=e.isActive&&this.checkFieldShouldbeRestricted("totalCost"):"totalGM"===e.key?this.isGMActive=e.isActive&&this.checkFieldShouldbeRestricted("totalGM"):"totalGMPercentage"===e.key&&(this.isGMPerActive=e.isActive&&this.checkFieldShouldbeRestricted("totalGMPercentage")),e.isActive){let t={key:e.key,columnName:e.columnName,label:e.label,fieldType:e.fieldType,isVisible:new u.j(e.isVisible||!1),col:e.col,isMandatory:this.mandatoryFields.includes(e.columnName)||e.isMandatory||!1,isForManpowerOnly:e.isForManpowerOnly,fieldPosition:e.position,decimalPart:e.decimalPart||0,enableVariableName:e.enableVariableName,isMasked:this.checkIfFieldShouldBeMasked(e),allowNegative:this.isQuoteCreateMode?this.quoteForm.get("allowNegativeNumber").value&&e.allowNegative:e.allowNegative};if("dropdown"==t.fieldType)switch(t.key){case"experience":t.masterData=this.experienceList,t.has_project_integrated=this.isWorkExperienceEnabled;break;case"workLocation":t.masterData=this.workLocationList,t.has_project_integrated=this.isWorkLocationEnabled;break;case"nationality":t.masterData=this.nationalityList,t.has_project_integrated=this.isNationalityEnabled;break;case"revenueRegion":t.masterData=this.geographicalRegionList,t.has_project_integrated=this.isRevenueRegionEnabled;break;case"entity":t.masterData=this.entityList;break;case"division":t.masterData=this.divisionList,t.isMasterDataFromPosition=!0;break;case"subDivision":t.masterData=this.subDivisionList,t.isMasterDataFromPosition=!0;break;case"unit":t.masterData=this.unitList,t.has_project_integrated=this.isUnitEnabled;break;case"typeOfBusiness":t.masterData=this.businessTypeList,t.has_project_integrated=this.isTypeOfBusinessEnabled;break;case"positionStatus":t.masterData=this.positionStatusList,t.has_project_integrated=this.isPositionStatusEnabled;break;case"milestone":t.masterData=this.milestoneList,t.has_project_integrated=this.isMilestoneEnabled;break;default:t.masterData=[]}if("number"==t.fieldType)switch(t.key){case"quantity":t.has_project_integrated=this.isQuantityEnabled;break;case"noOfResources":t.has_project_integrated=this.isNoOfResourcesEnabled;break;case"ratePerUnit":t.has_project_integrated=this.isRatePerUnitEnabled;break;case"costPerUnit":t.has_project_integrated=this.isCostPerUnitEnabled}"positionId"!=t.key?this.customizeFields.push(t):this.positionField={label:t.label,col:t.col}}this.customizeFields=this.customizeFields.filter(this.milestoneApplicable?e=>"positionStatus"!=e.key:e=>"milestone"!=e.key),console.log(this.customizeFields,"All field Configuration")}this.sortFieldConfig()},this.sortFieldConfig=()=>{this.customizeFields.sort((e,t)=>null==e.fieldPosition||null==t.fieldPosition?0:e.fieldPosition<t.fieldPosition?-1:e.fieldPosition>t.fieldPosition?1:void 0)},this.toggleSpinner=(e=!1)=>{e?this.spinner.show():this.spinner.hide(),this.isDataLoading=e},this.getQuoteDetails=()=>new Promise((e,t)=>{this._quoteMainService.getQuoteDetails(this.quoteId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{var n;if(t&&t.is_rds_peak&&(this._toaster.showError("Error",t.messText||"System unavailable Please Try Again Later",3e3),e(!0)),t&&"S"==t.messType&&t.data){this.initial_delivery_start_date=t.data.delivery_start_date,this.initial_delivery_end_date=t.data.delivery_end_date,this.quoteDuration=this.getDurationInDays(this.initial_delivery_start_date,this.initial_delivery_end_date),this.isQuoteActive=1===t.data.flag,this.isChangeRequestActive=1===t.data.change_request_flag,this.wholeQuoteData=t.data;let e=[...this.addButtonsList];this.addButtonsList=[];for(const t of e){const e=t.notApplicableForQuoteType||[],i=(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.quote_type)||this.quoteForm.get("quoteType").value,o=!e.includes(i);t.isActive&&o&&this.addButtonsList.push(t)}this.sDMappingEnabled&&this.addButtonsList.length&&(this.addButtonsList=this.addButtonsList.filter(e=>1!=e.id))}else this._toaster.showError("Error","Error in getting Quote details",this.opportunityService.longInterval),this.navigateToLandingPage();e(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote details",this.opportunityService.longInterval),this.navigateToLandingPage(),t(e)})}),this.patchQuoteDetails=e=>{this.isEditMode=!1,this.quoteForm.patchValue({quoteId:e.quote_header_id,quoteName:e.quote_name,quoteCurrency:e.quote_currency,quoteCurrencyId:e.quote_currency_id,initialCurrencyId:e.quote_currency_id,deliveryStartDate:e.delivery_start_date,deliveryEndDate:e.delivery_end_date,totalRevenue:e.quote_revenue_amount,totalCost:e.quote_cost_amount,totalGM:(e.quote_revenue_amount||0)-(e.quote_cost_amount||0),totalOrderValue:e.quote_amount,version:e.version,activeQuote:1===e.flag,quoteOpportunityHasParent:1===e.has_parent_opportunity,enableMilestoneTagging:1===e["has_milestone_tagging "],serviceOppTypeId:e.service_type,quoteType:e.quote_type,activeChangeRequest:1===e.change_request_flag,allowNegativeNumber:1===e.allow_negative_number,quoteDuration:this.quoteDuration}),this.checkNegativeAllocation(),this.calculateOverallGMPercentage(this.quoteForm);const t=e.service;if(t.length){this.servicesFormArr.clear();const e=this.quoteForm.get("discounts"),n=this.quoteForm.get("taxes");e.clear(),n.clear(),t.forEach((e,t)=>{var n,i,o,a;let s=this.getServicesFormGroup();s.patchValue({quoteServiceId:e.quote_service_id,serviceId:e.service_header_id,serviceName:e.quote_service_name,serviceTypeId:e.service_type_id,serviceRevenue:e.service_revenue_amount,serviceCost:e.service_cost_amount,isFixedRate:e.is_fixed_rate,isSection:null==e.service_header_id,isDeleteServiceEnabled:this.isDeleteServiceEnabled},{emitEvent:!1});let r=s.get("positions");if(r.clear(),0==e.position.length){const e=this.getPositionFormGroup();this.resolveSDMapping(s,e),r.push(e)}else for(const[l,c]of e.position.entries()){let e=this.getPositionFormGroup(2===c.resource_type_id,3===c.resource_type_id);if(c.position_items&&c.position_items.length){let t=e.get("nmpData");for(const[e,o]of c.position_items.entries()){let a=this.getPositionFormGroup(!0);const s=this.unitList.find(e=>e.id===o.unit_id);a.patchValue({quotePositionId:o.qp_item_id,positionId:o.nmp_id,positionName:o.description,noOfResources:o.resource||0,ratePerUnit:o.rate_per_unit||0,originalRatePerUnit:o.original_rate||0,costPerUnit:o.cost_per_unit||0,unit:o.unit_id,revenueRegion:o.revenue_region,typeOfBusiness:o.business_type||(null===(n=this.quoteForm)||void 0===n?void 0:n.get("quoteBusinessType").value)||(null===(i=this.wholeQuoteData)||void 0===i?void 0:i.business_type),positionStatus:o.position_status,milestone:o.milestone_id,milestoneDetails:o.milestone||[],lastUnit:o.unit_id,entity:o.entity,division:o.division,subDivision:o.sub_division,workLocation:o.work_location,nationality:o.nationality,quantity:o.quantity||0,totalCost:o.qpi_amount,totalRevenue:o.qpi_revenue_amount,totalGM:(o.qpi_revenue_amount||0)-(o.qpi_amount||0),startDate:o.start_date,endDate:o.end_date,positionIndex:e,defaultDebounce:s&&s.is_value_fixed?500:100,rcValue:o.rc_value||0,isPositionFieldEnabled:this.isPositionFieldEnabled,isUnitEnabled:this.isUnitEnabled,isWorkLocationEnabled:this.isWorkLocationEnabled,isQuantityEnabled:this.isQuantityEnabled,isNoOfResourcesEnabled:this.isNoOfResourcesEnabled,isRatePerUnitEnabled:this.isRatePerUnitEnabled,isCostPerUnitEnabled:this.isCostPerUnitEnabled,isAddServiceEnabled:this.isAddServiceEnabled,isDeleteServiceEnabled:this.isDeleteServiceEnabled,isAddPositionEnabled:this.isAddPositionEnabled,isAddPositionInlineEnabled:this.isAddPositionInlineEnabled,isDeletePositionEnabled:this.isDeletePositionEnabled,isClonePositionEnabled:this.isClonePositionEnabled,isCustomiseSlotEnabled:this.isCustomiseSlotEnabled,interServicePositionDragNDrop:this.interServicePositionDragNDrop,isTypeOfBusinessEnabled:this.isTypeOfBusinessEnabled,isRevenueRegionEnabled:this.isRevenueRegionEnabled,isPositionStatusEnabled:this.isPositionStatusEnabled,isMilestoneEnabled:this.isMilestoneEnabled,isNationalityEnabled:this.isNationalityEnabled,isWorkExperienceEnabled:this.isWorkExperienceEnabled,isAddSectionEnabled:this.isAddSectionEnabled,isPositionStatusToLost:this.isPositionStatusToLost},{emitEvent:!1}),this.getCurrentPositionData(a,o.nmp_id),this.calculateOverallGMPercentage(a),this.resolveOrgMapping("entity",a.value,a),this.resolveOrgMapping("division",a.value,a),t.push(a)}}const t=this.unitList.concat(this.otherUnitList).find(e=>e.id===c.unit_id);e.patchValue({quotePositionId:c.quote_position_id,positionId:c.position,positionName:c.position_name,noOfResources:c.resource_count||0,experience:c.work_experience,workLocation:c.work_location,nationality:c.nationality,entity:c.entity,division:c.division,subDivision:c.sub_division,unit:c.unit_id,revenueRegion:c.revenue_region,typeOfBusiness:c.business_type||(null===(o=this.quoteForm)||void 0===o?void 0:o.get("quoteBusinessType").value)||(null===(a=this.wholeQuoteData)||void 0===a?void 0:a.business_type),positionStatus:c.position_status,milestone:c.milestone_id,milestoneDetails:c.milestone||[],lastUnit:c.unit_id,quantity:c.quantity||0,ratePerUnit:c.rate_per_unit||0,originalRatePerUnit:c.original_rate||0,costPerUnit:c.cost_per_unit||0,totalRevenue:c.rate_revenue_amount||0,totalCost:c.rate_cost_amount||0,totalGM:(c.rate_revenue_amount||0)-(c.rate_cost_amount||0),positionIndex:l,startDate:c.start_date,endDate:c.end_date,defaultDebounce:t&&t.is_value_fixed?500:100,rcValue:c.rc_value||0,isPositionFieldEnabled:this.isPositionFieldEnabled,isUnitEnabled:this.isUnitEnabled,isWorkLocationEnabled:this.isWorkLocationEnabled,isQuantityEnabled:this.isQuantityEnabled,isNoOfResourcesEnabled:this.isNoOfResourcesEnabled,isRatePerUnitEnabled:this.isRatePerUnitEnabled,isCostPerUnitEnabled:this.isCostPerUnitEnabled,isAddServiceEnabled:this.isAddServiceEnabled,isDeleteServiceEnabled:this.isDeleteServiceEnabled,isAddPositionEnabled:this.isAddPositionEnabled,isAddPositionInlineEnabled:this.isAddPositionInlineEnabled,isDeletePositionEnabled:this.isDeletePositionEnabled,isClonePositionEnabled:this.isClonePositionEnabled,isCustomiseSlotEnabled:this.isCustomiseSlotEnabled,interServicePositionDragNDrop:this.interServicePositionDragNDrop,isTypeOfBusinessEnabled:this.isTypeOfBusinessEnabled,isRevenueRegionEnabled:this.isRevenueRegionEnabled,isPositionStatusEnabled:this.isPositionStatusEnabled,isMilestoneEnabled:this.isMilestoneEnabled,isNationalityEnabled:this.isNationalityEnabled,isWorkExperienceEnabled:this.isWorkExperienceEnabled,isAddSectionEnabled:this.isAddSectionEnabled,isPositionStatusToLost:this.isPositionStatusToLost},{emitEvent:!1}),this.getCurrentPositionData(e,c.position),this.resolveOrgMapping("entity",e.value,e),this.resolveOrgMapping("division",e.value,e),this.calculateOverallGMPercentage(e),r.push(e)}this.servicesFormArr.push(s),this.addPositionDropZone()}),console.log("Milestone Form",this.milestoneFormArray.value)}if(e.discountAndTax&&e.discountAndTax.length){const t=this.quoteForm.get("discounts"),n=this.quoteForm.get("taxes");t.clear(),n.clear();for(const i of e.discountAndTax)if("D"==i.type){const e=this.getDiscountsFormGroup();e.patchValue({dtItemId:i.dt_item_id,discountId:i.tax_discount_id,discountName:i.name,discountValue:i.item_amount,isCustom:i.is_custom||!1},{emitEvent:!1}),e.patchValue({discountPercentage:i.percentage}),i.is_custom?e.get("discountPercentage").enable({emitEvent:!1}):e.get("discountPercentage").disable({emitEvent:!1}),t.push(e)}else if("T"==i.type){const e=this.getTaxesFormGroup();e.patchValue({dtItemId:i.dt_item_id,taxId:i.tax_discount_id,taxName:i.name,taxValue:i.item_amount},{emitEvent:!1}),e.patchValue({taxPercentage:i.percentage}),n.push(e)}}this.calculateOverallRevenue(),this.calculateOverallCost(),setTimeout(()=>{this.scrollContainer=this.scrollFrame.nativeElement},10),setTimeout(()=>{this.isChangesMade=!1},1e3)},this.patchMilestoneDetails=e=>{const t=e.service;let n=new Map;if(t.length){t.forEach(e=>{e.position.forEach(e=>{const t=e.milestone;t&&t.length>0&&t.forEach(t=>{n.has(t.milestone_id)||n.set(t.milestone_id,{milestone:t,positions:[]}),n.get(t.milestone_id).positions.push(e)})})}),this.milestoneFormGroup=this.fb.group({allMilestones:this.fb.array([])});let e=this.milestoneFormGroup.get("allMilestones");n.forEach((t,n)=>{let i=this.getMilestoneFromGroup();i.patchValue({milestoneId:t.milestone.milestone_id,milestoneName:t.milestone.milestone_name,milestoneStartDate:t.milestone.milestone_start_date,milestoneEndDate:t.milestone.milestone_end_date,milestoneTotalRevenue:t.milestone.milestone_revenue||0,milestoneTotalCost:t.milestone.milestone_cost||0,activeQuote:!1,milestoneIndex:n});let o=i.get("positions");t.positions.forEach((e,t)=>{let n=this.getPositionFormGroup();n.patchValue({quotePositionId:e.quote_position_id,positionId:e.position,positionName:e.position_name,noOfResources:e.resource_count||0,experience:e.work_experience,workLocation:e.work_location,nationality:e.nationality,entity:e.entity,division:e.division,subDivision:e.sub_division,unit:e.unit_id,typeOfBusiness:e.business_type,milestone:e.milestone_id,milestoneDetails:e.milestone||[],lastUnit:e.unit_id,quantity:e.quantity||0,ratePerUnit:e.rate_per_unit||0,originalRatePerUnit:e.original_rate||0,costPerUnit:e.cost_per_unit||0,totalRevenue:e.rate_revenue_amount||0,totalCost:e.rate_cost_amount||0,totalGM:(e.rate_revenue_amount||0)-(e.rate_cost_amount||0),startDate:e.start_date,positionIndex:t,endDate:e.end_date,defaultDebounce:500,rcValue:e.rc_value||0,isPositionFieldEnabled:this.isPositionFieldEnabled,isUnitEnabled:this.isUnitEnabled,isWorkLocationEnabled:this.isWorkLocationEnabled,isQuantityEnabled:this.isQuantityEnabled,isNoOfResourcesEnabled:this.isNoOfResourcesEnabled,isRatePerUnitEnabled:this.isRatePerUnitEnabled,isCostPerUnitEnabled:this.isCostPerUnitEnabled,isAddServiceEnabled:this.isAddServiceEnabled,isDeleteServiceEnabled:this.isDeleteServiceEnabled,isAddPositionEnabled:this.isAddPositionEnabled,isAddPositionInlineEnabled:this.isAddPositionInlineEnabled,isDeletePositionEnabled:this.isDeletePositionEnabled,isClonePositionEnabled:this.isClonePositionEnabled,isCustomiseSlotEnabled:this.isCustomiseSlotEnabled,interServicePositionDragNDrop:this.interServicePositionDragNDrop,isTypeOfBusinessEnabled:this.isTypeOfBusinessEnabled,isMilestoneEnabled:this.isMilestoneEnabled,isNationalityEnabled:this.isNationalityEnabled,isWorkExperienceEnabled:this.isWorkExperienceEnabled,isAddSectionEnabled:this.isAddSectionEnabled},{emitEvent:!1}),o.push(n)}),e.push(i),this.positionMilestoneDropZones.push("milestoneDropZone"+(e.length-1)),this.quoteForm.get("milestoneArray").push(i)})}},this.patchMilestoneDetailsUsingForm=()=>{let e=new Map;if(this.servicesFormArr.controls){this.servicesFormArr.controls.forEach((t,n)=>{t.controls.positions.controls.forEach((t,n)=>{const i=t.controls.milestoneDetails.value;i&&i.length>0&&i.forEach(n=>{e.has(n.milestone_id)||e.set(n.milestone_id,{milestone:n,positions:[]}),e.get(n.milestone_id).positions.push(t)})})}),this.milestoneFormGroup=this.fb.group({allMilestones:this.fb.array([])});let t=this.milestoneFormGroup.get("allMilestones");e.forEach((e,n)=>{var i,o,a,s,r;let l=this.getMilestoneFromGroup();l.patchValue({milestoneId:e.milestone.milestone_id||Object(M.a)(),milestoneName:e.milestone.milestone_name,milestoneStartDate:(null===(i=e.milestone)||void 0===i?void 0:i.milestone_start_date)||(null===(o=e.milestone)||void 0===o?void 0:o.start_date),milestoneEndDate:(null===(a=e.milestone)||void 0===a?void 0:a.milestone_end_date)||(null===(s=e.milestone)||void 0===s?void 0:s.end_date),milestoneTotalRevenue:e.milestone.milestone_revenue||0,milestoneTotalCost:e.milestone.milestone_cost||0,activeQuote:!1,milestoneIndex:n,isMasterMilestone:(null===(r=e.milestone)||void 0===r?void 0:r.is_master)||0});let c=l.get("positions");e.positions.forEach((e,t)=>{var n,i,o,a,s,r,l,u,d,p,m,h,g,v,f,y,_,b,C,x,E,S,w,q,D;let O=this.getPositionFormGroup();O.patchValue({quotePositionId:null===(n=e.get("quotePositionId"))||void 0===n?void 0:n.value,positionId:null===(i=e.get("positionId"))||void 0===i?void 0:i.value,positionName:null===(o=e.get("positionName"))||void 0===o?void 0:o.value,noOfResources:(null===(a=e.get("noOfResources"))||void 0===a?void 0:a.value)||0,experience:null===(s=e.get("experience"))||void 0===s?void 0:s.value,workLocation:null===(r=e.get("workLocation"))||void 0===r?void 0:r.value,nationality:null===(l=e.get("nationality"))||void 0===l?void 0:l.value,entity:null===(u=e.get("entity"))||void 0===u?void 0:u.value,division:null===(d=e.get("division"))||void 0===d?void 0:d.value,subDivision:null===(p=e.get("subDivision"))||void 0===p?void 0:p.value,unit:null===(m=e.get("unit"))||void 0===m?void 0:m.value,typeOfBusiness:null===(h=e.get("typeOfBusiness"))||void 0===h?void 0:h.value,milestone:null===(g=e.get("milestone"))||void 0===g?void 0:g.value,milestoneDetails:(null===(v=e.get("milestoneDetails"))||void 0===v?void 0:v.value)||[],lastUnit:null===(f=e.get("lastUnit"))||void 0===f?void 0:f.value,quantity:(null===(y=e.get("quantity"))||void 0===y?void 0:y.value)||0,ratePerUnit:(null===(_=e.get("ratePerUnit"))||void 0===_?void 0:_.value)||0,costPerUnit:(null===(b=e.get("costPerUnit"))||void 0===b?void 0:b.value)||0,totalRevenue:(null===(C=e.get("totalRevenue"))||void 0===C?void 0:C.value)||0,totalCost:(null===(x=e.get("totalCost"))||void 0===x?void 0:x.value)||0,totalGM:((null===(E=e.get("totalRevenue"))||void 0===E?void 0:E.value)||0)-((null===(S=e.get("totalCost"))||void 0===S?void 0:S.value)||0),startDate:null===(w=e.get("startDate"))||void 0===w?void 0:w.value,endDate:null===(q=e.get("endDate"))||void 0===q?void 0:q.value,defaultDebounce:500,rcValue:(null===(D=e.get("rcValue"))||void 0===D?void 0:D.value)||0,isPositionFieldEnabled:this.isPositionFieldEnabled,isUnitEnabled:this.isUnitEnabled,isWorkLocationEnabled:this.isWorkLocationEnabled,isQuantityEnabled:this.isQuantityEnabled,isNoOfResourcesEnabled:this.isNoOfResourcesEnabled,isRatePerUnitEnabled:this.isRatePerUnitEnabled,isCostPerUnitEnabled:this.isCostPerUnitEnabled,isAddServiceEnabled:this.isAddServiceEnabled,isDeleteServiceEnabled:this.isDeleteServiceEnabled,isAddPositionEnabled:this.isAddPositionEnabled,isAddPositionInlineEnabled:this.isAddPositionInlineEnabled,isDeletePositionEnabled:this.isDeletePositionEnabled,isClonePositionEnabled:this.isClonePositionEnabled,isCustomiseSlotEnabled:this.isCustomiseSlotEnabled,interServicePositionDragNDrop:this.interServicePositionDragNDrop,isTypeOfBusinessEnabled:this.isTypeOfBusinessEnabled,isMilestoneEnabled:this.isMilestoneEnabled,isNationalityEnabled:this.isNationalityEnabled,isWorkExperienceEnabled:this.isWorkExperienceEnabled,isAddSectionEnabled:this.isAddSectionEnabled,positionIndex:t},{emitEvent:!1}),c.push(O)}),t.push(l),this.positionMilestoneDropZones.push("milestoneDropZone"+(t.length-1))})}},this.getNationalityList=()=>new Promise((e,t)=>{this.masterDataService.nationality.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.nationalityList=t,e(!0)})}),this.getGeographicalRegion=()=>new Promise((e,t)=>{this.masterDataService.geographicalRegion.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.geographicalRegionList=t,e(!0)})}),this.getWorkLocation=()=>new Promise((e,t)=>{this.masterDataService.workLocation.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.workLocationList=t,e(!0)})}),this.getCurrencyList=()=>new Promise((e,t)=>{this.masterDataService.currency.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.currencyList=t,e(!0)})}),this.getPositionList=()=>new Promise((e,t)=>{this.masterDataService.position.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.positionList=t,this.skillList=t.filter(e=>1===e.is_for_quote),e(!0)})}),this.getWorkExperience=()=>new Promise((e,t)=>{this.masterDataService.workExperience.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.experienceList=t,e(!0)})}),this.getTypeOfBusiness=()=>new Promise((e,t)=>{this.masterDataService.typeOfBusiness.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.businessTypeList=t,e(!0)})}),this.getMilestoneList=()=>new Promise((e,t)=>{this.masterDataService.milestone.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.milestoneList=t,e(!0)})}),this.quoteApproveStatus=()=>new Promise((e,t)=>{this.masterDataService.quoteApproveStatus.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.quoteApproveStatusList=t,e(!0)})}),this.getPositionStatusList=()=>new Promise((e,t)=>{this.masterDataService.positionStatus.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.positionStatusList=t,e(!0)})}),this.getUOM=()=>new Promise((e,t)=>{this.masterDataService.uom.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{for(const e of t){if(e.applicable_resource_type){let t=JSON.parse(e.applicable_resource_type);t.includes(2)&&this.nonManPowerUnit.push(e),t.includes(3)&&this.licenseUnit.push(e),t.includes(1)&&this.manPowerUnit.push(e)}if(this.service_based_unit&&e.applicable_service_type){let t=JSON.parse(e.applicable_service_type);this.oppServiceType&&!t.includes(this.oppServiceType)&&(this.nonManPowerUnit=this.nonManPowerUnit.filter(t=>t!==e),this.licenseUnit=this.licenseUnit.filter(t=>t!==e),this.manPowerUnit=this.manPowerUnit.filter(t=>t!==e))}e.is_for_position?this.unitList.push(e):this.otherUnitList.push(e)}e(!0)})}),this.getTaxDetails=()=>new Promise((e,t)=>{this.masterDataService.tax.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.taxesList=t,e(!0)})}),this.getDiscountDetails=()=>new Promise((e,t)=>{this.masterDataService.discount.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.discountsList=t,e(!0)})}),this.getNonManpowerList=()=>new Promise((e,t)=>{var n;this._quoteMainService.getNonManPowerList(this.opportunityId,this.defaultCurrency,this.quoteForm.get("quoteCurrency").value||(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.quote_currency),this.conversionTypeId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data&&t.data.length&&(this.nmpList=t.data),e(!0)})}),this.getDivisionList=()=>new Promise((e,t)=>{this.masterDataService.division.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.divisionList=t,e(!0)},e=>{console.log(e),t(e)})}),this.getSubDivisionList=()=>new Promise((e,t)=>{this.masterDataService.subDivision.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.subDivisionList=t,e(!0)})}),this.getEntity=()=>new Promise((e,t)=>{this.masterDataService.entity.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.entityList=t,e(!0)})}),this.getLicenseList=()=>new Promise((e,t)=>{var n;this._quoteMainService.getLicenseList(this.opportunityId,this.defaultCurrency,this.quoteForm.get("quoteCurrency").value||(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.quote_currency),this.conversionTypeId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data&&t.data.length&&(this.licenseList=t.data),e(!0)})}),this.getOrgMappingList=()=>new Promise((e,t)=>{this.masterDataService.orgMapping.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.orgMappingList=t,e(!0)})}),this.getWEMappingList=()=>new Promise((e,t)=>{this.masterDataService.wEMapping.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.wEMappingList=t,e(!0)})}),this.getSDMappingList=()=>new Promise((e,t)=>{this.masterDataService.sDMapping.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.sDMappingList=t,e(!0)})}),this.initializeForm=()=>{this.quoteForm=this.fb.group({quoteId:[null],quoteName:[null,u.H.required],quoteCurrency:[null,u.H.required],quoteCurrencyId:[null],initialCurrencyId:[null],deliveryStartDate:[null],deliveryEndDate:[null],quoteOpportunityHasParent:[null],services:this.fb.array([]),totalRevenue:[0],totalCost:[0],totalGM:[0],totalGMPercentage:[0],totalOrderValue:[0],discounts:this.fb.array([]),totalDiscount:[0],taxes:this.fb.array([]),totalTax:[0],version:[1],activeQuote:[!1],quoteBusinessType:[null],enableMilestoneTagging:[!1],serviceOppTypeId:[!1],milestoneArray:this.fb.array([]),activeChangeRequest:[!1],quoteType:[null],allowNegativeNumber:[!1],manpowerRevenue:[0],manpowerCost:[0],nonManpowerRevenue:[0],nonManpowerCost:[0],licenseRevenue:[0],licenseCost:[0],quoteDuration:[0]}),this.milestoneFormArray=this.fb.array([])},this.getServicesFormGroup=()=>this.fb.group({quoteServiceId:[null],serviceId:[null],serviceName:[null,u.H.required],serviceCost:[null],serviceRevenue:[null],serviceTypeId:[null],isFixedRate:[null],positions:this.fb.array([]),isSection:[!1],isDeleteServiceEnabled:[!1]}),this.getPositionFormGroup=(e=!1,t=!1)=>{var n,i;const o=e||t?u.H.nullValidator:u.H.required;let a=(null===(n=this.quoteForm)||void 0===n?void 0:n.get("quoteBusinessType").value)||(null===(i=this.wholeQuoteData)||void 0===i?void 0:i.business_type);const s=this.fb.group({quotePositionId:[null],positionId:[null,u.H.required],positionName:[null],noOfResources:[1,this.mandatoryFields.includes(this.resolveFieldMapping("noOfResources"))?o:u.H.nullValidator],experience:[null,this.mandatoryFields.includes(this.resolveFieldMapping("experience"))?o:u.H.nullValidator],workLocation:[null,this.mandatoryFields.includes(this.resolveFieldMapping("workLocation"))?o:u.H.nullValidator],nationality:[null,this.mandatoryFields.includes(this.resolveFieldMapping("nationality"))?o:u.H.nullValidator],division:[null,this.mandatoryFields.includes(this.resolveFieldMapping("division"))?o:u.H.nullValidator],subDivision:[null,this.mandatoryFields.includes(this.resolveFieldMapping("subDivision"))?o:u.H.nullValidator],entity:[null,this.mandatoryFields.includes(this.resolveFieldMapping("entity"))?o:u.H.nullValidator],unit:[null,u.H.required],typeOfBusiness:[null,this.mandatoryFields.includes(this.resolveFieldMapping("typeOfBusiness"))?o:u.H.nullValidator],revenueRegion:[null,this.mandatoryFields.includes(this.resolveFieldMapping("revenueRegion"))?o:u.H.nullValidator],positionStatus:[null,this.mandatoryFields.includes(this.resolveFieldMapping("positionStatus"))?o:u.H.nullValidator],milestone:[null,this.mandatoryFields.includes(this.resolveFieldMapping("milestone"))?o:u.H.nullValidator],milestoneDetails:[null],lastUnit:[null],ratePerUnit:[0,u.H.required],originalRatePerUnit:[0],costPerUnit:[0],quantity:[0,u.H.required],isQuantityChanged:[!1],totalRevenue:[0],totalCost:[0],totalGM:[{value:0,disabled:!0}],totalGMPercentage:[{value:0,disabled:!0}],isNonManpower:[e],isLicense:[t],startDate:[this.quoteForm.get("deliveryStartDate").value],endDate:[this.quoteForm.get("deliveryEndDate").value],nmpData:this.fb.array([]),isUnitChanged:[!1],isPositionEffortChanged:[!1],isPositionValueChanged:[!1],defaultDebounce:[100],divisionMasterData:[this.divisionList],subDivisionMasterData:[this.subDivisionList],rcValue:[0],isPositionFieldEnabled:[!1],isUnitEnabled:[!1],isWorkLocationEnabled:[!1],isQuantityEnabled:[!1],isNoOfResourcesEnabled:[!1],isRatePerUnitEnabled:[!1],isCostPerUnitEnabled:[!1],isAddServiceEnabled:[!1],isDeleteServiceEnabled:[!1],isAddPositionEnabled:[!1],isAddPositionInlineEnabled:[!1],isDeletePositionEnabled:[!1],isClonePositionEnabled:[!1],isCustomiseSlotEnabled:[!1],interServicePositionDragNDrop:[!1],isTypeOfBusinessEnabled:[!1],isRevenueRegionEnabled:[!1],isPositionStatusEnabled:[!1],isNationalityEnabled:[!1],isWorkExperienceEnabled:[!1],isAddSectionEnabled:[!1],isPositionStatusToLost:[!1],positionDuplicationLoader:[!1],calendarId:[null],isMilestoneEnabled:[!1],positionIndex:[null]});if(this.valueChangeSubscription.add(s.get("positionId").valueChanges.pipe(Object(w.a)(10),Object(S.a)(this._onDestroy)).subscribe(e=>{if(null!=e){const t=this.getCurrentPositionData(s,e);if(t)if(s.get("positionName").patchValue(t.name),s.get("isNonManpower").value||s.get("isLicense").value){const e={entity:t.entity,division:t.division,subDivision:t.sub_division,experience:t.work_experience,workLocation:t.work_location,nationality:t.nationality};this.wEMappingEnabled&&delete e.entity,this.sDMappingEnabled&&delete e.division,s.patchValue(e,{emitEvent:!1}),s.patchValue({ratePerUnit:t.revenue,costPerUnit:t.cost,rcValue:t.revenue})}else this.getPositionRate(s)}else s.get("positionName").patchValue(null),s.get("typeOfBusiness").patchValue(a||null)})),this.valueChangeSubscription.add(s.get("noOfResources").valueChanges.pipe(Object(q.a)(),Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{if(""===e||null===e)return s.get("noOfResources").patchValue(0);this.calculatePositionRevenue(s),this.calculatePositionCost(s)})),this.valueChangeSubscription.add(s.get("unit").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{e&&e!=s.get("lastUnit").value&&(null!=s.get("lastUnit").value&&s.get("quantity").value>0?this._util.openConfirmationSweetAlertWithCustom("Do you want to change Unit ?","Doing this will reset the quantity").then(t=>{t?(s.patchValue({quantity:1,lastUnit:e,isUnitChanged:!0}),this.getPositionQuantity(s)):s.patchValue({unit:s.get("lastUnit").value,isUnitChanged:!1},{emitEvent:!1})}):(s.patchValue({lastUnit:e}),this.getPositionQuantity(s)));const t=this.unitList.find(t=>t.id===e);t&&t.is_value_fixed?s.get("defaultDebounce").patchValue(500):s.get("defaultDebounce").patchValue(100)})),this.valueChangeSubscription.add(s.get("milestone").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{var t;if(e){const n=this.milestoneList.find(t=>t.id===e);n&&(null===(t=s.get("milestoneDetails"))||void 0===t||t.patchValue([n],{emitEvent:!1})),this.patchMilestoneDetailsUsingForm()}else console.log("Milestone is not selected or invalid.")})),this.valueChangeSubscription.add(s.get("ratePerUnit").valueChanges.pipe(Object(q.a)(),Object(w.a)(500),Object(S.a)(this._onDestroy)).subscribe(e=>this.quoteForm.get("allowNegativeNumber").value||""!==e&&null!==e?(e=e.toString().replace(/,/g,""),s.get("originalRatePerUnit").patchValue(Number(e)),this.quoteForm.get("discounts").value.length?(this._toaster.showWarning("Discount for the quote is indentified!","The entered rate will be altered according to the applied discount",4500),void setTimeout(()=>{this._toaster.showWarning("Discount for the quote is identified!","The entered rate will be altered according to the applied discount",4500),this.calculateTotalDiscount(),this.calculatePositionRevenue(s)},1500)):void this.calculatePositionRevenue(s)):s.get("ratePerUnit").patchValue(0))),this.valueChangeSubscription.add(s.get("costPerUnit").valueChanges.pipe(Object(q.a)(),Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{if(""===e||null===e)return s.get("costPerUnit").patchValue(0);this.calculatePositionCost(s)})),this.valueChangeSubscription.add(s.get("quantity").valueChanges.pipe(Object(q.a)(),Object(D.a)(()=>Object(E.a)(s.get("defaultDebounce").value)),Object(S.a)(this._onDestroy)).subscribe(e=>{if(!this.quoteForm.get("allowNegativeNumber").value&&(""===e||null===e))return s.get("quantity").patchValue(0);s.get("isQuantityChanged").patchValue(!0),s.contains("positionEffortData")&&s.get("positionEffortData").patchValue(null);const t=this.unitList.concat(this.otherUnitList).find(e=>e.id===s.get("unit").value);if(t&&t.is_value_fixed){const n=t.unit_value_in_hrs;"string"==typeof e&&(e=e.replace(/,/g,"")),/^\d*\.?\d*$/.test(e)&&s.get("quantity").patchValue(Math.ceil(parseFloat(e)/n)*n)}this.calculatePositionRevenue(s),this.calculatePositionCost(s)})),this.valueChangeSubscription.add(s.get("totalRevenue").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{s.get("isPositionValueChanged").patchValue(!0),s.get("totalGM").patchValue(this.convertToFixed(e-s.get("totalCost").value)),this.calculateOverallGMPercentage(s)})),this.valueChangeSubscription.add(s.get("totalCost").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{s.get("isPositionValueChanged").patchValue(!0),s.get("totalGM").patchValue(this.convertToFixed(s.get("totalRevenue").value-e)),this.calculateOverallGMPercentage(s)})),this.valueChangeSubscription.add(s.get("totalGM").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{this.calculateOverallGM()})),this.valueChangeSubscription.add(s.get("entity").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{var t,n;if(this.resolveOrgMapping("entity",s.value,s),this.geographicRevenueRegionMapping){const i=this.checkAndAllocateRevenueRegion((null===(t=this.wholeQuoteData)||void 0===t?void 0:t.sales_region)||this.oppQuoteDetails.sales_region,e);null===(n=s.get("revenueRegion"))||void 0===n||n.patchValue(i,{emitEvent:!1}),i?s.get("revenueRegion").disable({emitEvent:!1}):s.get("revenueRegion").enable({emitEvent:!1})}})),this.valueChangeSubscription.add(s.get("division").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{this.resolveOrgMapping("division",s.value,s)})),this.valueChangeSubscription.add(s.get("positionStatus").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t,n;s.get("isPositionStatusToLost"),t=this.projectDetailsForOpportunity;let i=this.positionStatusList.filter(e=>1===e.allocation_restrict).map(e=>e.id);if(4===e||i.includes(e)){let e=null===(n=this.quoteForm.get("quoteId"))||void 0===n?void 0:n.value;e&&(this.allocation_details=yield this._quoteMainService.checkQuoteAllocation(e),yield this.calculateOverallRevenue(s))}else this.calculateOverallRevenue()})))),this.wEMappingEnabled&&this.valueChangeSubscription.add(s.get("workLocation").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{if(e&&this.wEMappingList.length){const t=this.wEMappingList.find(t=>t.work_location_id===e);s.get("entity").patchValue(t?t.entity_id:null,{emitEvent:!1})}else s.get("entity").patchValue(null,{emitEvent:!1})})),!e&&!t)for(const l of this.mandatoryFields)this.valueChangeSubscription.add(s.get(this.resolveFieldMapping(l)).valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{this.getPositionRate(s)}));const r=()=>{var e,t,n,i,o,a;const r=(null===(e=s.get("entity"))||void 0===e?void 0:e.value)||0,l=null===(t=this.wholeQuoteData)||void 0===t?void 0:t.opp_service_type_id,c=(null===(n=s.get("workLocation"))||void 0===n?void 0:n.value)||0,u=(null===(i=s.get("isNonManpower"))||void 0===i?void 0:i.value)?2:(null===(o=s.get("isLicense"))||void 0===o?void 0:o.value)?3:1,d=this.checkAndAllocateCalendar(r,l,c,u);null===(a=s.get("calendarId"))||void 0===a||a.patchValue(d,{emitEvent:!1}),this.getPositionQuantity(s)};return["workLocation","entity"].forEach(e=>{const t=s.get(e);t&&this.valueChangeSubscription.add(t.valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(r))}),s},this.getMilestoneFromGroup=(e=!1,t=!1)=>this.fb.group({milestoneId:[null],milestoneName:[null,u.H.required],milestoneStartDate:[null],milestoneEndDate:[null],positions:this.fb.array([]),milestoneTotalRevenue:[0],milestoneTotalCost:[0],activeQuote:[!1],milestoneTotalGMPercentage:[0],isMasterMilestone:[0]}),this.calculatePositionRatePerUnit=(e,t=!1)=>{let n=e.get("noOfResources").value*e.get("ratePerUnit").value*e.get("quantity").value;e.get("originalPositionRevenue")||e.addControl("originalPositionRevenue",this.fb.control(n));let i=e.get("originalRatePerUnit").value;n=e.get("originalPositionRevenue").value;for(const o of this.quoteForm.get("discounts").controls){const a=o.get("discountPercentage").value,s=this.convertToFixed((a||0)/100*n);n-=s,i-=this.convertToFixed((a||0)/100*i),e.contains("discount"+o.get("discountId").value)?(t||o.get("discountValue").patchValue(o.get("discountValue").value-e.get("discount"+o.get("discountId").value).value,{emitEvent:!1}),e.get("discount"+o.get("discountId").value).patchValue(s)):e.addControl("discount"+o.get("discountId").value,this.fb.control(s)),o.get("discountValue").patchValue(this.convertToFixed(o.get("discountValue").value+s),{emitEvent:!1})}e.get("ratePerUnit").patchValue(i,{emitEvent:!1}),this.calculatePositionRevenue(e),this.calculateOverallRevenue()},this.setDiscountEdited=e=>{e.get("isEdited")?e.get("isEdited").patchValue(!0):e.addControl("isEdited",this.fb.control(!0))},this.calculatePositionRevenue=(e,t=!1)=>{var n,i;let o=e.get("noOfResources").value*e.get("ratePerUnit").value*e.get("quantity").value;for(const a of this.quoteForm.get("discounts").controls){const e=a.get("discountPercentage").value;this.convertToFixed((e||0)/100*o)}(null===(n=e.get("valueChangedFromTotalRevenue"))||void 0===n?void 0:n.value)||e.get("totalRevenue").patchValue(o,{emitEvent:!1}),this.calculateOverallRevenue(),null===(i=e.get("valueChangedFromTotalRevenue"))||void 0===i||i.patchValue(!1,{emitEvent:!1})},this.calculateOverallRevenue=e=>{var t;let n=0,i=0,o=0,a=0;const s=this.isQuoteFullyDiscounted();let r=this.allocation_details.data||[],l=this.allocation_details.msg||null,c=this.allocation_details.detail_msg||null;for(const d of this.servicesFormArr.value)for(const n of d.positions){const u=this.positionStatusList.find(e=>e.id===(null==n?void 0:n.positionStatus));if((null==u?void 0:u.exclude_for_revenue)&&(null==r?void 0:r.includes(n.quotePositionId))||(null==r?void 0:r.includes(n.quotePositionId))&&(null==u?void 0:u.allocation_restrict))return(null==e?void 0:e.get("positionStatus"))&&(null===(t=e.get("positionStatus"))||void 0===t||t.setValue(null,{emitEvent:!1})),this._toaster.showWarning(l||"Position Cannot be changed to lost status",c||"Position already have allocation"),!0;if(!(null==u?void 0:u.exclude_for_revenue)){const e=(n.noOfResources||0)*(s?n.originalRate||0:n.ratePerUnit||0)*(n.quantity||0);switch(n.isNonManpower?2:n.isLicense?3:1){case 2:i+=e;break;case 3:o+=e;break;case 1:default:a+=e}for(const t of n.nmpData)i+=(t.noOfResources||1)*(s?t.originalRate:t.ratePerUnit)*(t.quantity||1)}}n=a+i+o;const u=n+this.quoteForm.get("totalTax").value;this.quoteForm.get("totalRevenue").patchValue(this.convertToFixed(n)),this.quoteForm.get("totalOrderValue").patchValue(this.convertToFixed(u)),this.quoteForm.get("manpowerRevenue").patchValue(this.convertToFixed(a)),this.quoteForm.get("nonManpowerRevenue").patchValue(this.convertToFixed(i)),this.quoteForm.get("licenseRevenue").patchValue(this.convertToFixed(o))},this.calculatePositionCost=e=>{const t=e.get("noOfResources").value*e.get("costPerUnit").value*e.get("quantity").value;e.get("totalCost").patchValue(this.convertToFixed(t)),this.calculateOverallCost()},this.convertToFixed=e=>parseFloat(e.toFixed(2)),this.calculateOverallCost=()=>{let e=0,t=0,n=0,i=0;for(const o of this.servicesFormArr.value)for(const e of o.positions){switch(e.isNonManpower?2:e.isLicense?3:1){case 2:t+=e.totalCost;break;case 3:n+=e.totalCost;break;case 1:default:i+=e.totalCost}for(const n of e.nmpData)t+=n.totalCost}e=this.convertToFixed(i+t+n),this.quoteForm.get("totalCost").patchValue(e),this.quoteForm.get("manpowerCost").patchValue(this.convertToFixed(i)),this.quoteForm.get("nonManpowerCost").patchValue(this.convertToFixed(t)),this.quoteForm.get("licenseCost").patchValue(this.convertToFixed(n))},this.calculateOverallGM=()=>{let e=0;for(const t of this.servicesFormArr.getRawValue())for(const n of t.positions){e+=n.totalGM;for(const t of n.nmpData)e+=t.totalGM}e=this.convertToFixed(e),this.quoteForm.get("totalGM").patchValue(e),this.calculateOverallGMPercentage(this.quoteForm)},this.calculateOverallGMPercentage=e=>{const t=e.get("totalGM").value,n=e.get("totalRevenue").value;let i=0;0!=n&&(i=this.convertToFixed(t/n*100)),e.get("totalGMPercentage").patchValue(i)},this.navigateToLandingPage=()=>{this.$router.navigate(["../../"],{relativeTo:this.route})},this.addService=e=>Object(f.c)(this,void 0,void 0,(function*(){const{AddServicesDialogComponent:t}=yield n.e(243).then(n.bind(null,"D4n5"));this.dialog.open(t,{height:"90%",width:"70%",data:{label:e,opportunityId:this.opportunityId,mandatoryFields:this.mandatoryFields,experienceList:this.experienceList,workLocationList:this.workLocationList,nationalityList:this.nationalityList,entityList:this.entityList,divisionList:this.divisionList,subDivisionList:this.subDivisionList,defaultCurrency:this.defaultCurrency,conversionTypeId:this.conversionTypeId,quoteCurrency:this.quoteForm.get("quoteCurrency").value},disableClose:!0}).afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){if(e&&"submit"==e.event&&e.data){const t=e.data;t.length&&t.forEach((e,t)=>{var n,i;let o=this.getServicesFormGroup();o.patchValue({serviceId:e.serviceId,serviceName:e.serviceName,serviceTypeId:e.serviceTypeId,isFixedRate:e.isFixedRate,isDeleteServiceEnabled:!0},{emitEvent:!1});let a=o.get("positions");a.clear();const s=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config),r=this.allocateDefaultValues(this.newly_added_quote_project_integration_config),l=Object.entries(r).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});if(0==e.positions.length){const e=this.getPositionFormGroup();this.resolveSDMapping(o,e),e.patchValue(Object.assign(Object.assign({},s),l)),a.push(e),this.patchMilestoneDetailsUsingForm()}else for(let c of e.positions){let e=this.getPositionFormGroup(!1,c.isLicense);const t=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config),s=this.allocateDefaultValues(this.newly_added_quote_project_integration_config),r=Object.entries(s).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});e.patchValue(Object.assign(Object.assign(Object.assign({},r),{quotePositionId:c.quotePositionId||Object(M.a)(),positionId:c.positionId,positionName:c.positionName,experience:c.experience,workLocation:c.workLocation,nationality:c.nationality,entity:c.entity,division:c.division,subDivision:c.subDivision,unit:c.unit,quantity:1,ratePerUnit:c.revenue||0,totalRevenue:c.revenue||0,costPerUnit:c.cost||0,totalCost:c.cost||0,totalGM:(c.revenue||0)-(c.cost||0),rcValue:c.revenue||0,revenueRegion:this.checkAndAllocateRevenueRegion((null===(n=this.wholeQuoteData)||void 0===n?void 0:n.sales_region)||(null===(i=this.oppQuoteDetails)||void 0===i?void 0:i.sales_region),c.entity)}),t),{emitEvent:!1}),this.resolveSDMapping(o,e),this.calculatePositionRevenue(e),this.calculateOverallGMPercentage(e),this.getPositionQuantity(e),a.push(e)}this.servicesFormArr.push(o),this.addPositionDropZone(),this.patchMilestoneDetailsUsingForm()}),this.calculateOverallCost(),this.calculateOverallRevenue(),setTimeout(()=>{this.scrollContainer=this.scrollFrame.nativeElement},10)}})))})),this.addPositionDropZone=()=>{this.positionDropZones.push("positionDropZone"+(this.servicesFormArr.length-1))},this.previewQuote=()=>{this.isEditMode=!1},this.saveQuote=()=>Object(f.c)(this,void 0,void 0,(function*(){var e,t,n;if(this.isCUDEnabled){if((null===(t=null===(e=this.wholeQuoteData)||void 0===e?void 0:e.change_request_merged)||void 0===t?void 0:t.length)&&(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.discountAndTax.length)&&this.quoteForm.get("quoteId").value&&this.quoteForm.get("totalOrderValue"),!this.isEditMode)return this.isEditMode=!0;if("VALID"==this.quoteForm.status){if(!(yield this.checkMandatory()))return;const e=this.quoteForm.getRawValue();if(e.totalRevenue<=0&&e.activeQuote)return this._toaster.showError("Mandatory Field Error","The quote revenue must be greater than 0.",this.opportunityService.longInterval),!0;if(this.wEMappingEnabled&&this.wEMappingList.length&&!this.validateWEMapping(e))return!0;if(this.sDMappingEnabled&&this.sDMappingList.length&&!this.validateSDMapping(e))return!0;if(this.geographicRevenueRegionMapping&&this.revenueRegionMapping.length&&!this.validateSalesRegionGeoMapping(e))return!0;this.isEditMode=!1;let t={},n=[],i={items:[]};for(const[o,a]of e.services.entries()){let t=[];for(const[n,i]of a.positions.entries()){if(!i.isLicense&&!i.isNonManpower&&!this.skillList.find(e=>e.id==i.positionId))return this.servicesFormArr.at(o).get("positions").at(n).patchValue({positionId:null,positionName:null},{emitEvent:!1}),this._toaster.showError("Mandatory Field Error","Kindly Select Valid Position",this.opportunityService.longInterval),this.isEditMode=!0;let a=[];if(i.nmpData&&i.nmpData.length)for(const[t,n]of i.nmpData.entries()){let t=n.isPositionEffortChanged||!1;n.isQuantityChanged&&(t=!0),a.push({qp_item_id:"number"==typeof n.quotePositionId?n.quotePositionId:n.quotePositionId&&n.quotePositionId.length>8?null:n.quotePositionId||null,nmp_id:n.positionId,name:n.positionName||null,no_of_resource:n.noOfResources,rate_per_unit:n.ratePerUnit,original_rate:n.originalRatePerUnit,cost_per_unit:n.costPerUnit,entity:n.entity,division:n.division,sub_division:n.subDivision,work_location:n.workLocation,nationality:n.nationality,unit_id:n.unit,quantity:n.quantity,resource_type_id:2,qpi_currency:e.quoteCurrency,qpi_amount:n.totalCost,qpi_revenue_amount:n.totalRevenue,start_date:n.startDate,end_date:n.endDate,position_effort:n.positionEffortData||null,is_position_effort_changed:t,is_position_value_changed:n.isPositionValueChanged,rc_value:n.rcValue,business_type:n.typeOfBusiness,revenue_region:n.revenueRegion,position_status:n.positionStatus,milestone_id:(()=>{const e=this.milestoneList.find(e=>e.id==n.milestone);return e?1===e.is_master||e.milestone_id&&e.milestone_id.length>8?null:e.milestone_id:null})(),milestoneDetails:this.milestoneList.find(e=>e.id==n.milestone)||[],milestone:(()=>{const e=this.milestoneList.find(e=>e.id==n.milestone);return e?(e.milestone_id=1==(null==e?void 0:e.is_master)||e.milestone_id&&e.milestone_id.length>8?null:e.milestone_id,[e]):[]})()})}let s=i.isPositionEffortChanged||!1;i.isQuantityChanged&&(s=!0),t.push({quote_position_id:"number"==typeof i.quotePositionId?i.quotePositionId:i.quotePositionId&&i.quotePositionId.length>8?null:i.quotePositionId||null,position:i.positionId,position_name:i.positionName||null,resource_count:i.noOfResources,rate_per_unit:i.ratePerUnit,original_rate:i.originalRatePerUnit,cost_per_unit:i.costPerUnit,work_experience:i.experience,nationality:i.nationality,work_location:i.workLocation,entity:i.entity,division:i.division,sub_division:i.subDivision,unit_id:i.unit,quantity:i.quantity,resource_type_id:i.isNonManpower?2:i.isLicense?3:1,item_currency:e.quoteCurrency,item_revenue_amount:i.totalRevenue,item_cost_amount:i.totalCost,start_date:i.startDate,end_date:i.endDate,position_order:n,position_items:a,position_effort:i.positionEffortData||null,is_position_effort_changed:s,is_position_value_changed:i.isPositionValueChanged,rc_value:i.rcValue,business_type:i.typeOfBusiness,revenue_region:i.revenueRegion,position_status:i.positionStatus,milestone_id:(()=>{const e=this.milestoneList.find(e=>e.id==i.milestone);return e?1===e.is_master||e.milestone_id&&e.milestone_id.length>8?null:e.milestone_id:null})(),milestone:(()=>{const e=this.milestoneList.find(e=>e.id==i.milestone);return e?(e.milestone_id=1==(null==e?void 0:e.is_master)||e.milestone_id&&e.milestone_id.length>8?null:e.milestone_id,[e]):[]})(),calendar_id:i.calendarId||this.calendarId})}n.push({quote_service_id:a.quoteServiceId,service_header_id:a.serviceId,service_name:a.serviceName,service_type_id:a.serviceTypeId||null,service_revenue_amount:a.serviceRevenue,service_cost_amount:a.serviceCost,service_currency:e.quoteCurrency,is_fixed_rate:a.isFixedRate||null,service_order:o,position:t})}for(const o of e.discounts)i.items.push({dt_item_id:o.dtItemId,id:o.discountId,name:o.discountName,percentage:o.discountPercentage,type:"D",tax_type:null,item_amount:o.discountValue,item_currency:e.quoteCurrency,is_custom:o.isCustom});for(const o of e.taxes)i.items.push({dt_item_id:o.dtItemId,id:o.taxId,name:o.taxName,percentage:o.taxPercentage,type:"T",tax_type:null,item_amount:o.taxValue,item_currency:e.quoteCurrency,is_custom:!1});if(t.quote_name=e.quoteName,t.quote_currency=e.quoteCurrency,t.quote_currency_id=e.quoteCurrencyId,t.initial_currency_id=e.initialCurrencyId||e.quoteCurrencyId,t.quote_amount=e.totalOrderValue,t.quote_cost_amount=e.totalCost,t.quote_revenue_amount=e.totalRevenue,t.delivery_start_date=e.deliveryStartDate,t.delivery_end_date=e.deliveryEndDate,t.opportunity_id=this.opportunityId,t.service=n,t.discountAndTax=i,t.version=e.version,t.work_schedule_id=this.workScheduleId||1,t.default_currency=this.defaultCurrency,t.conversion_type_id=this.conversionTypeId,t.initial_delivery_start_date=this.initial_delivery_start_date,t.initial_delivery_end_date=this.initial_delivery_end_date,t.is_quote_active=e.activeQuote,t.has_parent_opportunity=e.quoteOpportunityHasParent,t.quote_type=e.quoteType,this.isDataBeingSaved=!0,e.quoteId){t.quote_header_id=e.quoteId;let n=(yield this._quoteMainService.checkForPercentageApproval(t,!0))||null;if(n.data&&n.applicable_for_quote_edit)return yield this.patchQuoteDetails(this.wholeQuoteData),this.isDataBeingSaved=!1,this._toaster.showWarning("Warning",n.edit_msg||"More than a certain Percentage value Increase should be Change Request",this.opportunityService.longInterval);this._quoteMainService.updateQuote(t).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){return e.rds_peak?(this._toaster.showError("System Unavailable",e.msg||"System is Unavailable try after Sometime!",3e3),!0):"W"==e.messType&&e.data?(this.patchQuoteDetails(this.wholeQuoteData),this.isDataBeingSaved=!1,this._toaster.showWarning("Warning",e.msg||"Quantity of Position Cannot be reduced below allocated hours",this.opportunityService.longInterval),!0):("S"==e.messType&&e.data?(this._toaster.showSuccess("Success","Quote Updated Successfully !",this.opportunityService.longInterval),this.toggleSpinner(!0),yield this.getQuoteDetails(),this.alloteFieldConfig(),this.quoteForm.get("activeQuote").value&&(yield this.resolveQuoteOpportunityIntegration(t)),this.patchQuoteDetails(this.wholeQuoteData),this.milestoneApplicable&&(yield this.getMilestoneListQB()),this.patchMilestoneDetailsUsingForm(),this.toggleSpinner(!1),this.isChangesMade=!1):this._toaster.showError("Failed","Quote Updation Failed !",this.opportunityService.mediumInterval),void(this.isDataBeingSaved=!1))})),e=>{console.log(e),this.isDataBeingSaved=!1,this._toaster.showError("Error","Error in Updation Quote Data",this.opportunityService.mediumInterval)})}else this._quoteMainService.createQuote(t).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{e.rds_peak&&(this._toaster.showError("System Unavailable",e.msg||"System is Unavailable try after Sometime!",3e3),this.isDataBeingSaved=!1),"S"==e.messType&&e.data?(this._toaster.showSuccess("Success","Quote Created Successfully !",this.opportunityService.longInterval),this.quoteForm.patchValue({quoteId:e.data},{emitEvent:!1}),this.$router.navigate(["../../edit/"+e.data],{relativeTo:this.route})):this._toaster.showError("Failed","Quote Creation Failed !",this.opportunityService.mediumInterval),this.isDataBeingSaved=!1},e=>{console.log(e),this.isDataBeingSaved=!1,this._toaster.showError("Error","Error in Quote Creation",this.opportunityService.mediumInterval)})}else for(const e of this.servicesFormArr.controls)for(const t of e.get("positions").controls)if("INVALID"==t.status){for(const e of t.get("nmpData").controls)if("INVALID"==e.status)return this.resolveMandatoryMsg(e);return this.resolveMandatoryMsg(t)}}})),this.resolveMandatoryMsg=e=>{for(const[t,n]of Object.entries(e.controls))if("INVALID"==n.status){let n="Kindly Fill";const i=this.customizeFields.find(e=>e.key===t);return"positionId"==t?n+=" Position Field":i&&(n+=` ${i.label} Field of ${e.get("positionName").value} position`),i&&0==i.isVisible.value&&(n+=" (Kindly enable the field in Customize Fields Option)"),this.wEMappingEnabled&&"entity"==t&&(n+=" (Kindly Update the Mapping)"),this.sDMappingEnabled&&"division"==t&&(n+=" (Kindly Update the Mapping)"),this._toaster.showError("Mandatory Field Error",n,this.opportunityService.longInterval)}},this.addPosition=(e,t,n,i)=>{var o;const a=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config),s=this.allocateDefaultValues(this.newly_added_quote_project_integration_config),r=Object.entries(s).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{}),l=this.getPositionFormGroup("nmpData"===n);l.patchValue(Object.assign(Object.assign({},r),a),{emitEvent:!1}),l.patchValue({quotePositionId:Object(M.a)()},{emitEvent:!1}),this.resolveSDMapping(e,l),(i||e).get(n).insert(t+1,l);const c=this.milestoneList.find(e=>e.id===r.milestone);c&&(null===(o=l.get("milestoneDetails"))||void 0===o||o.patchValue([c],{emitEvent:!1})),this.patchMilestoneDetailsUsingForm()},this.deletePosition=(e,t,n)=>{const i=e.get(n);if(this.allocateDefaultValues(this.newly_added_quote_project_integration_config),this.getPositionFormGroup("nmpData"===n),"nmpData"!=n&&1==i.length)return this._toaster.showError("Mandatory Error","Minimum of 1 Position has to be present",this.opportunityService.longInterval);null!=i.at(t).get("quotePositionId").value?this._util.openConfirmationSweetAlertWithCustom("Are you Sure ?","Do you want to delete this position").then(e=>{e&&(i.removeAt(t),this.patchMilestoneDetailsUsingForm(),this.calculateOverallRevenue(),this.calculateOverallCost(),this.calculateOverallGM())}):(i.removeAt(t),this.patchMilestoneDetailsUsingForm(),this.calculateOverallRevenue(),this.calculateOverallCost(),this.calculateOverallGM())},this.duplicatePosition=(e,t,n,i)=>Object(f.c)(this,void 0,void 0,(function*(){let o=(i||e).get(n);const a=o.at(t).value;let s=this.getPositionFormGroup("nmpData"===n);o.at(t).get("positionDuplicationLoader").patchValue(!0);const r=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);s.patchValue(a,{emitEvent:!1}),s.patchValue(Object.assign({quotePositionId:Object(M.a)(),lastUnit:a.unit,isQuantityChanged:!0,isUnitChanged:!1},r),{emitEvent:!1}),a.positionEffortData||!a.quotePositionId||"string"==typeof a.quotePositionId||a.isQuantityChanged||(a.positionEffortData=yield this.getCalendarDataForTheDuration(s,a)),a.positionEffortData||a.quotePositionId&&!("string"==typeof a.quotePositionId&&a.quotePositionId.length>8)||(a.positionEffortData=null),s.contains("positionEffortData")||s.addControl("positionEffortData",this.fb.control([])),s.patchValue({positionEffortData:a.positionEffortData,isQuantityChanged:!1,isPositionEffortChanged:!0,isUnitChanged:!1},{emitEvent:!1}),s.get("ratePerUnit").patchValue(a.ratePerUnit),s.get("costPerUnit").patchValue(a.costPerUnit),this.resolveSDMapping(e,s),o.insert(t+1,s),o.at(t).get("positionDuplicationLoader").patchValue(!1),this.patchMilestoneDetailsUsingForm()})),this.clearPosition=(e,t,n)=>{e.get(n).at(t).patchValue({positionId:null,noOfResources:1,experience:null,workLocation:null,nationality:null,unit:null,ratePerUnit:0,quantity:0,totalRevenue:0,rcValue:0})},this.addButtonClick=e=>{switch(e.id){case 1:this.openSectionOverlay();break;case 2:this.addService(e.label);break;case 3:this.openPositionDialog(e.label);break;case 4:this.openNonManpowerDialog(e.label);break;case 5:this.openLicenseDialog(e.label);break;case 6:if(this.quoteForm.get("totalOrderValue").value<=0)return this._toaster.showWarning("Discount cannot be applied as the Total Order Value is zero or negative.","",this.opportunityService.mediumInterval);this.quoteForm.get("discounts").push(this.getDiscountsFormGroup()),setTimeout(()=>{this.scrollToBottom()},10);break;case 7:if(this.quoteForm.get("totalOrderValue").value<=0)return this._toaster.showWarning("Tax cannot be applied as the Total Order Value is zero or negative.","",this.opportunityService.mediumInterval);this.quoteForm.get("taxes").push(this.getTaxesFormGroup()),setTimeout(()=>{this.scrollToBottom()},10);break;case 8:this.openMilestoneDialog()}},this.openSectionOverlay=()=>{var e;if(!(null===(e=this.sectionOverlayRef)||void 0===e?void 0:e.hasAttached())){this.addSectionFormControl.reset();const e=this.overlay.position().global().centerHorizontally().centerVertically();this.sectionOverlayRef=this.overlay.create({positionStrategy:e,hasBackdrop:!0});const t=new b.h(this.templateRef,this.viewContainerRef);this.sectionOverlayRef.attach(t),this.sectionOverlayRef.backdropClick().subscribe(()=>{this.closeSectionDialog()})}},this.closeSectionDialog=()=>{var e;this.addSectionFormControl.reset(),null===(e=this.sectionOverlayRef)||void 0===e||e.dispose()},this.addSection=()=>{const e=this.getServicesFormGroup();e.patchValue({serviceId:null,serviceName:this.addSectionFormControl.value,isSection:!0},{emitEvent:!1});const t=e.get("positions"),n=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config),i=this.allocateDefaultValues(this.newly_added_quote_project_integration_config),o=Object.entries(i).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});let a=this.getPositionFormGroup();a.patchValue(Object.assign(Object.assign({},n),o)),t.push(a),this.servicesFormArr.push(e),this.addPositionDropZone(),this.closeSectionDialog()},this.openPositionDialog=e=>Object(f.c)(this,void 0,void 0,(function*(){const{AddPositionsDialogComponent:t}=yield n.e(240).then(n.bind(null,"WzEf"));this.dialog.open(t,{height:"70%",width:"50%",data:{label:e,positionList:this.skillList},disableClose:!0}).afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t;if(e&&"submit"==e.event&&e.data){const n=e.data;let i=this.servicesFormArr.at(this.servicesFormArr.length-1),o=i.get("positions");for(let e of n){let n=this.getPositionFormGroup();const a=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);let s=this.allocateDefaultValues(this.newly_added_quote_project_integration_config);const r=Object.entries(s).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});n.patchValue(Object.assign(Object.assign({quotePositionId:Object(M.a)(),positionId:e.positionId,positionName:e.positionName},r),a),{emitEvent:!1}),n.patchValue({ratePerUnit:e.revenue||0,originalRatePerUnit:e.originalRatePerUnit||e.ratePerUnit});const l=this.milestoneList.find(e=>e.id===r.milestone);l&&(null===(t=n.get("milestoneDetails"))||void 0===t||t.patchValue([l],{emitEvent:!1})),this.getPositionQuantity(n),this.resolveSDMapping(i,n),o.push(n)}this.patchMilestoneDetailsUsingForm()}})))})),this.openNonManpowerDialog=e=>Object(f.c)(this,void 0,void 0,(function*(){let t=[],i=0;for(const[e,n]of this.servicesFormArr.value.entries())for(const[o,a]of n.positions.entries())if(!a.isNonManpower&&!a.isLicense){i++;let n=this.nationalityList.find(e=>e.id===a.nationality),s=this.experienceList.find(e=>e.id===a.experience);t.push({uniqueId:i,serviceIndex:e,positionIndex:o,position:a.positionName,nationality:null==n?void 0:n.name,experience:null==s?void 0:s.name})}const{AddNonManpowerDialogComponent:o}=yield n.e(238).then(n.bind(null,"0mG6"));this.dialog.open(o,{minHeight:"70vh",height:"auto",width:"50%",data:{label:e,nmpList:this.nmpList,positionsList:t,manpowerNonManpowerMapping:this.manpowerNonManpowerMapping},disableClose:!0}).afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t,n;if(e&&"submit"==e.event&&e.data&&e.data.nmpData){const i=e.data.nmpData;if(e.data.taggedPositions&&e.data.taggedPositions.length)for(const o of e.data.taggedPositions){let e=this.servicesFormArr.at(o.serviceIndex),a=e.get("positions").at(o.positionIndex).get("nmpData");this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);let s=this.allocateDefaultValues(this.newly_added_quote_project_integration_config);const r=Object.entries(s).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});for(let n of i){let i=this.getPositionFormGroup(!0);const o=this.nmpList.find(e=>e.id===n.nmpId),s=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);let r=this.allocateDefaultValues(this.newly_added_quote_project_integration_config);const l=Object.entries(r).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});o&&i.patchValue(Object.assign(Object.assign(Object.assign({},l),{positionId:n.nmpId,positionName:n.nmpName,workLocation:o.work_location,nationality:o.nationality,entity:o.entity,division:o.division,subDivision:o.sub_division,unit:o.quote_unit,lastUnit:o.quote_unit,quantity:1,ratePerUnit:o.revenue||0,totalRevenue:o.revenue||0,costPerUnit:o.cost||0,totalCost:o.cost||0,totalGM:(o.revenue||0)-(o.cost||0),rcValue:o.revenue||0,originalRatePerUnit:o.revenue}),s),{emitEvent:!1}),i.patchValue({ratePerUnit:o.revenue||0,originalRatePerUnit:o.revenue});const c=this.milestoneList.find(e=>e.id===l.milestone);c&&(null===(t=i.get("milestoneDetails"))||void 0===t||t.patchValue([c],{emitEvent:!1})),this.calculatePositionRevenue(i),this.calculateOverallGMPercentage(i),this.getPositionQuantity(i),this.resolveSDMapping(e,i),a.push(i)}let l=this.getPositionFormGroup(!0);const c=this.milestoneList.find(e=>e.id===r.milestone);c&&(null===(n=l.get("milestoneDetails"))||void 0===n||n.patchValue([c],{emitEvent:!1})),this.patchMilestoneDetailsUsingForm()}else{let e=this.servicesFormArr.at(this.servicesFormArr.length-1),t=e.get("positions");const n=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config),o=this.allocateDefaultValues(this.newly_added_quote_project_integration_config),a=Object.entries(o).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});for(let s of i){let i=this.getPositionFormGroup(!0);const o=this.nmpList.find(e=>e.id===s.nmpId);o&&i.patchValue(Object.assign(Object.assign(Object.assign({},a),{positionId:s.nmpId,positionName:s.nmpName,workLocation:o.work_location,nationality:o.nationality,entity:o.entity,division:o.division,subDivision:o.sub_division,unit:o.quote_unit,lastUnit:o.quote_unit,quantity:1,ratePerUnit:o.revenue||0,totalRevenue:o.revenue||0,costPerUnit:o.cost||0,totalCost:o.cost||0,totalGM:(o.revenue||0)-(o.cost||0),rcValue:o.revenue||0}),n),{emitEvent:!1}),i.patchValue({ratePerUnit:o.revenue||0,originalRatePerUnit:o.revenue}),this.calculatePositionRevenue(i),this.calculateOverallGMPercentage(i),this.getPositionQuantity(i),this.resolveSDMapping(e,i),t.push(i)}}}})))})),this.openLicenseDialog=e=>Object(f.c)(this,void 0,void 0,(function*(){const{AddPositionsDialogComponent:t}=yield n.e(240).then(n.bind(null,"WzEf"));this.dialog.open(t,{height:"70%",width:"50%",data:{label:e,positionList:this.licenseList,isLicense:!0},disableClose:!0}).afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t;if(e&&"submit"==e.event&&e.data){const n=e.data;let i=this.servicesFormArr.at(this.servicesFormArr.length-1),o=i.get("positions");const a=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);let s=this.allocateDefaultValues(this.newly_added_quote_project_integration_config);const r=Object.entries(s).filter(([e,t])=>"number"==typeof t).reduce((e,[t,n])=>Object.assign(Object.assign({},e),{[t]:n}),{});for(const e of n){let n=this.getPositionFormGroup(!1,!0);const s=this.licenseList.find(t=>t.id===e.positionId);s&&n.patchValue(Object.assign(Object.assign(Object.assign({},r),{positionId:e.positionId,positionName:e.positionName,workLocation:s.work_location,nationality:s.nationality,entity:s.entity,division:s.division,subDivision:s.sub_division,unit:s.quote_unit,lastUnit:s.quote_unit,quantity:1,totalRevenue:s.revenue||0,costPerUnit:s.cost||0,totalCost:s.cost||0,totalGM:(s.revenue||0)-(s.cost||0),rcValue:s.revenue||0}),a),{emitEvent:!1}),n.patchValue({ratePerUnit:s.revenue||0,originalRatePerUnit:s.revenue});const l=this.milestoneList.find(e=>e.id===r.milestone);l&&(null===(t=n.get("milestoneDetails"))||void 0===t||t.patchValue([l],{emitEvent:!1})),this.calculatePositionRevenue(n),this.calculateOverallGMPercentage(n),this.getPositionQuantity(n),this.resolveSDMapping(i,n),o.push(n)}this.calculateOverallRevenue(),this.calculateOverallCost(),this.patchMilestoneDetailsUsingForm()}})))})),this.dropPosition=(e,t)=>{if(e.previousContainer==e.container){let n=this.servicesFormArr.at(t).get("positions");this.changeFormArrayOrder(n,e.previousIndex,e.currentIndex)}else if(null!=this.initialServiceDragIndex){let n=this.servicesFormArr.at(this.initialServiceDragIndex).get("positions"),i=this.servicesFormArr.at(t).get("positions");this.resolveSDMapping(this.servicesFormArr.at(t),n.at(e.previousIndex)),i.insert(e.currentIndex,n.at(e.previousIndex)),n.removeAt(e.previousIndex)}this.initialServiceDragIndex=null},this.dropService=e=>{this.changeFormArrayOrder(this.servicesFormArr,e.previousIndex,e.currentIndex)},this.changeFormArrayOrder=(e,t,n)=>{const i=e.at(t),o=e.at(n);e.setControl(n,i),e.setControl(t,o)},this.onPositionDragStart=e=>{this.initialServiceDragIndex=e},this.initDiscountAndTax=()=>{this.quoteForm.get("discounts").push(this.getDiscountsFormGroup()),this.quoteForm.get("taxes").push(this.getTaxesFormGroup()),setTimeout(()=>{this.scrollToBottom()},10)},this.getDiscountsFormGroup=()=>{const e=this.fb.group({dtItemId:[null],discountId:[null],discountName:[null],discountPercentage:[{value:null,disabled:!0}],discountValue:[0],isCustom:[!1]});this.valueChangeSubscription.add(e.get("discountId").valueChanges.pipe(Object(w.a)(10),Object(S.a)(this._onDestroy)).subscribe(t=>{if(t){const n=this.discountsList.find(e=>e.id===t);e.get("discountName").patchValue(n.name||null,{emitEvent:!1}),e.get("discountPercentage").patchValue(n.percentage||0)}else e.get("discountPercentage").patchValue(0)})),this.valueChangeSubscription.add(e.get("isCustom").valueChanges.pipe(Object(w.a)(10),Object(S.a)(this._onDestroy)).subscribe(t=>{e.get("discountId").reset(),e.get("discountName").reset(),e.get("discountPercentage").reset(),t?e.get("discountPercentage").enable({emitEvent:!1}):e.get("discountPercentage").disable({emitEvent:!1})}));let t=!1;return this.valueChangeSubscription.add(e.get("discountPercentage").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(n=>{if(t)return;let i=parseFloat(n);const o=e.get("discountPercentage"),a=this.quoteForm.get("discounts").controls.reduce((t,n)=>{if(n!==e){const e=parseFloat(n.get("discountPercentage").value);return t+(isNaN(e)?0:e)}return t},0),s=Math.max(0,100-a);if(i<0||isNaN(i)?(t=!0,setTimeout(()=>{o.patchValue(0,{emitEvent:!1}),t=!1})):o.patchValue(i,{emitEvent:!1}),i>s)return this._toaster.showWarning(`Only up to ${s}% can be applied to this discount to keep the total under 100%.`,"",this.opportunityService.longInterval),t=!0,void setTimeout(()=>{o.setValue(s),t=!1});this.setDiscountEdited(e),this.calculateTotalDiscount(e)})),this.valueChangeSubscription.add(e.get("discountValue").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(e=>{this.calculateOverallDiscount()})),e},this.calculateTotalDiscount=e=>{var t,n,i;for(const o of this.quoteForm.get("discounts").controls)o.get("discountValue").patchValue(0,{emitEvent:!1});for(const o of this.servicesFormArr.controls)for(const e of o.get("positions").controls){this.calculatePositionRatePerUnit(e,!0);for(const t of e.get("nmpData").controls)this.calculatePositionRatePerUnit(t,!0)}this.calculateOverallDiscount(),this.calculateOverallRevenue(),(null===(n=null===(t=this.wholeQuoteData)||void 0===t?void 0:t.change_request_merged)||void 0===n?void 0:n.length)&&(null===(i=this.wholeQuoteData)||void 0===i?void 0:i.discountAndTax.length)&&this.quoteForm.get("quoteId").value&&this.quoteForm.get("totalOrderValue")},this.deleteDiscountAndTax=(e,t)=>{var n;const i=this.quoteForm.get(t).at(e);this.quoteForm.get(t).removeAt(e),this.wholeQuoteData&&(null===(n=this.wholeQuoteData)||void 0===n||n.discountAndTax.some(e=>e.dt_item_id===i.get("dtItemId").value)),"discounts"===t?this.calculateTotalDiscount():"taxes"===t&&this.calculateTotalTax()},this.removeDiscount=(e,t)=>{this.quoteForm.get(t).removeAt(e),this.wholeQuoteData.discountAndTax.length>0&&this.wholeQuoteData.discountAndTax.some(e=>"T"===e.type)&&this._toaster.showWarning("Removing this discount will not have any impact on the rate!","",this.opportunityService.longInterval),e>-1&&this.applyAllDiscountsToPositions()},this.calculateOverallDiscount=()=>{var e,t,n,i,o;const a=null===(e=this.quoteForm.get("totalOrderValue"))||void 0===e?void 0:e.value,s=this.quoteForm.get("discounts");if(!(null==s?void 0:s.length))return void(null===(t=this.quoteForm.get("totalDiscount"))||void 0===t||t.patchValue(0));let r=1;s.controls.forEach(e=>{var t;const n=+(null===(t=e.get("discountPercentage"))||void 0===t?void 0:t.value)||0;r*=1-n/100});let l=a/r,c=0;if(0===r){s.controls.forEach(e=>{var t,n;const i=+(null===(t=e.get("discountPercentage"))||void 0===t?void 0:t.value)||0,o=a*(i/100);null===(n=e.get("discountValue"))||void 0===n||n.patchValue(o,{emitEvent:!1})});let e=0;for(const n of this.servicesFormArr.value)for(const t of n.positions){e+=(t.noOfResources||1)*(t.quantity||1)*(t.originalRatePerUnit||0);for(const n of t.nmpData)e+=(n.noOfResources||1)*(n.quantity||1)*(n.originalRatePerUnit||0)}const t=e+(+(null===(n=this.quoteForm.get("totalTax"))||void 0===n?void 0:n.value)||0),o=s.controls.reduce((e,t)=>{var n;return e+(+(null===(n=t.get("discountPercentage"))||void 0===n?void 0:n.value)||0)},0);return o>0&&s.controls.forEach(e=>{var n,i;const a=(+(null===(n=e.get("discountPercentage"))||void 0===n?void 0:n.value)||0)/o*t;null===(i=e.get("discountValue"))||void 0===i||i.patchValue(a,{emitEvent:!1})}),void(null===(i=this.quoteForm.get("totalDiscount"))||void 0===i||i.patchValue(t))}s.controls.reduce((e,t)=>{var n;return e+(+(null===(n=t.get("discountPercentage"))||void 0===n?void 0:n.value)||0)},0),s.controls.forEach(e=>{var t,n;const i=+(null===(t=e.get("discountPercentage"))||void 0===t?void 0:t.value)||0,o=l*(i/100);null===(n=e.get("discountValue"))||void 0===n||n.patchValue(o,{emitEvent:!1}),l-=o,c+=o}),null===(o=this.quoteForm.get("totalDiscount"))||void 0===o||o.patchValue(c)},this.getTaxesFormGroup=()=>{const e=this.fb.group({dtItemId:[null],taxId:[null],taxName:[null],taxPercentage:[{value:null,disabled:!0}],taxValue:[0]});return this.valueChangeSubscription.add(e.get("taxId").valueChanges.pipe(Object(w.a)(10),Object(S.a)(this._onDestroy)).subscribe(t=>{if(t){const n=this.taxesList.find(e=>e.id===t);e.get("taxName").patchValue(n.name||null,{emitEvent:!1}),e.get("taxPercentage").patchValue(n.tax_percentage||0)}else e.get("taxPercentage").patchValue(0)})),this.valueChangeSubscription.add(e.get("taxPercentage").valueChanges.pipe(Object(w.a)(100),Object(S.a)(this._onDestroy)).subscribe(t=>{const n=this.quoteForm.get("totalRevenue").value,i=this.convertToFixed((t||0)/100*n);e.get("taxValue").patchValue(i),this.calculateTotalTax()})),e},this.calculateTotalTax=()=>{let e=0;for(const t of this.quoteForm.get("taxes").value)e+=t.taxValue;this.quoteForm.get("totalTax").patchValue(e),this.calculateTotalDiscount(),this.calculateOverallRevenue()},this.toggleCustomDiscount=e=>{let t=this.quoteForm.get("discounts").at(e);t.get("isCustom").patchValue(!t.get("isCustom").value)},this.openCustomizeDialog=()=>Object(f.c)(this,void 0,void 0,(function*(){if(this.customizeFields.length){const{CustomizeFieldDialogComponent:e}=yield n.e(268).then(n.bind(null,"e3aX"));this.dialog.open(e,{height:"auto",width:"25vw",data:{customizeFields:this.customizeFields},disableClose:!0}).afterClosed().subscribe(e=>{let t=[];for(let n=0;n<this.customizeFields.length;n++)t.push({field_key:this.resolveFieldMapping(this.customizeFields[n].key),field_position:n,is_visible:this.customizeFields[n].isVisible.value});this._quoteMainService.updateUserFieldConfig(t).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{"E"==e.messType&&this._toaster.showError("Error in Updating Field Config",e.messText,this.opportunityService.longInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in Updating Field Config",this.opportunityService.longInterval)})})}})),this.openPositionEffort=(e,t,i)=>Object(f.c)(this,void 0,void 0,(function*(){if(e.get("unit").value){const t={quoteId:this.quoteForm.get("quoteId").value,quotePositionId:e.get("quotePositionId").value,positionName:e.get("positionName").value,startDate:e.get("startDate").value,endDate:e.get("endDate").value,unit:e.get("unit").value,quantity:e.get("quantity").value,isQuantityChanged:e.get("isQuantityChanged").value,isUnitChanged:e.get("isUnitChanged").value,resourceTypeId:e.get("isNonManpower").value?2:e.get("isLicense").value?3:1,positionLevel:i,quoteType:this.quoteForm.get("quoteType").value};e.contains("positionEffortData")&&(t.positionEffortData=e.get("positionEffortData").value);const{PositionEffortDialogComponent:o}=yield n.e(311).then(n.bind(null,"FaV9")),a=this.dialog.open(o,{height:"73%",width:"50%",data:{positionData:t,unitList:this.unitList.concat(this.otherUnitList),isEditMode:this.isEditMode,calendarId:e.get("calendarId").value,workScheduleId:this.workScheduleId||1,deliveryStartDate:this.quoteForm.get("deliveryStartDate").value,deliveryEndDate:this.quoteForm.get("deliveryEndDate").value,quoteType:this.quoteForm.get("quoteType").value},disableClose:!0});console.log(e.get("calendarId").value,"Effort Calendar ID"),a.afterClosed().subscribe(t=>Object(f.c)(this,void 0,void 0,(function*(){if(t&&t.data){const n=t.data;n.startDate&&e.get("startDate").patchValue(n.startDate),n.endDate&&e.get("endDate").patchValue(n.endDate),n.hasOwnProperty("quantity")&&(e.get("quantity").patchValue(n.quantity,{emitEvent:!1}),this.calculatePositionRevenue(e),this.calculatePositionCost(e)),n.positionEffortData&&(e.contains("positionEffortData")||e.addControl("positionEffortData",this.fb.control(null)),e.patchValue({positionEffortData:n.positionEffortData,isQuantityChanged:!1,isPositionEffortChanged:!0,isUnitChanged:!1}))}})))}else this._toaster.showWarning("Unit not found","Kindly Select Unit before proceeding",this.opportunityService.longInterval)})),this.openActivityLog=()=>Object(f.c)(this,void 0,void 0,(function*(){const{QuoteActivityLogComponent:e}=yield Promise.all([n.e(0),n.e(313)]).then(n.bind(null,"YQWb"));this.dialog.open(e,{height:"100vh",width:"35vw",data:{quoteId:this.quoteId,showQuoteValue:this.checkFieldShouldbeRestricted("totalRevenue")},position:{right:"0"},disableClose:!1})})),this.resolveQuoteOpportunityIntegration=e=>new Promise((t,n)=>{this._quoteMainService.resolveQuoteOppIntegration(this.opportunityId,null,1,!0,e).pipe(Object(S.a)(this._onDestroy)).subscribe(i=>{"S"==i.messType?(i.quoteMessageType&&("info"==i.quoteMessageType?this._toaster.showInfo("Quote Info",i.messText,this.opportunityService.longInterval):"warning"==i.quoteMessageType?this._toaster.showWarning("Quote Warning",i.messText,this.opportunityService.longInterval):"error"==i.quoteMessageType&&this._toaster.showError("Quote Error",i.messText,this.opportunityService.longInterval)),i.hasOwnProperty("showConfirmationPopup")&&1==i.showConfirmationPopup&&i.quoteId?this._util.openConfirmationSweetAlertWithCustom("Copy Quote Value?","Do you wish to copy Quote value to Opportunity ?").then(o=>{o?this._quoteMainService.updateValueInOpportunity(this.opportunityId,i.quoteId,e).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showInfo("Value Updated",e.messText,this.opportunityService.longInterval):this._toaster.showError("Error in Updating",e.messText,this.opportunityService.longInterval),t(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in Updating Quote value in Opportunity",this.opportunityService.mediumInterval),n(!1)}):t(!0)}):t(!0)):(this._toaster.showError("Error",i.messText,this.opportunityService.mediumInterval),t(!0))},e=>{console.log(e),this._toaster.showError("Error","Error in Checking Quote Configuration",this.opportunityService.mediumInterval),n(!1)})}),this.resolveFieldMapping=e=>{for(const t of this.customizeFields){if(t.key===e)return t.columnName;if(t.columnName===e)return t.key}return e},this.resolveCalendarFieldMapping=e=>{const t={entity:"entity",work_location:"workLocation",service_type_id:"serviceTypeId"};return t[e]?t[e]:e},this.getPositionRate=e=>{const t={position:e.get("positionId").value};for(const n of this.mandatoryFields){const i=e.get(this.resolveFieldMapping(n)).value;if(null==i||""==i)return!0;t[n]=i}this._quoteMainService.getRate(t,this.opportunityId,this.defaultCurrency,this.quoteForm.get("quoteCurrency").value,this.conversionTypeId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{if("S"==t.messType&&t.data&&t.data.length){const n=t.data[0],i={entity:n.entity,division:n.division,subDivision:n.sub_division,experience:n.work_experience,workLocation:n.work_location,nationality:n.nationality,quantity:1};for(const e of this.mandatoryFields)delete i[this.resolveFieldMapping(e)];e.patchValue(i,{emitEvent:!1});const o={ratePerUnit:n.revenue,originalRatePerUnit:n.revenue,costPerUnit:n.cost,rcValue:n.revenue};null!=n.unit&&(o.unit=n.unit),e.patchValue(o)}else e.patchValue({ratePerUnit:0,costPerUnit:0,originalRatePerUnit:0,rcValue:0}),this._toaster.showWarning("Rate card not found !","Rate, Cost has been reset",this.opportunityService.longInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in fetching rate card",this.opportunityService.mediumInterval)})},this.getPositionQuantity=e=>{const t=e.get("startDate").value,n=e.get("endDate").value,i=e.get("calendarId").value,o=this.unitList.concat(this.otherUnitList).find(t=>t.id===e.get("unit").value);if(!o)return!0;this._quoteMainService.getQuantityForPosition(t,n,o,i,this.workScheduleId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&null!=t.data&&e.patchValue({quantity:t.data})},e=>{console.log(e),this._toaster.showError("Error","Error in fetching quantity for position",this.opportunityService.mediumInterval)})},this.deleteQuote=()=>{var e;let t=this.projectDetailsForOpportunity&&"S"==this.projectDetailsForOpportunity.messType&&(null===(e=this.projectDetailsForOpportunity.messData)||void 0===e?void 0:e.length)>0;this.isQuoteActive||this.isChangeRequestActive?t?this._toaster.showWarning("Cannot Delete Active Quote","Project has already been created for the Opportunity!",this.opportunityService.longInterval):this._toaster.showWarning("Warning!","Cannot Delete Active Quote",this.opportunityService.longInterval):this.quoteId&&this.isCUDEnabled&&this._util.openConfirmationSweetAlertWithCustom("Are you sure ?",`Do you want to delete ${this.quoteForm.get("quoteName").value} Quote`).then(e=>{e&&this._quoteMainService.deleteQuote(this.quoteId,this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{e&&"S"==e.messType?(this._toaster.showSuccess("Success","Quote deleted Successfully !",this.opportunityService.longInterval),this.navigateToLandingPage()):this._toaster.showError("Error",e.messText,this.opportunityService.longInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in deleting Quote",this.opportunityService.mediumInterval)})})},this.cancelChanges=()=>Object(f.c)(this,void 0,void 0,(function*(){if(!this.isChangesMade)return this.isEditMode=!1;this.quoteId&&(this.toggleSpinner(!0),yield this.getQuoteDetails(),this.alloteFieldConfig(),this.patchQuoteDetails(this.wholeQuoteData),this.patchMilestoneDetailsUsingForm(),this.toggleSpinner(!1))})),this.deleteService=e=>{const t=this.servicesFormArr.at(e);null!=t.get("quoteServiceId").value?this._util.openConfirmationSweetAlertWithCustom("Are you Sure ?","Do you want to delete this "+(t.get("serviceId").value?"Service":"Section")).then(t=>{t&&(this.servicesFormArr.removeAt(e),this.calculateOverallRevenue(),this.calculateOverallCost(),this.calculateOverallGM())}):(this.servicesFormArr.removeAt(e),this.calculateOverallRevenue(),this.calculateOverallCost(),this.calculateOverallGM())},this.getCurrentPositionData=(e,t)=>{const n=(e.get("isNonManpower").value?this.nmpList:e.get("isLicense").value?this.licenseList:this.positionList).find(e=>e.id===t);return e.get("positionName").patchValue(n?n.name:null),n},this.resolveSDMapping=(e,t)=>{if(this.sDMappingEnabled&&this.sDMappingList.length){const n=e.get("serviceId").value;if(null!=n){const e=this.sDMappingList.find(e=>e.service_id===n);t.get("division").patchValue(e?e.division_id:null,{emitEvent:!1})}if(!n||null==n){const e=this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);t.patchValue(Object.assign({},e))}}},this.validateSalesRegionGeoMapping=e=>{const t=this.customizeFields.find(e=>"revenueRegion"===e.key),n=this.customizeFields.find(e=>"entity"===e.key);for(const i of e.services)for(const e of i.positions){if(e.revenueRegion){const i=this.revenueRegionMapping.find(t=>{var n;return t.sales_region_id===((null===(n=this.wholeQuoteData)||void 0===n?void 0:n.sales_region)||this.oppQuoteDetails.sales_region)&&t.legal_entity_id===e.entity});if(i&&i.revenue_region_id!=e.revenueRegion)return this._toaster.showError(`${t?t.label:"Geographical Region"} <-> ${n?n.label:"Entity"} <-> Sales Region Mapping is Invalid`,`${t?t.label:"Geographical Region"} Selected for Position - ${e.positionName} doesn't match with ${n?n.label:"Entity"} <-> Sales Region Mapped.`,3e3),!1}if(e.nmpData&&e.nmpData.length)for(const i of e.nmpData)if(i.revenueRegion){const o=this.revenueRegionMapping.find(e=>{var t;return e.sales_region_id===((null===(t=this.wholeQuoteData)||void 0===t?void 0:t.sales_region)||this.oppQuoteDetails.sales_region)&&e.legal_entity_id===i.entity});if(o&&o.revenue_region_id!=i.revenueRegion)return this._toaster.showError(`${t?t.label:"Geographical Region"} <-> ${n?n.label:"Entity"} <-> Sales Region Mapping is Invalid`,`${t?t.label:"Geographical Region"} Selected for Position - ${e.positionName} doesn't match with ${n?n.label:"Entity"} <-> Sales Region Mapped.`,3e3),!1}}return!0},this.validateWEMapping=e=>{const t=this.customizeFields.find(e=>"workLocation"===e.key),n=this.customizeFields.find(e=>"entity"===e.key);for(const i of e.services)for(const e of i.positions){if(e.work_location){const i=this.wEMappingList.find(t=>t.work_location_id===e.work_location);if(i&&i.entity_id!=e.entity)return this._toaster.showError(`${t?t.label:"Work Location"} <-> ${n?n.label:"Entity"} Mapping is Invalid`,`${n?n.label:"Entity"} Selected for Position - ${e.positionName} doesn't match with ${t?t.label:"Work Location"} Mapped (Kindly remove and Position)`,this.opportunityService.longInterval),!1}if(e.nmpData&&e.nmpData.length)for(const i of e.nmpData)if(i.work_location){const o=this.wEMappingList.find(e=>e.work_location_id===i.work_location);if(o&&o.entity_id!=i.entity)return this._toaster.showError(`${t?t.label:"Work Location"} <-> ${n?n.label:"Entity"} Mapping is Invalid`,`${n?n.label:"Entity"} Selected for Position - ${e.positionName} doesn't match with ${t?t.label:"Work Location"} Mapped (Kindly remove and Position)`,this.opportunityService.longInterval),!1}}return!0},this.validateSDMapping=e=>{const t=this.customizeFields.find(e=>"division"===e.key);for(const n of e.services){if(null==n.serviceId)return this._toaster.showError(`Service <-> ${t?t.label:"Division"} Mapping is Invalid`,`Kindly remove Section - ${n.serviceName} as Service <-> ${t?t.label:"Division"} Mapping is Enabled !`,this.opportunityService.longInterval),!1;const e=this.sDMappingList.find(e=>e.service_id===n.serviceId);if(e)for(const i of n.positions){if(e.division_id!=i.division)return this._toaster.showError(`Service <-> ${t?t.label:"Division"} Mapping is Invalid`,`${t?t.label:"Division"} Selected for Position - ${i.positionName} doesn't match with Service Mapped (Kindly remove and Position)`,this.opportunityService.longInterval),!1;if(i.nmpData&&i.nmpData.length)for(const n of i.nmpData)if(e.division_id!=n.division)return this._toaster.showError(`Service <-> ${t?t.label:"Division"} Mapping is Invalid`,`${t?t.label:"Division"} Selected for Position - ${n.positionName} doesn't match with Service Mapped (Kindly remove and Position)`,this.opportunityService.longInterval),!1}}return!0},this.getCalendarDataForTheDuration=(e,t)=>Object(f.c)(this,void 0,void 0,(function*(){let n=e.get("startDate").value,i=e.get("endDate").value;const o=this.unitList.concat(this.otherUnitList).find(t=>t.id===e.get("unit").value);if(n&&i){o&&o.unit_value_in_hrs>24&&!o.is_value_fixed&&(n=P()(n).startOf("month").toISOString(),i=P()(i).endOf("month").toISOString());const s={quoteId:this.quoteId,quotePositionId:t.quotePositionId,resourceTypeId:e.get("isNonManpower").value?2:e.get("isLicense").value?3:1,positionLevel:1};let r=this.calendarId;e.contains("calendarId")&&(r=e.get("calendarId").value||this.calendarId);try{const e=yield this._quoteMainService.getQuoteCalendarData(n,i,s,!0,r,this.workScheduleId).toPromise();return"S"===e.messType&&e.data&&e.data.length?e.data||[]:(this._toaster.showError("Error",e.messText||"Error in getting Quote Calendar details",4500),!1)}catch(a){return console.error(a),this._toaster.showError("Error","Error in getting Quote Calendar details",4500),!1}}return[]})),this.getDurationInDays=(e,t)=>{let n=0;return P()(e).isValid()&&P()(t).isValid()&&(n=P()(t).diff(e,"days")),n},this.getCalendarCombinationConfig=()=>new Promise((e,t)=>{this._quoteMainService.getCalendarCombinationConfig().pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{t&&"S"==t.messType&&t.data?this.calendarCombinationConfig=t.data:this._toaster.showError("Error","Error in getting Quote calendar combination config ",this.opportunityService.longInterval),e(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote calendar combination config ",this.opportunityService.longInterval),this.navigateToLandingPage(),t(e)})}),this.getRevenueRegionCombinationConfig=()=>new Promise((e,t)=>{this._quoteMainService.getRevenueRegionCombinationConfig().pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{t&&"S"==t.messType&&t.data?this.revenueRegionMapping=t.data:this._toaster.showError("Error","Error in getting Quote Geographical Region config ",3e3),e(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Geographical Region config ",3e3),this.navigateToLandingPage(),t(e)})}),this.getOppMetaDetails=()=>new Promise((e,t)=>{this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{t&&"S"==t.messType&&t.data&&(this.oppQuoteDetails=t.data),e(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Opportunity Meta details",3e3),t(e)})}),this.getMilestoneListQB=()=>new Promise((e,t)=>{var n;let i=[];for(const o of this.servicesFormArr.controls){const e=o.get("positions");for(const t of e.controls){const e=null===(n=t.get("quotePositionId"))||void 0===n?void 0:n.value;e&&i.push(e)}}this._quoteMainService.getMilestoneListQB(i).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{t&&"S"==t.messType&&t.data?(this.milestoneList=t.data,this.milestoneApplicable&&(this.customizeFields.find(e=>"milestone"==e.key).masterData=this.milestoneList)):this._toaster.showError("","Error in getting Milestone Data",this.opportunityService.longInterval),e(!0)},e=>{console.log(e),this._toaster.showError("","Error in getting Milestone Data",this.opportunityService.longInterval),this.navigateToLandingPage(),t(e)})}),this.checkMilestoneApplicable=()=>Object(f.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this._quoteMainService.checkMilestoneApplicable(null,this.quoteId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>Object(f.c)(this,void 0,void 0,(function*(){var n,i;t&&"S"==t.messType&&(this.milestoneApplicable=!(!this.isQuoteCreateMode||!(null===(i=null===(n=this.lumpsum_quote_config)||void 0===n?void 0:n.service_type)||void 0===i?void 0:i.includes(this.quoteForm.get("serviceOppTypeId").value)))||t.data),e(!0)})),e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote calendar combination config ",this.opportunityService.longInterval),this.navigateToLandingPage(),t(e)})})})),this.openMilestoneDialog=()=>Object(f.c)(this,void 0,void 0,(function*(){if("VALID"!=this.quoteForm.status)for(const n of this.servicesFormArr.controls)for(const e of n.get("positions").controls)if("INVALID"==e.status){for(const t of e.get("nmpData").controls)if("INVALID"==t.status)return this.resolveMandatoryMsg(t);return this.resolveMandatoryMsg(e)}this.patchMilestoneDetailsUsingForm();const e=this.quoteForm.getRawValue();let t=this.getAllQuoteData(e,this.skillList,this.servicesFormArr,this.calendarId,this.opportunityId,this.workScheduleId,this.defaultCurrency,this.conversionTypeId,this.initial_delivery_start_date,this.initial_delivery_end_date);console.log("quoteData",t);const{MilestoneDialogComponent:i}=yield n.e(301).then(n.bind(null,"SCxD")),o=this.dialog.open(i,{minHeight:"60vh",minWidth:"65vw",data:{quoteData:t,milestoneFieldConfig:this.milestoneFieldConfig,quoteForm:this.quoteForm,milestoneFormArray:this.milestoneFormArray.value,milestoneFormGroup:this.milestoneFormGroup,customizeFields:this.customizeFields,mandatoryFields:this.mandatoryFields,licenseList:this.licenseList,positionList:this.positionList,wEMappingEnabled:this.wEMappingEnabled,sDMappingEnabled:this.sDMappingEnabled,opportunityId:this.opportunityId,defaultCurrency:this.defaultCurrency,conversionTypeId:this.conversionTypeId,nmpList:this.nmpList,skillList:this.skillList,milestoneList:this.milestoneList,positionDropZones:this.positionMilestoneDropZones,quoteCurrency:this.quoteForm.get("quoteCurrency").value,isEditMode:!this.isMilestoneTaggingEnabled&&this.isEditMode},disableClose:!0});console.log(this.isMilestoneTaggingEnabled,"--"),o.afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t,n,i,o,a,s,r,l,c,u,d;if(e&&"submit"==e.event&&e.data){const p=e.data,m=e.milestoneList;this.milestoneListCopy=[...this.milestoneList],m.forEach(e=>{const t=this.milestoneListCopy.findIndex(t=>t.id===e.id);-1!==t?this.milestoneListCopy[t]=e:(console.log("About to Push"),this.milestoneListCopy.push(e))}),console.log(this.milestoneList,"OG"),console.log(this.milestoneListCopy,"BOG");let h=this.customizeFields.find(e=>"milestone"==e.key);h.masterData=[],this.milestoneList=[],this.milestoneList=[...this.milestoneListCopy],h.masterData=[...this.milestoneListCopy],console.log(this.milestoneList,"After List Change"),console.log(this.milestoneList);for(const e of this.servicesFormArr.controls)for(const m of e.get("positions").controls){const e=null===(t=m.get("quotePositionId"))||void 0===t?void 0:t.value,h=p.service.flatMap(e=>e.position).find(t=>t.quote_position_id===e);h&&((null===(n=m.get("milestone"))||void 0===n?void 0:n.value)!=h.milestone_id&&(null===(i=m.get("milestone"))||void 0===i||i.patchValue(null,{emitEvent:!1}),setTimeout(()=>{var e;null===(e=m.get("milestone"))||void 0===e||e.patchValue(h.milestone_id,{emitEvent:!1})},1e3)),(null===(o=m.get("milestoneDetails"))||void 0===o?void 0:o.value)!=h.milestone&&(null===(a=m.get("milestoneDetails"))||void 0===a||a.patchValue(h.milestone,{emitEvent:!1})),(null===(s=m.get("startDate"))||void 0===s?void 0:s.value)!=h.start_date&&(null===(r=m.get("startDate"))||void 0===r||r.patchValue(h.start_date,{emitEvent:!1})),(null===(l=m.get("endDate"))||void 0===l?void 0:l.value)!=h.end_date&&(null===(c=m.get("endDate"))||void 0===c||c.patchValue(h.end_date,{emitEvent:!1})),(null===(u=m.get("isPositionEffortChanged"))||void 0===u?void 0:u.value)!=h.is_position_effort_changed&&(null===(d=m.get("isPositionEffortChanged"))||void 0===d||d.patchValue(h.is_position_effort_changed,{emitEvent:!1})))}this.patchMilestoneDetailsUsingForm()}})))})),this.checkQuoteEditableBasedOnConfig=e=>Object(f.c)(this,void 0,void 0,(function*(){this._landingPageService.checkQuoteEditableBasedOnConfig(e).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){e&&!e.allowed&&(this.editRestricted=!e.allowed,this.editRestrictedMsg=e.msg)})))})),this.initializeForm()}ngOnInit(){return Object(f.c)(this,void 0,void 0,(function*(){this.routeDataSubscription.add(this.route.data.pipe(Object(S.a)(this._onDestroy)).subscribe(({opportunity:e})=>{this.opportunityId=parseInt(e.opportunityId)})),yield this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{e&&"S"==e.messType&&e.data&&(this.oppServiceType=e.data.service_type)}),yield this.loadProjectDetailsForOpportunity(),this.routeDataSubscription.add(this.route.params.pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{this.quoteId=null,e&&e.quoteId&&parseInt(e.quoteId)&&(this.quoteId=parseInt(e.quoteId)),this.quoteId||(this.isQuoteCreateMode=!0,this.quoteCreateDataSubscription&&this.quoteCreateDataSubscription.unsubscribe(),this.quoteCreateDataSubscription=this._quoteMainService.intialQuoteDetail.pipe(Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){if(e&&e.quoteName&&e.currency){const t=yield this.getQuoteTypeConfig(e.quoteType);this.quoteForm.patchValue({quoteName:e.quoteName,quoteCurrency:e.currency,deliveryStartDate:e.deliveryStartDate||null,deliveryEndDate:e.deliveryEndDate||null,quoteBusinessType:e.businessType||null,enableMilestoneTagging:"TRADITIONAL"==e.quoteType,serviceOppTypeId:e.serviceTypeId||null,quoteType:e.quoteType,allowNegativeNumber:1==(null==t?void 0:t.allow_negative_number)||!1},{emitEvent:!1}),this.initial_creation_qb_data=e,this.quoteDuration=this.getDurationInDays(this.initial_creation_qb_data.deliveryStartDate,this.initial_creation_qb_data.deliveryEndDate)}else this.navigateToLandingPage()})))),this.checkQuoteEditableBasedOnConfig(this.quoteId)})),this.getQuoteConfiguration()}))}loadProjectDetailsForOpportunity(){return Object(f.c)(this,void 0,void 0,(function*(){try{this.projectDetailsForOpportunity=yield this.opportunityService.getOpportunityProjectDetailsForQuote(this.opportunityId)}catch(e){console.error("Error Retrieving Project Details for this Opportunity",e)}}))}alloteFieldConfig(){var e;this.project_access_field_config&&this.projectDetailsForOpportunity&&"S"==this.projectDetailsForOpportunity.messType&&(null===(e=this.projectDetailsForOpportunity.messData)||void 0===e?void 0:e.length)>0&&this.isQuoteActive&&this.checkAndAlloteProjectFieldConfig()}isDragDisabled(e,t){return!this.isEditMode||!t.get("interServicePositionDragNDrop").value||1==e.get("positions").controls.length}newAddedPostionAfterProjectInterationConfig(e){const t={};return e.forEach(e=>{t[null==e?void 0:e.variableName]=e.editEnabled}),t}allocateDefaultValues(e){var t,n;const i={};e.forEach(e=>{var t;i[e.key]=null!==(t=null==e?void 0:e.defaultValue)&&void 0!==t?t:null}),i.typeOfBusiness=(null===(t=this.quoteForm)||void 0===t?void 0:t.get("quoteBusinessType").value)||(null===(n=this.wholeQuoteData)||void 0===n?void 0:n.business_type);let o=this.milestoneList.sort((e,t)=>e.id-t.id).find(e=>!e.is_master||0===e.is_master)||this.milestoneList.find(e=>1===e.is_master);return i.milestone=null!=i.milestone&&(null==o?void 0:o.id)||null,i}checkAndAlloteProjectFieldConfig(){const e={positionId:"isPositionFieldEnabled",unit:"isUnitEnabled",workLocation:"isWorkLocationEnabled",quantity:"isQuantityEnabled",noOfResources:"isNoOfResourcesEnabled",ratePerUnit:"isRatePerUnitEnabled",costPerUnit:"isCostPerUnitEnabled",add_service:"isAddServiceEnabled",delete_service:"isDeleteServiceEnabled",add_position:"isAddPositionEnabled",add_position_inline:"isAddPositionInlineEnabled",delete_position:"isDeletePositionEnabled",clone_position:"isClonePositionEnabled",customise_slot:"isCustomiseSlotEnabled",add_tax:"isTaxButtonEnabled",add_license:"isLicenseButtonEnabled",add_tAndl:"isNonManPowerButtonEnabled",add_discount:"isDiscountButtonEnabled",enable_position_drag:"interServicePositionDragNDrop",typeOfBusiness:"isTypeOfBusinessEnabled",positionStatus:"isPositionStatusEnabled",nationality:"isNationalityEnabled",experience:"isWorkExperienceEnabled",add_section:"isAddSectionEnabled",revenueRegion:"isRevenueRegionEnabled",positionStatusToLost:"isPositionStatusToLost",milestone:"isMilestoneEnabled",add_milestone:"isMilestoneButtonEnabled"};for(const t of this.project_access_field_config){const n=e[t.key];n&&(this[n]=t.editEnabled)}for(const t of this.addButtonsList)switch(t.key){case"service":t.has_project_integrated=this.isAddServiceEnabled;break;case"position":t.has_project_integrated=this.isAddPositionEnabled;break;case"nonManpower":t.has_project_integrated=this.isNonManPowerButtonEnabled;break;case"license":t.has_project_integrated=this.isLicenseButtonEnabled;break;case"discount":t.has_project_integrated=this.isDiscountButtonEnabled;break;case"tax":t.has_project_integrated=this.isTaxButtonEnabled;break;case"milestone":t.has_project_integrated=this.isMilestoneButtonEnabled}}checkAndAllocateCalendar(e,t,n,i){const o=this.calendarCombinationConfig.find(o=>o.entity_id===e&&o.service_type_id===t&&o.location_id===n&&o.resource_type_id===i);return o?o.calendar_id:this.calendarId}checkAndAllocateRevenueRegion(e,t){const n=this.revenueRegionMapping.find(n=>n.sales_region_id===e&&n.legal_entity_id===t);return n?n.revenue_region_id:null}checkNegativeAllocation(){for(const e of this.customizeFields)e.allowNegative=this.quoteForm.get("allowNegativeNumber").value&&e.allowNegative}checkIfFieldShouldBeMasked(e){var t,n;const i=null===(t=this.masking_configuration)||void 0===t?void 0:t.masking_field_config.find(t=>t.key===e.key);let o=this._ticket.getCurrentUserRole();return console.log(o,"role"),!(!(null===(n=this.masking_configuration)||void 0===n?void 0:n.maskingEnabled)||!i||(null==i?void 0:i.role_id.includes(o)))}formatNumberByCurrency(e,t){return"INR"===t?new Intl.NumberFormat("en-IN",{maximumFractionDigits:0}).format(e):new Intl.NumberFormat("en-US",{maximumFractionDigits:0}).format(e)}checkFieldShouldbeMasked(e,t){var n,i;const o=this._ticket.getCurrentUserRole(),a=null===(n=this.masking_configuration)||void 0===n?void 0:n.masking_field_config.find(n=>n.resource_type===t&&n.key.includes(e));return!!((null===(i=this.masking_configuration)||void 0===i?void 0:i.maskingEnabled)&&a&&a.role_id.includes(o))}checkFieldShouldbeRestricted(e){const t=this._ticket.getCurrentUserRole(),n=this.fieldConfig.find(t=>t.key===e);return!(!(null==n?void 0:n.isActive)||(null==n?void 0:n.role_access_restriction)&&n.role_access_restriction.includes(t))}get servicesFormArr(){return this.quoteForm.get("services")}get milestoneFormArrayMain(){return this.milestoneFormGroup.get("allMilestones")}checkMandatory(){return Object(f.c)(this,void 0,void 0,(function*(){const e=this.quoteForm.getRawValue(),t=[],n=(e,n="")=>{for(const o of this.customizeFields){const a="quotePostitionId"===o.key,s="dropdown"===o.fieldType;if(o.isMandatory&&!a&&s&&(null==(i=e[o.key])||"string"==typeof i&&""===i.trim())){const e=o.label||o.key;t.push(n?`${n} - ${e}`:e)}}var i};if(Array.isArray(e.services)&&e.services.forEach(e=>{Array.isArray(e.positions)&&e.positions.forEach(e=>{n(e,""+e.positionName)})}),t.length){const e=""+t.join(", ");return this._toaster.showError("Mandatory Fields Error",e,this.opportunityService.longInterval),!1}return!0}))}applyDiscountToPosition(e,t){var n,i;e.contains("originalRatePerUnit")||e.addControl("originalRatePerUnit",this.fb.control(null));let o=null===(n=e.get("originalRatePerUnit"))||void 0===n?void 0:n.value;const a=e.get("ratePerUnit").value;o||(o=a,null===(i=e.get("originalRatePerUnit"))||void 0===i||i.setValue(o,{emitEvent:!1}));const s=o-o*(t/100);e.get("ratePerUnit").patchValue(s,{emitEvent:!1})}isQuoteFullyDiscounted(){const e=this.quoteForm.get("discounts");if(!(null==e?void 0:e.length))return!1;let t=1;return e.controls.forEach(e=>{var n;const i=+(null===(n=e.get("discountPercentage"))||void 0===n?void 0:n.value)||0;t*=1-i/100}),0===t}addDiscountAndTax(e,t){const n=this.quoteForm.get(t),i="discounts"===t?this.getDiscountsFormGroup():this.getTaxesFormGroup();n.insert(e+1,i)}applyAllDiscountsToPositions(){const e=this.quoteForm.get("discounts");this.servicesFormArr.controls.forEach(t=>{t.get("positions").controls.forEach(t=>{const n=e.controls.map(e=>e.get("discountPercentage").value||0).reduce((e,t)=>e+t,0);this.applyDiscountToPosition(t,n)})})}scrollToBottom(){this.scrollContainer.scroll({top:this.scrollContainer.scrollHeight,left:0,behavior:"smooth"})}openCostOverlay(e){var t;if(!(null===(t=this.costOverlayRef)||void 0===t?void 0:t.hasAttached())){const t=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"bottom",offsetX:0,offsetY:-e.offsetHeight}]),n=this.overlay.scrollStrategies.close();this.costOverlayRef=this.overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,backdropClass:"quote-transparent-overlay-backdrop"});const i=new b.h(this.costTemplateRef,this.viewContainerRef);this.costOverlayRef.attach(i),this.costOverlayRef.backdropClick().subscribe(()=>{this.closeCostOverlay()})}}closeCostOverlay(){var e;null===(e=this.costOverlayRef)||void 0===e||e.dispose(),this.costSummary={resourceCost:0,nonManpowerCost:0,total:0}}openRevSummaryOverlay(e){var t;if(!(null===(t=this.revOverlayRef)||void 0===t?void 0:t.hasAttached())){const t=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"bottom",offsetX:0,offsetY:-e.offsetHeight}]),n=this.overlay.scrollStrategies.close();this.revOverlayRef=this.overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,backdropClass:"quote-transparent-overlay-backdrop"});const i=new b.h(this.revTemplateRef,this.viewContainerRef);this.revOverlayRef.attach(i),this.revOverlayRef.backdropClick().subscribe(()=>{this.closeRevOverlay()})}}closeRevOverlay(){var e;null===(e=this.revOverlayRef)||void 0===e||e.dispose(),this.revenueSummary={revenue:0,cost:0,grossMargin:0}}openQuoteOverlay(e){var t;if(this.isEditMode&&!(null===(t=this.quoteOverlayRef)||void 0===t?void 0:t.hasAttached())){this.quoteNameFormControl.patchValue(this.quoteForm.get("quoteName").value);const t=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]),n=this.overlay.scrollStrategies.close();this.quoteOverlayRef=this.overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,backdropClass:"quote-transparent-overlay-backdrop"});const i=new b.h(this.quoteTemplateRef,this.viewContainerRef);this.quoteOverlayRef.attach(i),this.quoteOverlayRef.backdropClick().subscribe(()=>{this.closeQuoteOverlay()})}}closeQuoteOverlay(){var e;null===(e=this.quoteOverlayRef)||void 0===e||e.dispose()}changeQuoteName(){const e=this.quoteNameFormControl.value.trim();if(!e)return this._toaster.showError("Mandatory Field Error","Kindly enter Quote Name",this.opportunityService.mediumInterval);this.quoteForm.get("quoteName").patchValue(e),this.closeQuoteOverlay()}getUserFieldConfig(){this._quoteMainService.getUserFieldConfig().pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{if("S"==e.messType&&e.data.field_config&&e.data.field_config instanceof Array){for(let t=0;t<this.customizeFields.length;t++){const n=e.data.field_config.find(e=>e.field_key===this.resolveFieldMapping(this.customizeFields[t].key));n&&(this.customizeFields[t].isVisible.patchValue(n.is_visible||!1),this.customizeFields[t].fieldPosition=n.field_position)}this.sortFieldConfig()}},e=>{console.log(e),this._toaster.showError("Error","Error in getting Field config details",this.opportunityService.longInterval)})}resolveOrgMapping(e,t,n){if(!this.wEMappingEnabled&&!this.sDMappingEnabled)if("entity"==e)if(null==t.entity)n.get("division").patchValue(null,{emitEvent:!1}),n.get("subDivision").patchValue(null,{emitEvent:!1}),n.get("divisionMasterData").patchValue(this.divisionList,{emitEvent:!1}),n.get("subDivisionMasterData").patchValue(this.subDivisionList,{emitEvent:!1});else{n.get("division").disabled&&n.get("division").enable({emitEvent:!1});const e=this.orgMappingList.filter(e=>e.entity_id===t.entity),i=e.map(e=>e.division_id),o=e.map(e=>e.sub_division_id);n.get("divisionMasterData").patchValue(this.divisionList.filter(e=>i.includes(e.id)),{emitEvent:!1}),n.get("subDivisionMasterData").patchValue(this.subDivisionList.filter(e=>o.includes(e.id)),{emitEvent:!1}),null==t.division||i.includes(t.division)||n.get("division").patchValue(null),null==t.subDivision||o.includes(t.subDivision)||n.get("subDivision").patchValue(null)}else if("division"==e)if(null==t.division)n.get("subDivision").patchValue(null,{emitEvent:!1}),n.get("subDivisionMasterData").patchValue(this.subDivisionList,{emitEvent:!1});else{n.get("subDivision").disabled&&n.get("subDivision").enable({emitEvent:!1});const e=this.orgMappingList.filter(e=>e.division_id===t.division&&(null==t.entity||e.entity_id==t.entity)).map(e=>e.sub_division_id);n.get("subDivisionMasterData").patchValue(this.subDivisionList.filter(t=>e.includes(t.id)),{emitEvent:!1}),null==t.subDivision||e.includes(t.subDivision)||n.get("subDivision").patchValue(null)}}selectCurrency(e){if(this.quoteForm.get("quoteCurrency").value!==e.name){this.quoteForm.patchValue({quoteCurrency:e.name},{emitEvent:!0}),this.quoteForm.patchValue({quoteCurrencyId:e.currency_id});for(const e of this.servicesFormArr.controls)for(const t of e.get("positions").controls){t.get("isPositionValueChanged").patchValue(!0,{emitEvent:!1});for(const e of t.get("nmpData").controls)e.get("isPositionValueChanged").patchValue(!0,{emitEvent:!1})}}}openApproversPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){"SEND"===t&&(yield this.getQuoteApprovers(e)),"SEE"===t&&(yield this.getQuoteStatus(e)),this.submitQuoteForApprovalRef=this.dialog.open(this.submitQuoteForApprovalTemplate,{width:"30rem",minHeight:"40vh",data:{type:"SUBMIT",quoteDetails:e,popUpType:t},animation:{entryAnimation:{keyframes:[{transform:"scale(0.5) translateY(100%)",opacity:0},{transform:"scale(1) translateY(0)",opacity:1}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"scale(1) translateY(0)",opacity:1},{transform:"scale(0.5) translateY(100%)",opacity:0}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}},disableClose:!0})}))}getQuoteApprovers(e){return Object(f.c)(this,void 0,void 0,(function*(){this.miniLoader=!0,this._landingPageService.getQuoteApprovers({quoteDetails:e}).subscribe(e=>{console.log("Approvers:",e),this.reviewersList=null==e?void 0:e.data,0==this.reviewersList.length&&this._toaster.showWarning("No Approvers Found for the selected type!","",this.opportunityService.longInterval),this.miniLoader=!1},e=>{console.error("Error fetching approvers:",e),this.miniLoader=!1})}))}getQuoteStatus(e){var t;return Object(f.c)(this,void 0,void 0,(function*(){this.miniLoader=!0;try{let n={quote_id:e.quote_header_id};const i=yield new Promise((e,t)=>{this._landingPageService.getQuoteStatus(n).subscribe(t=>e(t),e=>t(e))});console.log("Approvers:",i);let o=i.data.approvers;this.submitterDetails=null==i?void 0:i.data.submitter[0],this.reviewersList=null==i?void 0:i.data.approvers,o.find(e=>!0===e.isApprover);const a=(null===(t=this.reviewersList.find(e=>e.comments))||void 0===t?void 0:t.comments)||" ";this.commentToSubmitter=a||" ",0===this.submitterDetails.length&&this._toaster.showWarning("No Approvers Found for the selected type!","",this.opportunityService.longInterval)}catch(n){console.error("Error fetching approvers:",n)}finally{this.miniLoader=!1}}))}openApproverDialog(e){var t,n;return Object(f.c)(this,void 0,void 0,(function*(){yield this.getQuoteStatus(e),this.isCUDEnabled||2!==(null==e?void 0:e.quote_status)||3!==(null==e?void 0:e.quote_status)||this.openApproversPopUp(e,"SEE"),this.approveOrRejectRef=this.dialog.open(this.approveOrRejectTemplate,{width:"35rem",minHeight:"45vh",data:{quoteDetails:e,submitterDetails:this.submitterDetails,approverDetails:this.reviewersList},disableClose:!0,panelClass:"custom-mat-dialog-panel"});const i=yield this.approveOrRejectRef.afterClosed().toPromise();if("CANCEL"!==i){if("APPROVE"===i){let n={workflowId:e.approval_workflow_header_id,approval_status:"A",comments:this.commentToSubmitter,quoteDetails:e};try{const e=yield this._landingPageService.approveOrReject(n).toPromise();console.log("Approvers:",e),e&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Approved!","",this.opportunityService.shortInterval))}catch(o){console.error("Error approving quote:",o);let e=null===(t=null==o?void 0:o.error)||void 0===t?void 0:t.err;"WORKFLOW_TRIGGERED"===(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Cannot Approve Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!==(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Error approving quote!",(null==e?void 0:e.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error approving quote!","Kindly try again after sometime",this.opportunityService.mediumInterval),this.miniLoader=!1}}if("REJECT"===i){let t={workflowId:e.approval_workflow_header_id,approval_status:"R",comments:this.commentToSubmitter,quoteDetails:e};try{const e=yield this._landingPageService.approveOrReject(t).toPromise();console.log("Approvers:",e),e&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Rejected!","",this.opportunityService.shortInterval))}catch(o){console.error("Error rejecting quote:",o);let e=null===(n=null==o?void 0:o.error)||void 0===n?void 0:n.err;"WORKFLOW_TRIGGERED"===(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Cannot Reject Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!==(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Error rejecting quote!",(null==e?void 0:e.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error rejecting quote!","Kindly try again after sometime",this.opportunityService.mediumInterval),this.miniLoader=!1}}}}))}getStatusCss(e){switch(e){case 4:return{background:"#E8E9EE",color:"#45546E"};case 1:return{background:"#FFF3E8",color:"#FA8C16"};case 2:return{background:"#EEF9E8",color:"#52C41A"};case 3:return{background:"#FFEBEC",color:"#FF3A46"};default:return{}}}closeApproverPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){if(console.log(e),"APPROVE"===t)this.approveOrRejectRef.close(t);else if("REJECT"===t){if(!this.commentToSubmitter||""===this.commentToSubmitter.trim())return void this._toaster.showWarning("Please enter comment before rejecting approval!","",this.opportunityService.mediumInterval);this.approveOrRejectRef.close(t)}else this.approveOrRejectRef.close("CANCEL")}))}closeSubmitApprovalPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){console.log(e),this.submitQuoteForApprovalRef.close()}))}getApproverTooltip(e){return`${(null==e?void 0:e.aid)||(null==e?void 0:e.associate_id)} - ${e.employee_name||""}\nStatus: ${e.status_name}`}getQuoteTypeConfig(e){return new Promise((t,n)=>{this._quoteMainService.getQuoteTypeConfig(e).subscribe({next:e=>{"S"===(null==e?void 0:e.messType)&&(null==e?void 0:e.data)?t(e.data):(this._toaster.showError("Error","Cannot retrive quote type configurations",this.opportunityService.longInterval),t(!1))},error:e=>{console.error(e),this._toaster.showError("Error","Error in getting Quote Opportunity Meta details",3e3),n(e)}})})}getAllQuoteData(e,t,n,i,o,a,s,r,l,c){let u={},d=[],p={items:[]};for(const[m,h]of e.services.entries()){let o=[];for(const[a,s]of h.positions.entries()){if(!s.isLicense&&!s.isNonManpower&&!t.find(e=>e.id==s.positionId))return n.at(m).get("positions").at(a).patchValue({positionId:null,positionName:null},{emitEvent:!1}),this._toaster.showError("Mandatory Field Error","Kindly Select Valid Position",this.opportunityService.longInterval),{isEditMode:!0};let r=[];if(s.nmpData&&s.nmpData.length)for(const t of s.nmpData)r.push({qp_item_id:t.quotePositionId,nmp_id:t.positionId,name:t.positionName||null,no_of_resource:t.noOfResources,rate_per_unit:t.ratePerUnit,original_rate:t.originalRatePerUnit,cost_per_unit:t.costPerUnit,entity:t.entity,division:t.division,sub_division:t.subDivision,work_location:t.workLocation,nationality:t.nationality,unit_id:t.unit,quantity:t.quantity,resource_type_id:2,qpi_currency:e.quoteCurrency,qpi_amount:t.totalCost,qpi_revenue_amount:t.totalRevenue,start_date:t.startDate,end_date:t.endDate,position_effort:t.positionEffortData||null,is_position_effort_changed:t.isPositionEffortChanged||t.isQuantityChanged||!1,is_position_value_changed:t.isPositionValueChanged,rc_value:t.rcValue,business_type:t.typeOfBusiness,milestone_id:t.milestone});o.push({quote_position_id:s.quotePositionId,position:s.positionId,position_name:s.positionName||null,resource_count:s.noOfResources,rate_per_unit:s.ratePerUnit,original_rate:s.originalRatePerUnit,cost_per_unit:s.costPerUnit,work_experience:s.experience,nationality:s.nationality,work_location:s.workLocation,entity:s.entity,division:s.division,sub_division:s.subDivision,unit_id:s.unit,quantity:s.quantity,resource_type_id:s.isNonManpower?2:s.isLicense?3:1,item_currency:e.quoteCurrency,item_revenue_amount:s.totalRevenue,item_cost_amount:s.totalCost,start_date:s.startDate,end_date:s.endDate,position_order:a,position_items:r,position_effort:s.positionEffortData||null,is_position_effort_changed:s.isPositionEffortChanged||s.isQuantityChanged||!1,is_position_value_changed:s.isPositionValueChanged,rc_value:s.rcValue,business_type:s.typeOfBusiness,calendar_id:s.calendarId||i,milestone_id:s.milestone,milestone:s.milestoneDetails})}d.push({quote_service_id:h.quoteServiceId,service_header_id:h.serviceId,service_name:h.serviceName,service_type_id:h.serviceTypeId||null,service_revenue_amount:h.serviceRevenue,service_cost_amount:h.serviceCost,service_currency:e.quoteCurrency,is_fixed_rate:h.isFixedRate||null,service_order:m,position:o})}for(const m of e.discounts)p.items.push({dt_item_id:m.dtItemId,id:m.discountId,name:m.discountName,percentage:m.discountPercentage,type:"D",tax_type:null,item_amount:m.discountValue,item_currency:e.quoteCurrency,is_custom:m.isCustom});for(const m of e.taxes)p.items.push({dt_item_id:m.dtItemId,id:m.taxId,name:m.taxName,percentage:m.taxPercentage,type:"T",tax_type:null,item_amount:m.taxValue,item_currency:e.quoteCurrency,is_custom:!1});return u={quote_name:e.quoteName,quote_currency:e.quoteCurrency,quote_currency_id:e.quoteCurrencyId,initial_currency_id:e.initialCurrencyId||e.quoteCurrencyId,quote_amount:e.totalOrderValue,quote_cost_amount:e.totalCost,quote_revenue_amount:e.totalRevenue,delivery_start_date:e.deliveryStartDate,delivery_end_date:e.deliveryEndDate,opportunity_id:o,service:d,discountAndTax:p,version:e.version,work_schedule_id:a||1,default_currency:s,conversion_type_id:r,initial_delivery_start_date:l,initial_delivery_end_date:c,is_quote_active:e.activeQuote,has_parent_opportunity:e.quoteOpportunityHasParent},e.quoteId&&(u.quote_header_id=e.quoteId),u}get isMilestoneTaggingEnabled(){var e;const t=this.customizeFields.find(e=>"milestone"===e.key);return(null==t?void 0:t.has_project_integrated)&&this.project_access_field_config&&this.projectDetailsForOpportunity&&"S"==this.projectDetailsForOpportunity.messType&&(null===(e=this.projectDetailsForOpportunity.messData)||void 0===e?void 0:e.length)>0&&this.isQuoteActive}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe(),this.routeDataSubscription&&this.routeDataSubscription.unsubscribe(),this.quoteCreateDataSubscription&&this.quoteCreateDataSubscription.unsubscribe(),this.toggleSpinner(!1)}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](v.a),k["\u0275\u0275directiveInject"](v.g),k["\u0275\u0275directiveInject"](u.i),k["\u0275\u0275directiveInject"](p.b),k["\u0275\u0275directiveInject"](k.ViewContainerRef),k["\u0275\u0275directiveInject"](g.e),k["\u0275\u0275directiveInject"](F.a),k["\u0275\u0275directiveInject"](T.a),k["\u0275\u0275directiveInject"](A.a),k["\u0275\u0275directiveInject"](R.c),k["\u0275\u0275directiveInject"](Q.a),k["\u0275\u0275directiveInject"](L.a),k["\u0275\u0275directiveInject"](G),k["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-details-page"]],viewQuery:function(e,t){if(1&e&&(k["\u0275\u0275viewQuery"](Z,!0),k["\u0275\u0275viewQuery"](ee,!0),k["\u0275\u0275viewQuery"](te,!0),k["\u0275\u0275viewQuery"](ne,!0),k["\u0275\u0275viewQuery"](ie,!0),k["\u0275\u0275viewQuery"](oe,!0),k["\u0275\u0275viewQuery"](ae,!0)),2&e){let e;k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.costTemplateRef=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.revTemplateRef=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.quoteTemplateRef=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.approveOrRejectTemplate=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.submitQuoteForApprovalTemplate=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.scrollFrame=e.first)}},features:[k["\u0275\u0275ProvidersFeature"]([{provide:_.c,useClass:y.c,deps:[_.f,y.a]},{provide:_.e,useValue:{parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:67,vars:24,consts:[[1,"container-fluid","quote-creation-styles"],[3,"formGroup","keydown.enter"],[1,"card",2,"box-shadow","none"],[1,"card-body",2,"padding","10px"],[1,"row","align-items-center"],[1,"col-1"],["matTooltip","Back","mat-stroked-button","",1,"back-button",3,"click"],[1,"back-icon"],["cdkOverlayOrigin","",1,"quote-text",3,"ngClass","click"],["quoteNameContainer","","overlayTrigger","cdkOverlayOrigin"],[1,"d-flex","align-items-center","justify-content-start",2,"gap","2%"],[1,"quote-text"],["class","status-btn","mat-button","",3,"ngStyle","ngClass","matTooltip","click",4,"ngIf"],[1,"d-flex","align-items-center"],["class","quote-dot",4,"ngIf"],["class","quote-type",4,"ngIf"],[1,"col-4","slide-in-right"],[1,"row","date-picker-class"],[1,"col-4"],[1,"header"],[1,"date"],[4,"ngIf"],["matInput","","a","","formControlName","deliveryStartDate","readonly","",3,"max","min","matDatepicker"],["matSuffix","",3,"for",4,"ngIf"],["picker1",""],["matInput","","formControlName","deliveryEndDate","readonly","",3,"min","matDatepicker"],["picker2",""],[1,"pt-1","d-flex",2,"font-size","12px","font-weight","500"],["class","ml-2 service-icon-btn","style","cursor: pointer;",3,"matMenuTriggerFor",4,"ngIf"],[1,"custom-mat-menu"],["currencyMenu","matMenu"],[1,"menu-scroll"],["mat-menu-item","","class","menu-header-class",3,"click",4,"ngFor","ngForOf"],["class","col-4 slide-in-right",4,"ngIf"],[1,"card","mt-2",2,"min-height","70vh","box-shadow","none"],["mandatoryTemplate",""],["currencyTemplate",""],["bdColor","rgba(0, 0, 0, 0)","size","medium","color","#cf0001","type","ball-clip-rotate",3,"fullScreen"],[2,"color","#cf0001","margin-top","10vh !important","font-weight","400"],["submitForApproval",""],["approveOrReject",""],["mat-button","",1,"status-btn",3,"ngStyle","ngClass","matTooltip","click"],[1,"quote-dot"],[1,"quote-type"],["matSuffix","",3,"for"],["matDatepickerToggleIcon","",2,"font-size","14px"],[1,"ml-2","service-icon-btn",2,"cursor","pointer",3,"matMenuTriggerFor"],["mat-menu-item","",1,"menu-header-class",3,"click"],[1,"row"],["class","col-4 pr-0",4,"ngIf"],[1,"col-8","d-flex","justify-content-center"],["mat-icon-button","","class","d-flex align-items-center justify-content-center tag-btn",3,"disabled","matTooltip","click",4,"ngIf"],["matTooltip","Cancel Changes","mat-stroked-button","","class","preview-btn ml-2 mt-2",3,"disabled","click",4,"ngIf"],["mat-stroked-button","","matBadge","!","matBadgeSize","small","matBadgeColor","primary",1,"ml-2","mt-2",3,"disabled","matTooltip","ngClass","matBadgeHidden","click"],[4,"ngIf","ngIfElse"],["showSpinner",""],["matTooltip","More Options","mat-stroked-button","",1,"other-btn","ml-2","mt-2",3,"matMenuTriggerFor"],["moreOptionMenu","matMenuTrigger"],[1,"service-icon-btn"],[1,"pt-1","pb-1"],["moreQuoteActions","matMenu"],["class","d-flex align-items-center","class","menu-header-class","matTooltip","Activity Log","mat-menu-item","",3,"click",4,"ngIf"],["matTooltip","Customize Fields","mat-menu-item","",1,"menu-header-class",3,"click"],[1,"menu-item-class"],["class","d-flex align-items-center","class","menu-header-class","matTooltip","Delete Quote","mat-menu-item","",3,"click",4,"ngIf"],[1,"col-4","pr-0"],[1,"revenue-item"],["type","text","matInput","","formControlName","totalOrderValue","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],["mat-icon-button","",1,"d-flex","align-items-center","justify-content-center","tag-btn",3,"disabled","matTooltip","click"],["xmlns","http://www.w3.org/2000/svg","height","20px","viewBox","0 -960 960 960","width","20px","fill","#45546E"],["d","M843-399 562-117q-11 11-24 16t-27 5q-14 0-27-5t-24-16L116.7-460.3Q106-471 101-483.89T96-511v-281q0-29.7 21.15-50.85Q138.3-864 168-864h281q13.91 0 26.96 5 13.04 5 23.77 15.7L843-500q11 11 16 23.5t5 26.5q0 14-5.02 27.09Q853.96-409.83 843-399ZM511-168l281-281-343-343H168v281l343 343ZM264-636q25 0 42.5-17.5T324-696q0-25-17.5-42.5T264-756q-25 0-42.5 17.5T204-696q0 25 17.5 42.5T264-636Zm216 156Z"],["matTooltip","Cancel Changes","mat-stroked-button","",1,"preview-btn","ml-2","mt-2",3,"disabled","click"],["matTooltip","Saving Quote",1,"d-flex","justify-content-center"],[2,"color","#cf0000",3,"diameter"],["matTooltip","Activity Log","mat-menu-item","",1,"menu-header-class",3,"click"],["matTooltip","Delete Quote","mat-menu-item","",1,"menu-header-class",3,"click"],[2,"height","55vh","overflow","auto"],["scrollFrame",""],["cdkDropList","","formArrayName","services",3,"cdkDropListDropped"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"row",2,"background-color","#FDE4E2","line-height","5vh","border-bottom","1px solid #F8AFA7"],["class","col-2",4,"ngIf"],["class","col-2 p-0 d-flex align-items-center",4,"ngIf"],["class","row mt-3",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["sectionTemplateRef",""],["resourceCostRef",""],["revSummaryRef",""],["quoteNameRef",""],[3,"formGroupName"],["cdkDrag","",1,"row","d-flex","align-items-center","drag-class",3,"cdkDragDisabled"],["cdkDragHandle","","mat-icon-button","",4,"ngIf"],[1,"service-name","overflow-class",3,"ngStyle","matTooltip"],["mat-icon-button","","matTooltip","Delete",3,"click",4,"ngIf"],["cdkDropList","",2,"min-height","2vh",3,"id","cdkDropListConnectedTo","cdkDropListDropped"],["formArrayName","positions"],["class","row d-flex flex-nowrap",3,"ngClass",4,"ngIf"],["cdkDragHandle","","mat-icon-button",""],["mat-icon-button","","matTooltip","Delete",3,"click"],["fontSet","material-symbols-outlined",1,"service-icon-btn"],[1,"row","d-flex","flex-nowrap",3,"ngClass"],[3,"ngClass"],[3,"ngTemplateOutlet"],[4,"ngFor","ngForOf"],[3,"class",4,"ngIf"],["cdkDrag","",1,"drag-class",3,"ngStyle","cdkDragDisabled","cdkDragStarted"],[1,"row","d-flex","align-items-center","flex-nowrap"],[1,"d-flex","align-items-center",2,"padding","8px 0px 8px 0px"],["class","uniq-icons",3,"matTooltip",4,"ngIf"],["class","form-field-class","style","width: 80%;","formControlName","positionId",3,"matTooltip","list","hideMatLabel","placeholder","required","hasNoneOption","disabled",4,"ngIf","ngIfElse"],["positionNonEdit",""],[1,"col-1","p-0","pt-2","d-flex","align-items-center"],["matTooltip","Add Position","class","more-btn","mat-icon-button","",3,"click",4,"ngIf"],["matTooltip","Delete Position","class","more-btn","mat-icon-button","",3,"click",4,"ngIf"],["class","more-btn","mat-icon-button","",3,"matTooltip","disabled","click",4,"ngIf"],["matTooltip","Customize Slots","class","more-btn","mat-icon-button","",3,"click",4,"ngIf"],["formArrayName","nmpData"],[1,"uniq-icons",3,"matTooltip"],["formControlName","positionId",1,"form-field-class",2,"width","80%",3,"matTooltip","list","hideMatLabel","placeholder","required","hasNoneOption","disabled"],[1,"display-class",3,"matTooltip"],["class","form-field-class",3,"required","hasNoneOption","list","hideMatLabel","placeholder","formControlName","disabled",4,"ngIf"],[1,"form-field-class",3,"required","hasNoneOption","list","hideMatLabel","placeholder","formControlName","disabled"],[1,"display-class"],["class","form-field-class","appearance","outline",3,"opacity","pointer-events",4,"ngIf"],["appearance","outline",1,"form-field-class"],["type","text","matInput","","appFcCs","",3,"placeholder","formControlName","decimalPart","currency","allowNegative"],["class","suffix-class","matSuffix","",4,"ngIf"],["matSuffix","",1,"suffix-class"],["type","text","matInput","","appFcCs","","readonly","",1,"overflow-class",3,"formControlName","currency","showSuffix","decimalPart","suffix"],["type","text","matInput","","appFcCs","","readonly","",1,"overflow-class",3,"formControlName","currency"],["type","text","matInput","","readonly","",1,"overflow-class",3,"formControlName"],["matTooltip","Add Position","mat-icon-button","",1,"more-btn",3,"click"],[1,"more-btn-icon"],["matTooltip","Delete Position","mat-icon-button","",1,"more-btn",3,"click"],["fontSet","material-symbols-outlined",1,"more-btn-icon"],["mat-icon-button","",1,"more-btn",3,"matTooltip","disabled","click"],["class","more-btn-icon",4,"ngIf"],["class","more-btn-icon d-flex align-items-center justify-content-center","diameter","15","style","color: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;margin-left: 0.25rem;\n                                                        ",4,"ngIf"],["diameter","15",1,"more-btn-icon","d-flex","align-items-center","justify-content-center",2,"color","linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important","margin-left","0.25rem"],["matTooltip","Customize Slots","mat-icon-button","",1,"more-btn",3,"click"],[1,"row","d-flex","align-items-center","flex-nowrap",2,"width","100%"],["style","visibility: hidden;","mat-icon-button","",4,"ngIf"],["positionNonEdit1",""],[1,"col-1","p-0","pt-2"],["mat-icon-button","",2,"visibility","hidden"],["class","form-field-class","appearance","outline",4,"ngIf"],["type","text","matInput","","appFcCs","",3,"placeholder","formControlName","disabledD","currency","readonly","allowNegative"],["type","text","matInput","","appFcCs","","readonly","",1,"overflow-class",3,"formControlName","currency","showSuffix","suffix"],["class","more-btn-icon d-flex align-items-center p-0","diameter","15","style","color: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important",4,"ngIf"],["diameter","15",1,"more-btn-icon","d-flex","align-items-center","p-0",2,"color","linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important"],[1,"row","pt-2","col-12",2,"border-top","1px solid #DADCE2","color","#45546E"],["formArrayName","discounts"],["formArrayName","taxes"],[1,"row","d-flex","align-items-center",3,"ngClass"],[1,"col-2"],["class","dt-class",4,"ngIf"],[1,"col-2","p-0"],["class","label-class",4,"ngIf"],["discountNameNonEdit",""],[1,"col-1","p-0"],["class","form-field-class","appearance","outline",4,"ngIf","ngIfElse"],["discountPerNonEdit",""],[1,"col-1","pr-0","d-flex","flex-column"],["type","text","matInput","","formControlName","discountValue","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],[1,"col-2","d-flex","flex-column"],[1,"dt-class"],[1,"label-class"],["matInput","","placeholder","Enter Discount name","formControlName","discountName"],["class","form-field-class","formControlName","discountId",3,"list","hideMatLabel","placeholder",4,"ngIf","ngIfElse"],["formControlName","discountId",1,"form-field-class",3,"list","hideMatLabel","placeholder"],["type","number","min","0","matInput","","placeholder","Enter %","formControlName","discountPercentage"],[1,"pl-1"],["matTooltip","Add Discount","mat-icon-button","",1,"more-btn",2,"width","40px",3,"click"],["matTooltip","Delete Discount","mat-icon-button","",1,"more-btn",2,"width","40px",3,"click"],["matTooltip","Custom Discount","mat-icon-button","",1,"more-btn","pl-2",2,"width","40px",3,"click"],[1,"more-btn-icon",2,"font-size","25px",3,"ngStyle"],[1,"dt-class",2,"line-height","5vh","font-size","13px"],["type","text","matInput","","appFcCs","","readonly","",1,"overflow-class",3,"formControl","currency","showSuffix"],["class","form-field-class","formControlName","taxId",3,"list","hideMatLabel","placeholder",4,"ngIf","ngIfElse"],["taxNameNonEdit",""],["taxPerNonEdit",""],["type","text","matInput","","formControlName","taxValue","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],["formControlName","taxId",1,"form-field-class",3,"list","hideMatLabel","placeholder"],["type","number","min","0","matInput","","placeholder","Enter %","formControlName","taxPercentage"],["matTooltip","Add Tax","mat-icon-button","",1,"more-btn",2,"width","40px",3,"click"],["matTooltip","Delete Tax","mat-icon-button","",1,"more-btn",2,"width","40px",3,"click"],[1,"col-2","p-0","d-flex","align-items-center"],[1,"header",2,"min-width","5rem"],["class","revenue-item ml-2",4,"ngIf"],["class","d-flex mt-1","cdkOverlayOrigin","",4,"ngIf"],[1,"revenue-item","ml-2"],["type","text","matInput","","formControlName","totalRevenue","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],["cdkOverlayOrigin","",1,"d-flex","mt-1"],["overlayTrigger","cdkOverlayOrigin","revSummaryContainer",""],[1,"ml-2","service-icon-btn",2,"cursor","pointer",3,"click"],[1,"header",2,"min-width","4rem"],["type","text","matInput","","formControlName","totalCost","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],["overlayTrigger","cdkOverlayOrigin","headerCostContainer",""],["type","text","matInput","","formControlName","totalGM","appFcCs","","readonly","",1,"overflow-class",3,"currency","showSuffix"],[1,"row","mt-3"],["class","d-flex","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"d-flex",2,"cursor","pointer",3,"click"],[1,"ml-2","service-icon-btn"],[1,"ml-1","add-btn"],[1,"card",2,"min-width","25vw"],[1,"card-body","p-2"],[1,"row","pt-2","pb-1"],[1,"col-10","d-flex","align-items-center",2,"font-weight","500","color","#45546E"],["mat-icon-button","",3,"click"],[2,"font-size","15px","color","#1C1B1F"],[1,"row","col-12","section-label"],[1,"row","col-12"],["appearance","outline",2,"width","100%","font-size","13px"],["matInput","","placeholder","Enter here",3,"formControl"],[1,"row","col-12","pt-1","pb-2","pl-2"],["mat-raised-button","",1,"cancel-btn","ml-2","mt-2",3,"click"],["mat-raised-button","",1,"ml-2","mt-2",3,"disabled","ngClass","click"],[1,"card",2,"min-width","18vw"],[1,"col-7","header-label"],["class","col-3 pr-0 pl-0 value-label",4,"ngIf"],["class","col pl-0 value-label",4,"ngIf"],[1,"row","pt-2",2,"border-top","1px solid #dadce2"],[1,"col-3","pr-0","pl-0","value-label"],["type","text","matInput","","formControlName","manpowerCost","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],[1,"col","pl-0","value-label"],["type","text","matInput","","formControlName","nonManpowerCost","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","licenseCost","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","totalCost","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","manpowerRevenue","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","nonManpowerRevenue","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","licenseRevenue","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],["type","text","matInput","","formControlName","totalRevenue","appFcCs","","readonly","",1,"overflow-class",3,"matTooltip","currency","showSuffix"],[1,"card",2,"min-width","22vw"],[1,"card-body","p-1"],["mat-raised-button","",1,"ml-2","mt-2","create-btn",3,"click"],[2,"color","#cf0001"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/no_data_found.png","height","150","width","200",1,"mt-5","mb-3"],[1,"d-flex","flex-column","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-weight","600","color","#191729"],[2,"font-size","13px","color","#5f5f5f","font-weight","500"],[1,"d-flex","justify-content-center","slign-items-center","slide-from-down"],["mat-raised-button","",1,"create-btn","mt-2",3,"click"],["class","submit-for-approval-dialog","style","min-height: 40vh",4,"ngIf"],["class","mini-loader",4,"ngIf"],[1,"submit-for-approval-dialog",2,"min-height","40vh"],["mat-dialog-title","",1,"row","header-submit-pop"],[1,"heading"],["mat-icon-button","",1,"d-flex","justify-content-center","align-items-center",3,"click"],[1,"qb-details"],[1,"col-5","d-flex","align-items-center"],[1,"qb-title"],[1,"qb-value"],[1,"qb-value","col-5","d-flex","align-items-center"],[1,"approvers-profiles","ml-3","mt-3"],[1,"top-header","mb-2"],[1,"d-flex","align-items-center","justify-content-start","approvers-container"],[3,"matTooltip",4,"ngIf"],["class","approver-item",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center","align-items-center","mt-3","pt-3"],["mat-button","",1,"dialog-cancel-btn",3,"click"],["mat-raised-button","","mat-button","",1,"dialog-submit-btn",3,"disabled","matTooltip","click"],[3,"matTooltip"],[1,"approver-item"],["imgWidth","33px","imgHeight","33px",3,"id","matTooltip"],[1,"mini-loader"],["diameter","26",1,"spinner","d-flex","justify-content-center"],["matTooltip","Close","mat-icon-button","",1,"d-flex","justify-content-center","align-items-center",3,"click"],["class","top-header mb-2",4,"ngIf"],["class","d-flex align-items-center mb-3 submitter-details",4,"ngIf"],[1,"approvers-profiles","mb-3","mt-3"],[1,"top-header","mb-2",2,"font-weight","600 !important"],[1,"d-flex","align-items-center","justify-content-between"],[1,"uploader-details-desc","d-flex","align-items-center","justify-content-center",3,"ngStyle"],[1,"d-flex","align-items-center","justify-content-start"],[1,"top-header",2,"font-weight","600 !important"],["rows","5","cols","40",3,"ngModel","readonly","ngModelChange"],["class","d-flex justify-content-center align-items-center mt-3 pt-3",4,"ngIf"],[1,"d-flex","align-items-center","mb-3","submitter-details"],["imgWidth","28px","imgHeight","28px",3,"id"],[2,"font-size","12px"],[1,"reviwer-name"],["type","name",3,"oid"],["mat-raised-button","","mat-button","",1,"dialog-submit-btn",3,"click"]],template:function(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",0),k["\u0275\u0275elementStart"](1,"form",1),k["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),k["\u0275\u0275elementStart"](2,"div",2),k["\u0275\u0275elementStart"](3,"div",3),k["\u0275\u0275elementStart"](4,"div",4),k["\u0275\u0275elementStart"](5,"div",5),k["\u0275\u0275elementStart"](6,"button",6),k["\u0275\u0275listener"]("click",(function(){return t.navigateToLandingPage()})),k["\u0275\u0275elementStart"](7,"mat-icon",7),k["\u0275\u0275text"](8,"chevron_left"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275text"](9," Back "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",8,9),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const n=k["\u0275\u0275reference"](11);return t.openQuoteOverlay(n)})),k["\u0275\u0275elementStart"](13,"div",10),k["\u0275\u0275elementStart"](14,"div",11),k["\u0275\u0275text"](15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](16,re,2,6,"button",12),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](17,"div",13),k["\u0275\u0275template"](18,le,1,0,"span",14),k["\u0275\u0275template"](19,ce,2,0,"span",15),k["\u0275\u0275template"](20,ue,2,0,"span",15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](21,"div",16),k["\u0275\u0275elementStart"](22,"div",17),k["\u0275\u0275elementStart"](23,"div",18),k["\u0275\u0275elementStart"](24,"div",19),k["\u0275\u0275text"](25,"Delivery Start Date"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](26,"mat-form-field",20),k["\u0275\u0275template"](27,de,2,0,"mat-label",21),k["\u0275\u0275element"](28,"input",22),k["\u0275\u0275template"](29,pe,3,1,"mat-datepicker-toggle",23),k["\u0275\u0275element"](30,"mat-datepicker",null,24),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](32,"div",18),k["\u0275\u0275elementStart"](33,"div",19),k["\u0275\u0275text"](34,"Delivery End Date"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](35,"mat-form-field",20),k["\u0275\u0275template"](36,me,2,0,"mat-label",21),k["\u0275\u0275element"](37,"input",25),k["\u0275\u0275template"](38,he,3,1,"mat-datepicker-toggle",23),k["\u0275\u0275element"](39,"mat-datepicker",null,26),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](41,"div",18),k["\u0275\u0275elementStart"](42,"div",19),k["\u0275\u0275text"](43,"Currency"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](44,"div",27),k["\u0275\u0275text"](45),k["\u0275\u0275template"](46,ge,2,1,"mat-icon",28),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](47,"mat-menu",29,30),k["\u0275\u0275elementStart"](49,"div",31),k["\u0275\u0275template"](50,ve,2,1,"button",32),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](51,Se,21,12,"div",33),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](52,"div",34),k["\u0275\u0275elementStart"](53,"div",3),k["\u0275\u0275template"](54,pi,25,16,"ng-container",21),k["\u0275\u0275template"](55,mi,2,0,"ng-template",null,35,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](57,hi,1,1,"ng-template",null,36,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](59,gi,13,0,"ng-container",21),k["\u0275\u0275elementStart"](60,"ngx-spinner",37),k["\u0275\u0275elementStart"](61,"p",38),k["\u0275\u0275text"](62," Loading Data... "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](63,bi,2,2,"ng-template",null,39,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](65,Ii,2,2,"ng-template",null,40,k["\u0275\u0275templateRefExtractor"])}if(2&e){const e=k["\u0275\u0275reference"](31),n=k["\u0275\u0275reference"](40);let i=null,o=null,a=null,s=null;k["\u0275\u0275property"]("@.disabled",!0),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formGroup",t.quoteForm),k["\u0275\u0275advance"](9),k["\u0275\u0275property"]("ngClass",null!=t.servicesFormArr&&t.servicesFormArr.length?"col-3":"col-7"),k["\u0275\u0275advance"](5),k["\u0275\u0275textInterpolate"](t.quoteForm.get("quoteName").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",null==t.wholeQuoteData?null:t.wholeQuoteData.approval_workflow_header_id),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf","CHANGE_REQUEST"==(null==t.quoteForm||null==(i=t.quoteForm.get("quoteType"))?null:i.value)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","CHANGE_REQUEST"==(null==t.quoteForm||null==(o=t.quoteForm.get("quoteType"))?null:o.value)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",(null==t.quoteForm||null==(a=t.quoteForm.get("activeQuote"))?null:a.value)||(null==t.quoteForm||null==(a=t.quoteForm.get("activeChangeRequest"))?null:a.value)),k["\u0275\u0275advance"](7),k["\u0275\u0275property"]("ngIf",!t.quoteForm.get("deliveryStartDate").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("max",t.quoteForm.get("deliveryEndDate").value)("min","CHANGE_REQUEST"==(null==t.quoteForm||null==(s=t.quoteForm.get("quoteType"))?null:s.value)?t.quoteForm.get("deliveryStartDate").value:null)("matDatepicker",e),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.isEditMode&&t.has_date_change_access),k["\u0275\u0275advance"](7),k["\u0275\u0275property"]("ngIf",!t.quoteForm.get("deliveryEndDate").value),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("min",t.quoteForm.get("deliveryStartDate").value)("matDatepicker",n),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.isEditMode&&t.has_date_change_access),k["\u0275\u0275advance"](7),k["\u0275\u0275textInterpolate1"](" ",t.quoteForm.get("quoteCurrency").value," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.isEditMode&&t.quote_currency_change&&!(null!=t.wholeQuoteData&&t.wholeQuoteData.has_parent_opportunity)&&"CHANGE_REQUEST"!=t.quoteForm.get("quoteType").value),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngForOf",t.currencyList),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",null==t.servicesFormArr?null:t.servicesFormArr.length),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",null==t.servicesFormArr?null:t.servicesFormArr.length),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",0==(null==t.servicesFormArr?null:t.servicesFormArr.length)&&!t.isDataLoading),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("fullScreen",!1)}},directives:[u.J,u.w,u.n,o.a,s.a,a.a,g.b,i.NgClass,i.NgIf,r.c,c.b,u.e,d.g,u.v,u.l,d.f,m.g,i.NgForOf,R.a,i.NgStyle,r.g,d.i,r.i,d.j,m.f,m.d,z.a,W.a,H.c,h.e,u.h,g.a,u.o,h.a,h.b,i.NgTemplateOutlet,X.a,u.F,u.A,u.k,p.i,Y.a,u.y,K.a],pipes:[J.a],styles:[".quote-creation-styles[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{padding:2px;height:4vh;font-size:12px;display:flex;align-items:center;color:#8b95a5}.quote-creation-styles[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:18px;padding-top:2px}.quote-creation-styles[_ngcontent-%COMP%]   .quote-text[_ngcontent-%COMP%]{font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:pointer}.quote-creation-styles[_ngcontent-%COMP%]   .quote-text[_ngcontent-%COMP%]   .quote-dot[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none;height:.65rem;width:.65rem;border-radius:50%;background:#ee4961}.quote-creation-styles[_ngcontent-%COMP%]   .quote-text[_ngcontent-%COMP%]   .quote-type[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding-left:1%;padding-top:2px}.quote-creation-styles[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]{padding:3px;height:25px;width:80px;font-size:11px;max-height:70px;font-weight:700!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important;display:flex;align-items:center;justify-content:center}@keyframes shakeDetailQuote{0%,to{transform:translateX(0)}10%{transform:translateX(-5px)}20%{transform:translateX(5px)}30%{transform:translateX(-5px)}40%{transform:translateX(5px)}50%{transform:translateX(-5px)}60%{transform:translateX(5px)}70%{transform:translateX(-5px)}80%{transform:translateX(5px)}90%{transform:translateX(-5px)}}.quote-creation-styles[_ngcontent-%COMP%]   .shakeDetailQuote[_ngcontent-%COMP%]{animation:shakeDetailQuote .7s ease-in-out}@keyframes shine{0%{opacity:.5;color:#ef4a61}50%{opacity:1;color:#ff6f61}to{opacity:.5;color:#ef4a61}}.quote-creation-styles[_ngcontent-%COMP%]   .shine-icon[_ngcontent-%COMP%]{animation:shine 1s ease-in-out infinite}.quote-creation-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quote-creation-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{width:100%;max-height:15%!important;font-size:12px}.quote-creation-styles[_ngcontent-%COMP%]   .date-picker-class[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.quote-creation-styles[_ngcontent-%COMP%]   .date-picker-class[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding-bottom:0!important;margin-top:-7px}.quote-creation-styles[_ngcontent-%COMP%]   .date-picker-class[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.quote-creation-styles[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%], .quote-creation-styles[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;font-size:12px!important;line-height:30px;border-radius:4px}.quote-creation-styles[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:30px;border-radius:4px;height:30px!important}.quote-creation-styles[_ngcontent-%COMP%]   .tag-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:30px;border-radius:4px;border:1px solid #45546e;height:30px;margin-top:9px}.quote-creation-styles[_ngcontent-%COMP%]   .tag-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:19px}.quote-creation-styles[_ngcontent-%COMP%]   .other-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:30px;border-radius:4px;min-width:unset;width:33px;padding:0}.quote-creation-styles[_ngcontent-%COMP%]   .service-icon-btn[_ngcontent-%COMP%]{font-size:16px;color:#908b8b}.quote-creation-styles[_ngcontent-%COMP%]   .revenue-item[_ngcontent-%COMP%]{font-weight:600;font-size:13px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:-webkit-fill-available}.quote-creation-styles[_ngcontent-%COMP%]   .revenue-value[_ngcontent-%COMP%]{font-weight:500;color:#45546e;font-size:13px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quote-creation-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:90%;font-size:12px}.quote-creation-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%;font-size:12px!important}.quote-creation-styles[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]{color:#5f6c81;font-size:12px;font-weight:500}.quote-creation-styles[_ngcontent-%COMP%]   .more-btn[_ngcontent-%COMP%]{width:23px}.quote-creation-styles[_ngcontent-%COMP%]   .more-btn[_ngcontent-%COMP%]   .more-btn-icon[_ngcontent-%COMP%]{color:#6e7b8f;font-size:15px}.quote-creation-styles[_ngcontent-%COMP%]   .dt-class[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#45546e}.quote-creation-styles[_ngcontent-%COMP%]   .label-class[_ngcontent-%COMP%]{font-size:11px;color:#5f6c81;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:10px}.quote-creation-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%], .quote-creation-styles[_ngcontent-%COMP%]   .cdk-drop-list-dragging[_ngcontent-%COMP%]   .drag-class[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.quote-creation-styles[_ngcontent-%COMP%]   .display-class[_ngcontent-%COMP%]{overflow:hidden;color:#5f6c81;text-overflow:ellipsis;white-space:nowrap;font-size:12px;font-weight:600;padding:8px 0}.quote-creation-styles[_ngcontent-%COMP%]   .suffix-class[_ngcontent-%COMP%]{color:#5f6c81;font-size:11px;padding-left:5px}.quote-creation-styles[_ngcontent-%COMP%]   .overflow-class[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis}.quote-creation-styles[_ngcontent-%COMP%]   .service-name[_ngcontent-%COMP%]{font-weight:500;font-size:13px;max-width:75vw;white-space:nowrap}.quote-creation-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.quote-creation-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.quote-creation-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.quote-creation-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-webkit-inner-spin-button, .quote-creation-styles[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.quote-creation-styles[_ngcontent-%COMP%]     .mat-badge-content{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff!important}.section-label[_ngcontent-%COMP%]{font-size:12px;color:#5f6c81;line-height:8px}.create-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff}.create-btn[_ngcontent-%COMP%], .create-btn-disabled[_ngcontent-%COMP%]{font-size:12px!important;line-height:30px;border-radius:4px}.cancel-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:35px;border-radius:4px}.quote-transparent-overlay-backdrop[_ngcontent-%COMP%]    .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}.header-label[_ngcontent-%COMP%]{color:#45546e;font-size:11px;font-weight:400}.value-label[_ngcontent-%COMP%]{color:#45546e;text-align:right;font-size:11px;font-weight:600}.value-label[_ngcontent-%COMP%], .value-label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.menu-header-class[_ngcontent-%COMP%]{display:flex;max-height:5vh;align-items:center}.menu-item-class[_ngcontent-%COMP%]{font-size:11px;color:#45546e;font-weight:400}.custom-mat-menu[_ngcontent-%COMP%]{height:15rem!important;overflow:hidden!important}.menu-scroll[_ngcontent-%COMP%]{max-height:15rem;overflow-y:auto}.submit-for-approval-dialog[_ngcontent-%COMP%]{overflow-y:auto;overflow-x:hidden;padding:1rem;min-height:40vh}.dialog-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700;line-height:28px}.dialog-desc[_ngcontent-%COMP%], .dialog-title[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;text-align:center}.dialog-desc[_ngcontent-%COMP%]{font-size:14px;font-weight:400;line-height:20px;color:#475467}.dialog-submit-btn[_ngcontent-%COMP%]{background:#ef4a61!important;border-radius:4px;color:#fff!important;border:1px solid #ef4a61!important}.dialog-cancel-btn[_ngcontent-%COMP%], .dialog-submit-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:5rem;font-size:14px;font-weight:500!important}.dialog-cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:4px;font-family:Plus Jakarta Sans;margin-left:auto;margin-right:1.5rem}.heading[_ngcontent-%COMP%]{justify-content:start;font-weight:700;font-size:18px;padding-top:.2rem;font-family:Plus Jakarta Sans!important}.heading[_ngcontent-%COMP%], .heading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;align-items:center;color:#272a47}.heading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{justify-content:center;font-size:22px}.top-header[_ngcontent-%COMP%]{font-size:13px;font-weight:600;line-height:16px;letter-spacing:.02em;text-align:left;color:#45546e;font-family:Plus Jakarta Sans}.approvers-profiles[_ngcontent-%COMP%]{align-items:center;justify-content:start;gap:5%}.approvers-profiles[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{border:1px solid #dadce2;font-family:Plus Jakarta Sans;color:#45546e;font-size:13px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;margin-top:1%;height:5rem;resize:none;padding:1rem;border-radius:3px;width:95%}.approvers-profiles[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border:2px solid #ef4a61!important;outline:none}.approvers-profiles[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;color:#45546e;display:flex;align-items:center;justify-content:center}.approvers-profiles[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]{font-size:13px;font-weight:500;line-height:16px;letter-spacing:.02em;text-align:left;color:#45546e;font-family:Plus Jakarta Sans}.approvers-profiles[_ngcontent-%COMP%]   .uploader-details-desc[_ngcontent-%COMP%]{margin-top:3%;font-size:12px;font-weight:600;line-height:16px;letter-spacing:.02em;text-align:left;display:flex;align-items:center;border-radius:4px;height:2rem;padding:2%}.approvers-profiles[_ngcontent-%COMP%]   .approvers-container[_ngcontent-%COMP%]{position:relative}.approvers-profiles[_ngcontent-%COMP%]   .approver-item[_ngcontent-%COMP%]{margin-left:-10px;z-index:auto}.approvers-profiles[_ngcontent-%COMP%]   .approver-item[_ngcontent-%COMP%]:first-child{margin-left:0}.submitter-details[_ngcontent-%COMP%]{font-size:13px;font-weight:500;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;font-family:Plus Jakarta Sans;white-space:nowrap}.qb-details[_ngcontent-%COMP%]{padding:0 1rem}.qb-title[_ngcontent-%COMP%]{font-size:13px;font-weight:700;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.reviewer-name[_ngcontent-%COMP%]{font-size:12px;margin-top:-2%;font-weight:700}.qb-value[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mini-loader[_ngcontent-%COMP%]{min-height:40vh;display:flex;align-items:center;justify-content:center}.header-submit-pop[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;color:#5f5f5f;font-size:11px!important;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.uniq-icons[_ngcontent-%COMP%]{display:flex;justify-content:center;font-size:18px;color:#5f6c81;cursor:default}"],data:{animation:[Object(O.o)("listAnimation",[Object(O.n)("* => *",[Object(O.i)(":enter",[Object(O.m)({opacity:0,transform:"translateY(-10px)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-out",Object(O.m)({opacity:1,transform:"translateY(0)"}))])],{optional:!0}),Object(O.i)(":leave",[Object(O.m)({opacity:1,transform:"translateY(0)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-in",Object(O.m)({opacity:0,transform:"translateY(-10px)"}))])],{optional:!0})])]),Object(O.o)("listPositionAnimation",[Object(O.n)("* => *",[Object(O.i)(":enter",[Object(O.m)({opacity:0,transform:"translateY(-10px)"}),Object(O.k)(100,[Object(O.e)("80ms ease-out",Object(O.m)({opacity:1,transform:"translateY(0)"}))])],{optional:!0}),Object(O.i)(":leave",[Object(O.m)({opacity:1,transform:"translateY(0)"}),Object(O.k)(100,[Object(O.e)("80ms ease-in",Object(O.m)({opacity:0,transform:"translateY(-10px)"}))])],{optional:!0})])]),Object(O.o)("editModeTransition",[Object(O.n)("* => *",[Object(O.i)(":enter",[Object(O.m)({opacity:0,transform:"translateY(-10px)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-out",Object(O.m)({opacity:1,transform:"translateY(0)"}))])],{optional:!0}),Object(O.i)(":leave",[Object(O.m)({opacity:1,transform:"translateY(0)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-in",Object(O.m)({opacity:0,transform:"translateY(-10px)"}))])],{optional:!0})])]),Object(O.o)("buttonFadeInOut",[Object(O.n)(":enter",[Object(O.m)({opacity:0,transform:"translateY(-10px)"}),Object(O.e)("200ms ease-out",Object(O.m)({opacity:1,transform:"translateY(0)"}))]),Object(O.n)(":leave",[Object(O.e)("200ms ease-in",Object(O.m)({opacity:0,transform:"translateY(-10px)"}))])]),Object(O.o)("buttonSlideFade",[Object(O.n)("* => *",[Object(O.i)(":enter",[Object(O.m)({opacity:0,transform:"translateX(-20px)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-out",Object(O.m)({opacity:1,transform:"translateX(0)"}))])],{optional:!0}),Object(O.i)(":leave",[Object(O.m)({opacity:1,transform:"translateX(0)"}),Object(O.k)(100,[Object(O.e)("0.2s ease-in",Object(O.m)({opacity:0,transform:"translateX(-20px)"}))])],{optional:!0})])])]}}),e})();var Mi=n("IzEk"),ki=n("XXEo"),Fi=n("wiVK"),Ti=n("H44p");function Ai(e,t){1&e&&k["\u0275\u0275element"](0,"span",27)}const Ri=function(e,t){return{flagged:e,"change-request-flagged":t}};function Qi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"mat-icon",28),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]();return t.flagClick(t.quote.quote_type)})),k["\u0275\u0275text"](1,"flag"),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("matTooltip",e.getTooltipText(e.quote))("fontSet",e.getFontSet(e.quote))("ngClass",k["\u0275\u0275pureFunction2"](3,Ri,e.quote.flag&&"CHANGE_REQUEST"!==e.quote.quote_type,"CHANGE_REQUEST"===e.quote.quote_type&&e.quote.change_request_flag))}}function Li(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"p",9),k["\u0275\u0275text"](1,"Quote Value"),k["\u0275\u0275elementEnd"]())}function ji(e,t){if(1&e&&k["\u0275\u0275element"](0,"app-currency",29),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("currencyList",null==e.quote?null:e.quote.quote_revenue_value)("showActualAmount",!1)}}function Ni(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"button",30),k["\u0275\u0275elementStart"](1,"mat-icon",31),k["\u0275\u0275text"](2,"content_copy"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](24);k["\u0275\u0275property"]("matMenuTriggerFor",e)}}function Vi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",21),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().copyQuote("TRADITIONAL")})),k["\u0275\u0275elementStart"](1,"span"),k["\u0275\u0275text"](2,"Copy as Normal Quote"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Ui(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",32),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2);return t.copyQuote("CHANGE_REQUEST"!==t.quote.quote_type?"TRADITIONAL":"CHANGE_REQUEST")})),k["\u0275\u0275elementStart"](1,"mat-icon",31),k["\u0275\u0275text"](2,"content_copy"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275propertyInterpolate"]("matTooltip","CHANGE_REQUEST"!==e.quote.quote_type?"Copy Quote":"Copy Change Request")}}function $i(e,t){if(1&e&&k["\u0275\u0275template"](0,Ui,3,1,"button",23),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.showDeleted&&e.isCUDEnabled)}}function Gi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",32),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().deleteQuote()})),k["\u0275\u0275elementStart"](1,"mat-icon",25),k["\u0275\u0275text"](2,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275propertyInterpolate"]("matTooltip","CHANGE_REQUEST"!=e.quote.quote_type?"Delete Quote":"Delete Change Request")}}const Bi=function(e){return{shake:e}};function zi(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",33),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().triggerWorkflowQuoteApproval()})),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngStyle",e.getStatusCss(null==e.quote?null:e.quote.quote_status))("ngClass",k["\u0275\u0275pureFunction1"](4,Bi,e.quote.isApprover&&1===e.quote.quote_status))("matTooltip",e.quote.isApprover&&1===e.quote.quote_status?"Click to Approve/Reject Quote Activation":e.quote.isApprover&&2===e.quote.quote_status?"Click to check the details of Approval":e.quote.isApprover&&3===e.quote.quote_status?"Click to check the details of Rejection":e.quote.isApprover||2!==e.quote.quote_status?e.quote.isApprover||3!==e.quote.quote_status?"Click to check the status of the approval":"Click to check Rejection Details":"Click to check Approval Details"),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.quote.status_name," ")}}const Wi=function(e,t){return{"highlight-class":e,"change-request-highlight-class":t}};let Hi=(()=>{class e{constructor(){this.showDeleted=!1,this.isCUDEnabled=!0,this.isCRApplicable=!1,this.showApprovalButtons=!1,this.showQuoteValue=!0,this.flagged=new k.EventEmitter,this.changeRequestFlagged=new k.EventEmitter,this.edit=new k.EventEmitter,this.delete=new k.EventEmitter,this.activityLog=new k.EventEmitter,this.copy=new k.EventEmitter,this.copyAsCR=new k.EventEmitter,this.approveQuote=new k.EventEmitter,this.rejectQuote=new k.EventEmitter,this.openApproverPopUp=new k.EventEmitter,this.deleteQuote=()=>{this.delete.emit(!0)},this.openQuoteActivityLog=()=>{this.activityLog.emit(!0)},this.copyQuote=e=>{"CHANGE_REQUEST"===e?this.copyAsCR.emit(!0):this.copy.emit(!0)},this.triggerWorkflowQuoteApproval=e=>{this.openApproverPopUp.emit("clicked")}}ngOnInit(){}flagClick(e){"CHANGE_REQUEST"!==e?this.flagged.emit("clicked"):this.changeRequestFlagged.emit("clicked")}editQuote(){this.edit.emit(!0)}getStatusCss(e){switch(e){case 4:return{background:"#E8E9EE",color:"#45546E"};case 1:return{background:"#FFF3E8",color:"#FA8C16"};case 2:return{background:"#EEF9E8",color:"#52C41A"};case 3:return{background:"#FFEBEC",color:"#FF3A46"};default:return{}}}getTooltipText(e){return e.change_request_flag&&"CHANGE_REQUEST"===e.quote_type?"Change Request (Active)":"CHANGE_REQUEST"===e.quote_type?"Change Request (In Active)":e.flag?"Active Quote":"In Active Quote"}getFontSet(e){return"CHANGE_REQUEST"===e.quote_type&&e.change_request_flag||e.flag?null:"material-symbols-outlined"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["quote-card"]],inputs:{quote:"quote",showDeleted:"showDeleted",isCUDEnabled:"isCUDEnabled",isCRApplicable:"isCRApplicable",showApprovalButtons:"showApprovalButtons",quoteStatus:"quoteStatus",showQuoteValue:"showQuoteValue"},outputs:{flagged:"flagged",changeRequestFlagged:"changeRequestFlagged",edit:"edit",delete:"delete",activityLog:"activityLog",copy:"copy",copyAsCR:"copyAsCR",approveQuote:"approveQuote",rejectQuote:"rejectQuote",openApproverPopUp:"openApproverPopUp"},decls:36,vars:20,consts:[[1,"card",3,"ngClass"],[1,"top-section"],[3,"click"],[1,"quote-title",3,"matTooltip"],[1,"version"],["class","status-dot",4,"ngIf"],[2,"width","10rem"],[3,"matTooltip","fontSet","ngClass","click",4,"ngIf"],[1,"mid-section"],[1,"quote-title"],[1,"d-flex","align-items-center"],["height","28px","width","28px",3,"src"],[1,"person-name","pl-2"],[1,"last-section"],[1,"quote-value"],["class","quote-title",4,"ngIf"],["type","small",3,"currencyList","showActualAmount",4,"ngIf"],[1,"action-icons","d-flex","align-items-center"],["class","widget-btn","matTooltip","Copy Quote","mat-icon-button","",3,"matMenuTriggerFor",4,"ngIf","ngIfElse"],["copyMenu","matMenu"],["mat-menu-item","","class","copy-header-class",3,"click",4,"ngIf"],["mat-menu-item","",1,"copy-header-class",3,"click"],["normalCopyMenu",""],["class","widget-btn","mat-icon-button","",3,"matTooltip","click",4,"ngIf"],["matTooltip","Activity Log","mat-icon-button","",1,"widget-btn",3,"click"],["fontSet","material-symbols-outlined",1,"widget-btn-icon"],["class","status-btn","mat-button","",3,"ngStyle","ngClass","matTooltip","click",4,"ngIf"],[1,"status-dot"],[3,"matTooltip","fontSet","ngClass","click"],["type","small",3,"currencyList","showActualAmount"],["matTooltip","Copy Quote","mat-icon-button","",1,"widget-btn",3,"matMenuTriggerFor"],[1,"widget-btn-icon"],["mat-icon-button","",1,"widget-btn",3,"matTooltip","click"],["mat-button","",1,"status-btn",3,"ngStyle","ngClass","matTooltip","click"]],template:function(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",0),k["\u0275\u0275elementStart"](1,"div",1),k["\u0275\u0275elementStart"](2,"span",2),k["\u0275\u0275listener"]("click",(function(){return t.editQuote()})),k["\u0275\u0275elementStart"](3,"p",3),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"p",4),k["\u0275\u0275template"](6,Ai,1,0,"span",5),k["\u0275\u0275elementStart"](7,"span",6),k["\u0275\u0275text"](8),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](9,Qi,2,6,"mat-icon",7),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",8),k["\u0275\u0275elementStart"](11,"p",9),k["\u0275\u0275text"](12,"LAST MODIFIED BY"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](13,"div",10),k["\u0275\u0275element"](14,"img",11),k["\u0275\u0275elementStart"](15,"span",12),k["\u0275\u0275text"](16),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](17,"div",13),k["\u0275\u0275elementStart"](18,"div",14),k["\u0275\u0275template"](19,Li,2,0,"p",15),k["\u0275\u0275template"](20,ji,1,2,"app-currency",16),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](21,"div",17),k["\u0275\u0275template"](22,Ni,3,1,"button",18),k["\u0275\u0275elementStart"](23,"mat-menu",null,19),k["\u0275\u0275template"](25,Vi,3,0,"button",20),k["\u0275\u0275elementStart"](26,"button",21),k["\u0275\u0275listener"]("click",(function(){return t.copyQuote("CHANGE_REQUEST")})),k["\u0275\u0275elementStart"](27,"span"),k["\u0275\u0275text"](28,"Copy as Change Request"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](29,$i,1,1,"ng-template",null,22,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](31,Gi,3,1,"button",23),k["\u0275\u0275elementStart"](32,"button",24),k["\u0275\u0275listener"]("click",(function(){return t.openQuoteActivityLog()})),k["\u0275\u0275elementStart"](33,"mat-icon",25),k["\u0275\u0275text"](34,"history"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](35,zi,2,6,"button",26),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275reference"](30);k["\u0275\u0275property"]("ngClass",k["\u0275\u0275pureFunction2"](17,Wi,1==t.quote.flag,"CHANGE_REQUEST"==t.quote.quote_type&&t.quote.change_request_flag)),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("matTooltip",t.quote.quote_name),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate2"](" QT/",t.quote.quote_header_id," - ",t.quote.quote_name," "),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf","CHANGE_REQUEST"==t.quote.quote_type),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate2"](" ","CHANGE_REQUEST"==t.quote.quote_type?"Change Request":""," ",t.quote.flag||t.quote.change_request_flag?" (Active)":""," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!t.showDeleted&&t.isCUDEnabled),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("src",t.quote.last_modified_by_profile_url?t.quote.last_modified_by_profile_url:"https://assets.kebs.app/images/User.png",k["\u0275\u0275sanitizeUrl"]),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",null==t.quote?null:t.quote.last_modified_by_name," "),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",t.showQuoteValue),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.showQuoteValue),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",!t.showDeleted&&t.isCRApplicable&&"CHANGE_REQUEST"!==t.quote.quote_type)("ngIfElse",e),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",t.isCUDEnabled),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("ngIf",!t.showDeleted&&t.isCUDEnabled),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngIf",(null==t.quote?null:t.quote.approval_workflow_header_id)&&2!==t.quote.quote_status)}},directives:[i.NgClass,s.a,i.NgIf,m.g,m.d,o.a,a.a,Ti.a,m.f,i.NgStyle],styles:[".card[_ngcontent-%COMP%]{height:12.5rem;overflow:hidden;border-radius:4px;border:1px solid #e8e9ee;background:#fff;padding:24px}.card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0!important}.card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{cursor:pointer!important}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:16px;cursor:pointer}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]   .quote-title[_ngcontent-%COMP%]{font-size:14px;font-weight:700;color:#45546e;text-transform:none;max-width:16vw;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%]{font-size:12px;font-weight:400;display:flex;align-items:center;gap:4%}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none;height:.65rem;width:.65rem;border-radius:50%;background:#ee4961}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]   .flagged[_ngcontent-%COMP%]{color:#ee4961}.card[_ngcontent-%COMP%]   .top-section[_ngcontent-%COMP%]   .change-request-flagged[_ngcontent-%COMP%]{color:#5bab3e}.card[_ngcontent-%COMP%]   .quote-title[_ngcontent-%COMP%]{font-size:11px;color:#8b95a5;text-transform:uppercase}.card[_ngcontent-%COMP%]   .mid-section[_ngcontent-%COMP%]{margin-bottom:16px}.card[_ngcontent-%COMP%]   .last-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.card[_ngcontent-%COMP%]   .last-section[_ngcontent-%COMP%]   .quote-value[_ngcontent-%COMP%]{height:max-content}.card[_ngcontent-%COMP%]   .last-section[_ngcontent-%COMP%]   .action-icons[_ngcontent-%COMP%]{align-self:flex-end}.card[_ngcontent-%COMP%]   .last-section[_ngcontent-%COMP%]   .action-icons[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;color:#45546e}.card[_ngcontent-%COMP%]   .widget-btn[_ngcontent-%COMP%]{width:30px}.card[_ngcontent-%COMP%]   .widget-btn[_ngcontent-%COMP%]   .widget-btn-icon[_ngcontent-%COMP%]{color:#45546e;font-size:16px}.card[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]{padding:3px;height:25px;max-width:75px;font-size:11px;max-height:70px;font-weight:700!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important;display:flex;align-items:center;justify-content:center}@keyframes shake{0%,to{transform:translateX(0)}10%{transform:translateX(-5px)}20%{transform:translateX(5px)}30%{transform:translateX(-5px)}40%{transform:translateX(5px)}50%{transform:translateX(-5px)}60%{transform:translateX(5px)}70%{transform:translateX(-5px)}80%{transform:translateX(5px)}90%{transform:translateX(-5px)}}.card[_ngcontent-%COMP%]   .shake[_ngcontent-%COMP%]{animation:shake .7s ease-in-out}.card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important}.card[_ngcontent-%COMP%]   .person-name[_ngcontent-%COMP%]{color:#111434;font-size:13px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.menu-header-class[_ngcontent-%COMP%]{display:flex;max-height:5vh;align-items:center}.copy-header-class[_ngcontent-%COMP%]{color:var(--Black-100,#111434);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;display:flex;max-height:4.5vh;align-items:center}.menu-item-class[_ngcontent-%COMP%]{font-size:11px;color:#45546e;font-weight:400}.highlight-class[_ngcontent-%COMP%]{background:#fde4e2}.change-request-highlight-class[_ngcontent-%COMP%]{background:rgba(175,237,160,.3803921568627451)}"]}),e})();var Xi=n("QibW"),Yi=n("dlKe"),Ki=n("7EHt");let Ji=(()=>{class e{transform(e){return e?e.sort((e,t)=>{if(1===e.flag&&1!==t.flag)return-1;if(1!==e.flag&&1===t.flag)return 1;if(e.flag===t.flag){if(1===e.change_request_flag&&1!==t.change_request_flag)return-1;if(1!==e.change_request_flag&&1===t.change_request_flag)return 1}return 0}):[]}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=k["\u0275\u0275definePipe"]({name:"sortQuotes",type:e,pure:!0}),e})();const Zi=["copyQuoteDialog"],eo=["importQuoteDialog"],to=["submitForApproval"],no=["approveOrReject"],io=function(e){return{"shine-icon":e}};function oo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",25),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]();return t.openApproverDialog(t.ifUserHasApprovals)})),k["\u0275\u0275elementStart"](1,"mat-icon",26),k["\u0275\u0275text"](2," gavel "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",e.ifUserHasApprovals?"You have Quote Activation Approvals":"")("ngClass",k["\u0275\u0275pureFunction1"](2,io,e.ifUserHasApprovals))}}function ao(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",27),k["\u0275\u0275elementStart"](1,"button",28),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](),n=k["\u0275\u0275reference"](30);return t.openCreateDialog(n,"TRADITIONAL")})),k["\u0275\u0275text"](2," Create New Quote "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"button",29),k["\u0275\u0275elementStart"](4,"mat-icon"),k["\u0275\u0275text"](5,"keyboard_arrow_down"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){k["\u0275\u0275nextContext"]();const e=k["\u0275\u0275reference"](40);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("matMenuTriggerFor",e)}}function so(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",30),k["\u0275\u0275elementStart"](1,"button",31),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](),n=k["\u0275\u0275reference"](30);return t.openCreateDialog(n,"CHANGE_REQUEST")})),k["\u0275\u0275text"](2," Change Request "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function ro(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"quote-card",35),k["\u0275\u0275listener"]("delete",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).deleteQuote(n)}))("edit",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).editQuote(n)}))("copy",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).copyQuote(n)}))("copyAsCR",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).copyQuote(n,!1,"CHANGE_REQUEST")}))("flagged",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).updateActiveQuote(n)}))("changeRequestFlagged",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).updateActiveChangeRequestQuote(n)}))("activityLog",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).openActivityLog(n)}))("openApproverPopUp",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit;return k["\u0275\u0275nextContext"](4).openApproverDialog(n)})),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=k["\u0275\u0275nextContext"](4);k["\u0275\u0275property"]("quote",e)("isCUDEnabled","CHANGE_REQUEST"===e.quote_type?n.enableChangeRequest:n.isCUDEnabled)("isCRApplicable",n.enableChangeRequest)("showDeleted",n.showDeleted)("showApprovalButtons",n.ifUserHasApprovals)("showQuoteValue",n.checkFieldShouldbeRestricted("totalRevenue"))}}function lo(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",33),k["\u0275\u0275template"](2,ro,1,6,"quote-card",34),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).ngIf;k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e)}}function co(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,lo,3,1,"ng-container",32),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().ngIf;k["\u0275\u0275nextContext"]();const t=k["\u0275\u0275reference"](27);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.length)("ngIfElse",t)}}function uo(e,t){if(1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275template"](1,co,2,2,"ng-container",14),k["\u0275\u0275elementContainerEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e._landingPageService.isQuoteLoading)}}function po(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"p",42),k["\u0275\u0275text"](1,"Start creating quote now."),k["\u0275\u0275elementEnd"]())}function mo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",30),k["\u0275\u0275elementStart"](1,"button",31),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](3),n=k["\u0275\u0275reference"](30);return t.openCreateDialog(n,"TRADITIONAL")})),k["\u0275\u0275text"](2," Create New Quote "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"button",43),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](3).openImportQuoteDialog()})),k["\u0275\u0275text"](4," Import A Quote "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function ho(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",37),k["\u0275\u0275element"](1,"img",38),k["\u0275\u0275elementStart"](2,"div",39),k["\u0275\u0275elementStart"](3,"p",40),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,po,2,0,"p",41),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](6,mo,5,0,"div",13),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"]("No ",e.showDeleted?"Deleted":""," Quotes Here"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.showDeleted&&e.isCUDEnabled),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.showDeleted&&e.isCUDEnabled)}}function go(e,t){if(1&e&&k["\u0275\u0275template"](0,ho,7,3,"div",36),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.isQuoteBeingImported)}}function vo(e,t){1&e&&(k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",37),k["\u0275\u0275elementStart"](2,"span"),k["\u0275\u0275text"](3,"Loading...."),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]())}function fo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275elementStart"](1,"p",45),k["\u0275\u0275text"](2,"New Quote!"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"label"),k["\u0275\u0275text"](4,"Quote Name"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](5,"br"),k["\u0275\u0275elementStart"](6,"mat-form-field",46),k["\u0275\u0275elementStart"](7,"input",47),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().initialQuote.quoteName=t})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](8,"br"),k["\u0275\u0275elementStart"](9,"label"),k["\u0275\u0275text"](10,"Currency"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](11,"br"),k["\u0275\u0275elementStart"](12,"app-input-search",48),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().initialQuote.currency=t})),k["\u0275\u0275pipe"](13,"async"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"mat-dialog-actions",49),k["\u0275\u0275elementStart"](15,"button",50),k["\u0275\u0275text"](16,"Cancel"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](17,"button",31),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().routeToCreatePage()})),k["\u0275\u0275text"](18,"Create Quote"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](7),k["\u0275\u0275property"]("ngModel",e.initialQuote.quoteName),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("list",k["\u0275\u0275pipeBind1"](13,5,e.masterDataService.currency))("hideMatLabel",!0)("disabled",!0)("ngModel",e.initialQuote.currency)}}function yo(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"p",45),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]().$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"]("",e.isFromImport?"Import":"Copy"," Quote!")}}function _o(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"p",45),k["\u0275\u0275text"](1,"Copy as a Change Request Quote!"),k["\u0275\u0275elementEnd"]())}function bo(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",53),k["\u0275\u0275elementStart"](1,"mat-radio-group",54),k["\u0275\u0275elementStart"](2,"mat-radio-button",55),k["\u0275\u0275text"](3,"Copy all Data"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"mat-radio-button",56),k["\u0275\u0275text"](5,"Copy Referencing Rate Card"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("formControl",e.importFormControl),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",1==e.importFormControl.value?"radio-label-color":""),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngClass",2==e.importFormControl.value?"radio-label-color":"")}}function Co(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275template"](1,yo,2,1,"p",51),k["\u0275\u0275template"](2,_o,2,0,"p",51),k["\u0275\u0275elementStart"](3,"label"),k["\u0275\u0275text"](4,"Quote Name"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](5,"br"),k["\u0275\u0275elementStart"](6,"mat-form-field",46),k["\u0275\u0275elementStart"](7,"input",47),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().initialQuote.quoteName=t})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](8,"br"),k["\u0275\u0275elementStart"](9,"label"),k["\u0275\u0275text"](10,"Currency"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](11,"br"),k["\u0275\u0275elementStart"](12,"app-input-search",48),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().initialQuote.currency=t})),k["\u0275\u0275pipe"](13,"async"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](14,bo,6,3,"div",52),k["\u0275\u0275elementStart"](15,"mat-dialog-actions",49),k["\u0275\u0275elementStart"](16,"button",50),k["\u0275\u0275text"](17,"Cancel"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"button",31),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().copyData()})),k["\u0275\u0275text"](19),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","CHANGE_REQUEST"!=e.quoteType),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","CHANGE_REQUEST"==e.quoteType),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngModel",n.initialQuote.quoteName),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("list",k["\u0275\u0275pipeBind1"](13,9,n.masterDataService.currency))("hideMatLabel",!0)("disabled",!0)("ngModel",n.initialQuote.currency),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",e.showImportOption),k["\u0275\u0275advance"](5),k["\u0275\u0275textInterpolate1"]("",e.isFromImport?"Import":"Copy"," Quote")}}function xo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",62),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).clearSearch()})),k["\u0275\u0275elementStart"](1,"mat-icon",63),k["\u0275\u0275text"](2,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}2&e&&(k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip","Clear Search"))}function Eo(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",64),k["\u0275\u0275element"](1,"img",65),k["\u0275\u0275elementStart"](2,"div",66),k["\u0275\u0275text"](3,"No Quotes Found to Import !"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function So(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",64),k["\u0275\u0275element"](1,"mat-spinner",67),k["\u0275\u0275elementStart"](2,"div",68),k["\u0275\u0275text"](3,"Searching..."),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function wo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",74),k["\u0275\u0275elementStart"](2,"span",75),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const n=t.$implicit,i=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"](3).importQuote(i,n)})),k["\u0275\u0275text"](3),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit;k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate2"](" QT/",e.quote_header_id," - ",e.quote_name," ")}}function qo(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"mat-accordion"),k["\u0275\u0275elementStart"](2,"mat-expansion-panel",72),k["\u0275\u0275elementStart"](3,"mat-expansion-panel-header"),k["\u0275\u0275elementStart"](4,"mat-panel-title"),k["\u0275\u0275elementStart"](5,"div",73),k["\u0275\u0275text"](6),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](7,wo,4,2,"ng-container",70),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("expanded",!0),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate2"](" ",null!=e&&e.opportunity_code?e.opportunity_code:"OPP/"+e.opportunity_id," - ",e.opportunity_name," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.quote_list)}}function Do(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",76),k["\u0275\u0275element"](1,"mat-spinner",67),k["\u0275\u0275elementEnd"]())}function Oo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementContainerStart"](0),k["\u0275\u0275elementStart"](1,"div",69),k["\u0275\u0275listener"]("scrolled",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).onScroll()})),k["\u0275\u0275template"](2,qo,8,4,"div",70),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,Do,2,0,"div",71),k["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.oppQuoteList),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.limitLoader)}}function Io(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275elementStart"](1,"p",45),k["\u0275\u0275text"](2,"Import Quote From Opportunities"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",57),k["\u0275\u0275elementStart"](4,"mat-form-field",58),k["\u0275\u0275elementStart"](5,"mat-label"),k["\u0275\u0275text"](6,"Search Opportunities or Quotes"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"input",59),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().searchQuery=t}))("ngModelChange",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().onSearch()})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](8,xo,3,1,"button",60),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](9,Eo,4,0,"div",61),k["\u0275\u0275template"](10,So,4,0,"div",61),k["\u0275\u0275template"](11,Oo,4,5,"ng-container",14),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](7),k["\u0275\u0275property"]("ngModel",e.searchQuery)("formControl",e.importSearchFormControl),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.searchQuery),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",0==e.oppQuoteList.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.searchingFlag&&!e.limitLoader),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.oppQuoteList.length)}}function Po(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",97),k["\u0275\u0275text"](1," - "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","-")}function Mo(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",98),k["\u0275\u0275element"](1,"app-user-image",99),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",e.oid)("matTooltip",e.employee_name)}}function ko(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",79),k["\u0275\u0275elementStart"](1,"div"),k["\u0275\u0275elementStart"](2,"div",80),k["\u0275\u0275elementStart"](3,"div",81),k["\u0275\u0275elementStart"](4,"mat-icon"),k["\u0275\u0275text"](5," send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275text"](6," \xa0 Send Quote For Approval "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"button",82),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(t,"SUBMIT_CANCEL")})),k["\u0275\u0275elementStart"](8,"mat-icon"),k["\u0275\u0275text"](9,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",83),k["\u0275\u0275elementStart"](11,"div",84),k["\u0275\u0275elementStart"](12,"div",85),k["\u0275\u0275elementStart"](13,"div",86),k["\u0275\u0275text"](14,"Quote Name:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"div",85),k["\u0275\u0275elementStart"](16,"div",87),k["\u0275\u0275text"](17),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"div",84),k["\u0275\u0275elementStart"](19,"div",85),k["\u0275\u0275elementStart"](20,"div",86),k["\u0275\u0275text"](21,"Quote Value:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"div",85),k["\u0275\u0275element"](23,"app-currency",88),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](24,"div",89),k["\u0275\u0275elementStart"](25,"div",90),k["\u0275\u0275elementStart"](26,"span"),k["\u0275\u0275text"](27," Approvers \xa0 "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](28,"div",91),k["\u0275\u0275template"](29,Po,2,1,"div",92),k["\u0275\u0275template"](30,Mo,2,2,"div",93),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](31,"div",94),k["\u0275\u0275elementStart"](32,"button",95),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(t.quoteDetails,"SUBMIT_CANCEL")})),k["\u0275\u0275text"](33," Cancel "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](34,"button",96),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeSubmitApprovalPopUp(null==t?null:t.quoteDetails,"SUBMIT")})),k["\u0275\u0275text"](35," Send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](17),k["\u0275\u0275textInterpolate2"](" QT/",null==e?null:e.quoteDetails.quote_header_id," - ",null==e?null:e.quoteDetails.quote_name," "),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("currencyList",null==e||null==e.quoteDetails?null:e.quoteDetails.quote_revenue_value)("showActualAmount",!0),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("ngIf",0==t.reviewersList.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",t.reviewersList),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("disabled",0==t.reviewersList.length)("matTooltip",0==t.reviewersList.length?"No Approvers Found!":"")}}function Fo(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",100),k["\u0275\u0275element"](1,"mat-spinner",101),k["\u0275\u0275elementEnd"]())}function To(e,t){if(1&e&&(k["\u0275\u0275template"](0,ko,36,8,"div",77),k["\u0275\u0275template"](1,Fo,2,0,"div",78)),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.miniLoader),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.miniLoader)}}function Ao(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",90),k["\u0275\u0275elementStart"](1,"span",90),k["\u0275\u0275text"](2," Submitted By \xa0 "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function Ro(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",113),k["\u0275\u0275element"](1,"app-user-image",114),k["\u0275\u0275text"](2,"\xa0\xa0\xa0 "),k["\u0275\u0275elementStart"](3,"div"),k["\u0275\u0275elementStart"](4,"div",115),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",116),k["\u0275\u0275element"](7,"app-user-profile",117),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2).$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",null==e.submitterDetails?null:e.submitterDetails.oid),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"]("A.ID ",(null==e||null==e.submitterDetails?null:e.submitterDetails.associate_id)||(null==t.submitterDetails?null:t.submitterDetails.associate_id),""),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("oid",null==e.submitterDetails?null:e.submitterDetails.oid)}}function Qo(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",97),k["\u0275\u0275text"](1," - "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","-")}function Lo(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",98),k["\u0275\u0275element"](1,"app-user-image",99),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("id",e.oid)("matTooltip",n.getApproverTooltip(e))}}function jo(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"mat-icon",97),k["\u0275\u0275text"](1," info "),k["\u0275\u0275elementEnd"]()),2&e&&k["\u0275\u0275property"]("matTooltip","Comments are required for rejecting document")}function No(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",94),k["\u0275\u0275elementStart"](1,"button",95),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2).$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(t.quoteDetails,"REJECT")})),k["\u0275\u0275text"](2," Reject "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"button",118),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2).$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(null==t?null:t.quoteDetails,"APPROVE")})),k["\u0275\u0275text"](4," Approve "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Vo(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",79),k["\u0275\u0275elementStart"](1,"div"),k["\u0275\u0275elementStart"](2,"div",80),k["\u0275\u0275elementStart"](3,"div",81),k["\u0275\u0275elementStart"](4,"mat-icon"),k["\u0275\u0275text"](5," send "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275text"](6),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"button",102),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"]().$implicit;return k["\u0275\u0275nextContext"]().closeApproverPopUp(t,"CANCEL")})),k["\u0275\u0275elementStart"](8,"mat-icon"),k["\u0275\u0275text"](9,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"div",83),k["\u0275\u0275template"](11,Ao,3,0,"div",103),k["\u0275\u0275template"](12,Ro,8,3,"div",104),k["\u0275\u0275elementStart"](13,"div",105),k["\u0275\u0275elementStart"](14,"div",106),k["\u0275\u0275text"](15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"div",107),k["\u0275\u0275elementStart"](17,"div",91),k["\u0275\u0275template"](18,Qo,2,1,"div",92),k["\u0275\u0275template"](19,Lo,2,2,"div",93),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](20,"div",108),k["\u0275\u0275text"](21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"div",84),k["\u0275\u0275elementStart"](23,"div",85),k["\u0275\u0275elementStart"](24,"div",86),k["\u0275\u0275text"](25,"Quote Name:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](26,"div",85),k["\u0275\u0275elementStart"](27,"div",87),k["\u0275\u0275text"](28),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](29,"div",84),k["\u0275\u0275elementStart"](30,"div",85),k["\u0275\u0275elementStart"](31,"div",86),k["\u0275\u0275text"](32,"Quote Value:"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](33,"div",85),k["\u0275\u0275element"](34,"app-currency",88),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](35,"div",89),k["\u0275\u0275elementStart"](36,"div",109),k["\u0275\u0275elementStart"](37,"span",110),k["\u0275\u0275text"](38," Comments "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](39,jo,2,1,"mat-icon",92),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](40,"textarea",111),k["\u0275\u0275listener"]("ngModelChange",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).commentToSubmitter=t})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](41,No,5,0,"div",112),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]().$implicit,t=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](6),k["\u0275\u0275textInterpolate1"](" \xa0 ","Quote Approval",""),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover),k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate1"](" ",e.quoteDetails.isApprover?"Approvers for Quote Activation Request":"Approvers "," "),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",0==t.reviewersList.length),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",t.reviewersList),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngStyle",t.getStatusCss(null==e.quoteDetails?null:e.quoteDetails.quote_status)),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.quoteDetails.status_name||"-"," "),k["\u0275\u0275advance"](7),k["\u0275\u0275textInterpolate2"](" QT/",null==e?null:e.quoteDetails.quote_header_id," - ",null==e?null:e.quoteDetails.quote_name," "),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("currencyList",null==e||null==e.quoteDetails?null:e.quoteDetails.quote_revenue_value)("showActualAmount",!0),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",(null==e||null==e.quoteDetails?null:e.quoteDetails.isApprover)&&t.allow_approval),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngModel",t.commentToSubmitter)("readonly",!(null!=e.quoteDetails&&e.quoteDetails.isApprover)||3==e.quoteDetails.quote_status||2==e.quoteDetails.quote_status),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.quoteDetails.isApprover&&1===e.quoteDetails.quote_status)}}function Uo(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",100),k["\u0275\u0275element"](1,"mat-spinner",101),k["\u0275\u0275elementEnd"]())}function $o(e,t){if(1&e&&(k["\u0275\u0275template"](0,Vo,42,16,"div",77),k["\u0275\u0275template"](1,Uo,2,0,"div",78)),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngIf",!e.miniLoader),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.miniLoader)}}const Go=[{path:"",redirectTo:"list"},{path:"list",component:(()=>{class e{constructor(e,t,i,o,a,s,r,l,c,d,p,m,h){this.$router=e,this._route=t,this._landingPageService=i,this.masterDataService=o,this._quoteMainService=a,this._dialog=s,this._toaster=r,this._util=l,this.spinner=c,this._login=d,this._help=p,this.opportunityService=m,this._ticket=h,this.initialQuote={quoteName:"",currency:"",deliveryStartDate:null,deliveryEndDate:null,businessType:null,serviceTypeId:null,quoteType:""},this.oppQuoteDetails={},this.quoteConfiguration=[],this.oppQuoteList=[],this.limitLoader=!1,this.limit=25,this.offset=0,this.allDataLoaded=!1,this.searchingFlag=!1,this.searchQuery="",this.dialogOpened=!1,this.quoteCurrency=null,this.helpTopicId=null,this.currentUser=null,this.changeRequestEnabled=!1,this.cr_activation_access=!1,this._onDestroy=new C.b,this.searchFormControl=new u.j(""),this.importSearchFormControl=new u.j(""),this.importFormControl=new u.j("2"),this.quoteSearchSubscription=new x.a,this.importQuoteSearchSubscription=new x.a,this.showDeleted=!1,this.isQuoteBeingImported=!1,this.isCUDEnabled=!0,this.loadingContent="Loading Data...",this.isProjectIntegrated=!0,this.ifUserHasApprovals={},this.allow_approval=!1,this.miniLoader=!1,this.reviewersList=[],this.hasToSendApproval=!1,this.commentToSubmitter="",this.submitterDetails=[],this.enableChangeRequest=!1,this.allowMilestoneOpportunityStatus=[],this.CR_ENABLED_OBJECT=!1,this.isValidServiceType=!1,this.has_parent_opportunity=!1,this.cr_activation_in_progress=!1,this.fieldConfig=[],this.getQuoteConfiguration=()=>new Promise((e,t)=>{this.masterDataService.quoteConfiguration.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.quoteConfiguration=t;for(const e of this.quoteConfiguration)e&&e.quote_config_name&&e.hasOwnProperty("quote_config_value")&&("quote_currency"===e.quote_config_name?this.quoteCurrency=e.quote_config_value:"quote_help_topic_id"===e.quote_config_name?this.helpTopicId=e.quote_config_value:"lumpsum_quote_config"===e.quote_config_name?this.allowMilestoneOpportunityStatus=e.quote_config_value||[]:"change_request_enabled"===e.quote_config_name?this.changeRequestEnabled=e.quote_config_value:"quote_field_config"===e.quote_config_name&&(this.fieldConfig=e.quote_config_value));e(!0)})}),this.getQuoteAccessPrivilege=()=>{this._quoteMainService.getQuoteAccessPrivilege(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{this.isCUDEnabled=e&&"S"==e.messType&&e.data&&e.data.cud_access_enabled||!1},e=>{this.isCUDEnabled=!1,console.log(e),this._toaster.showError("Error","Error in getting Quote Edit Config",this.opportunityService.mediumInterval)})},this.getOppMetaDetails=()=>Object(f.c)(this,void 0,void 0,(function*(){this.CR_ENABLED_OBJECT=this._landingPageService.checkCREnabled(),this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){var t,n,i;if(e&&"S"==e.messType&&e.data){yield this._landingPageService.getQuoteDetails(this.opportunityId),this.oppQuoteDetails.currency=e.data.currency,this.oppQuoteDetails.deliveryStartDate=e.data.delivery_start_date,this.oppQuoteDetails.deliveryEndDate=e.data.delivery_end_date,this.oppQuoteDetails.opportunityStatus=e.data.opportunity_status_code,this.oppQuoteDetails.businessType=e.data.business_type,this.oppQuoteDetails.serviceTypeId=e.data.service_type,this.has_parent_opportunity=1==e.data.has_parent_opportunity||!1,this.isValidServiceType=null===(n=null===(t=this.allowMilestoneOpportunityStatus)||void 0===t?void 0:t.service_type)||void 0===n?void 0:n.includes(this.oppQuoteDetails.serviceTypeId);let o=0===this._landingPageService._quotesList.filter(e=>1==e.flag).length;this.enableChangeRequest="won"==e.data.opportunity_status_code&&this.changeRequestEnabled&&this.CR_ENABLED_OBJECT&&(!this.isValidServiceType||(null===(i=this.allowMilestoneOpportunityStatus)||void 0===i?void 0:i.change_request_applicable))&&!this.has_parent_opportunity&&!o,this.initialQuote.currency=this.oppQuoteDetails.currency,this.initialQuote.deliveryStartDate=this.oppQuoteDetails.deliveryStartDate,this.initialQuote.deliveryEndDate=this.oppQuoteDetails.deliveryEndDate,this.initialQuote.businessType=this.oppQuoteDetails.businessType,this.initialQuote.serviceTypeId=this.oppQuoteDetails.serviceTypeId}else yield this._landingPageService.getQuoteDetails(this.opportunityId)})),e=>Object(f.c)(this,void 0,void 0,(function*(){console.log(e),yield this._landingPageService.getQuoteDetails(this.opportunityId),this._toaster.showError("Error","Error in getting Quote details",this.opportunityService.mediumInterval)})))})),this.deleteQuote=e=>Object(f.c)(this,void 0,void 0,(function*(){var t;const n=yield this.checkIfProjectIntegrated();if(1==e.quote_status)return this._toaster.showWarning("Cannot Delete Quote Under Review","",this.opportunityService.longInterval);if(1!=e.flag&&1!=e.change_request_flag){if(1==e.quote_status)return this._toaster.showWarning("Cannot Delete Quote Under Review","",4500);if(1!=e.flag){if(!n||1!=e.flag)return(e&&e.quote_header_id&&"CHANGE_REQUEST"===e.quote_type?this.enableChangeRequest:this.isCUDEnabled)?void this._util.openConfirmationSweetAlertWithCustom("Are you sure?",`Are you sure you want to delete ${e.quote_name}\n          Quote${(null===(t=this.quoteList)||void 0===t?void 0:t.some(e=>"CHANGE_REQUEST"==e.quote_type&&1==e.change_requst_flag))?"? Deleting this quote will also remove all associated active change requests.":"?"}`).then(t=>{t&&this._quoteMainService.deleteQuote(e.quote_header_id,this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{t&&"S"==t.messType?(this._landingPageService.deleteQuoteFromList(e.quote_header_id),this._toaster.showSuccess("Success","CHANGE_REQUEST"!==e.quoteType?"Quote deleted Successfully !":"Change Request deleted Successfully !",this.opportunityService.longInterval)):this._toaster.showError("Error",t.messText,this.opportunityService.mediumInterval)},t=>{console.log(t),this._toaster.showError("Error","CHANGE_REQUEST"!==e.quoteType?"Error in deleting Quote":"Error in deleting Change Request !",this.opportunityService.mediumInterval)})}):this._toaster.showWarning("Access Restricted!","",this.opportunityService.mediumInterval);this._toaster.showWarning("Cannot Delete Active Quote","Project has already been created for the Opportunity!",this.opportunityService.longInterval)}else this._toaster.showWarning("Cannot Delete Active Quote","",4500)}else this._toaster.showWarning(1==e.change_request_flag?"Cannot Delete Active Change Request":"Cannot Delete Active Quote","",this.opportunityService.longInterval)})),this.checkIfProjectIntegrated=()=>new Promise((e,t)=>{this.opportunityService.getOpportunityProjectDetails(this.opportunityId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.isProjectIntegrated=t&&t.messData.length>0,e(this.isProjectIntegrated)},e=>{console.error("Error occured while fetching project integration for the opportunity!"+e),t(e)})}),this.updateActiveQuote=e=>Object(f.c)(this,void 0,void 0,(function*(){if(this.allow_approval=this._landingPageService.checkQuoteActivateAccess(),1!==e.quote_status){if(yield this.checkIfProjectIntegrated())return this._toaster.showWarning("Cannot Make Quote Active/InActive!","Project has already been created for the Opportunity!",this.opportunityService.longInterval);if(this.isValidServiceType&&!e.hasMilestoneForAllPosition&&0===e.flag)return this._toaster.showWarning("Cannot make the quote active!","Milestone for all quotes positions is not selected!",this.opportunityService.longInterval);if(e&&e.quote_header_id&&this.isCUDEnabled){const t=0==e.flag?1:0;if(t&&!this.resolveQuoteOpportunityCheck(e,"Quote cannot be made Active"))return!0;if(e.quote_revenue_amount<=0)return this._toaster.showWarning("Unable to Activate Quote","The quote value must be greater than 0.",this.opportunityService.longInterval);1!=e.activation_approval_required||this.allow_approval||0!=e.flag?this._landingPageService.updateQuoteActive(this.opportunityId,e.quote_header_id,t).pipe(Object(S.a)(this._onDestroy)).subscribe(n=>{if(n&&"S"==n.messType){if(this._landingPageService.updateActiveFlagInList(e.quote_header_id,t),"string"!=typeof n.data&&n.data.length)for(let e of n.data)this._landingPageService.updateChangeRequestFlagInList(e.quote_header_id);this._toaster.showSuccess("Success",t?"Active Quote updated Successfully !":"Quote made inActive !",this.opportunityService.longInterval),e.flag=t,"string"!=typeof n.data&&this.quoteList.find(e=>"CHANGE_REQUEST"==e.quote_type)&&this._toaster.showInfo("Info","All the Active Change Requests have been made inactive",this.opportunityService.mediumInterval),t&&this.resolveQuoteOpportunityIntegration()}else this._toaster.showError("Error",n.messText,this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in Updating Quote Flag",this.opportunityService.mediumInterval)}):this.openApproversPopUp(e,"SEND")}}})),this.updateActiveChangeRequestQuote=e=>Object(f.c)(this,void 0,void 0,(function*(){if(1!==e.quote_status&&this.checkAndEnableChangeRequest()){if(this.isValidServiceType&&!e.hasMilestoneForAllPosition&&0===e.flag)return this._toaster.showWarning("Cannot make the quote active!","Milestone for all quotes positions is not selected!",this.opportunityService.longInterval);if(e&&e.quote_header_id){if(1==e.change_request_flag)return this._toaster.showWarning("Cannot deactivate Change Request once made active!","",this.opportunityService.longInterval);const t=0==e.change_request_flag?1:0;if(t&&!this.resolveQuoteOpportunityCheck(e,"Quote cannot be made Active"))return!0;let n=yield this.checkForPercentageApproval(e);if(1!=e.activation_approval_required||this.cr_activation_access&&this.CR_ENABLED_OBJECT||e.change_request_flag)if(n&&n.data)n.data&&"approval"==n.type?this.openApproversPopUp(e,"SEND"):n.data&&"error"==n.type&&this._toaster.showError("Change Request Cannot be made Active",n.msg||"Unable to Activate Change request!",3e3);else{if(this.cr_activation_in_progress)return;this.cr_activation_in_progress=!0,this._landingPageService.updateChangeRequestQuoteActive(this.opportunityId,e.quote_header_id,t).pipe(Object(S.a)(this._onDestroy)).subscribe(n=>Object(f.c)(this,void 0,void 0,(function*(){if(n&&n.data.err)return this._toaster.showError("Error",n.data.msg?n.data.msg:"Allocation Of a Position Cannot be reduced",this.opportunityService.longInterval),this.cr_activation_in_progress=!1,!0;n&&"S"==n.messType?(this._toaster.showSuccess("Success",t?"Change Request Activated Successfully !":"Change Request made InActive !",this.opportunityService.mediumInterval),e.change_request_flag=t,this.cr_activation_in_progress=!1,yield this._landingPageService.getQuoteDetails(this.opportunityId)):(this.cr_activation_in_progress=!1,this._toaster.showError("Error",n.messText,this.opportunityService.mediumInterval))})),e=>{console.log(e),this.cr_activation_in_progress=!1,this._toaster.showError("Error","Error in Updating Change Request Flag",this.opportunityService.mediumInterval)})}else this.openApproversPopUp(e,"SEND")}}})),this.editQuote=e=>{if(this.showDeleted)return this._toaster.showSuccess("","CHANGE_REQUEST"!==e.quoteType?"Deleted Quote cannot be Edited":"Deleted Change Request cannot be Edited !",this.opportunityService.longInterval);e.quote_header_id&&this.resolveQuoteOpportunityCheck(e,"CHANGE_REQUEST"!==e.quoteType?"Quote cannot be Edited":"Change Request cannot be Edited !")&&this.$router.navigate(["../edit/"+e.quote_header_id],{relativeTo:this._route})},this.resolveQuoteOpportunityCheck=(e,t)=>{let n=!1;return"CHANGE_REQUEST"!==e.quote_type?I(e.delivery_start_date).isSame(this.oppQuoteDetails.deliveryStartDate,"day")?I(e.delivery_end_date).isSame(this.oppQuoteDetails.deliveryEndDate,"day")?n=!0:this._toaster.showError(t,"Quote Delivery End Date & Opportunity Delivery End Date does not match",this.opportunityService.longInterval):this._toaster.showError(t,"Quote Delivery Start Date & Opportunity Delivery Start Date does not match",this.opportunityService.longInterval):"CHANGE_REQUEST"==e.quote_type&&(I(e.delivery_start_date).isBetween(this.oppQuoteDetails.deliveryStartDate,this.oppQuoteDetails.deliveryEndDate,"day","[]")?n=!0:this._toaster.showError(t,"Quote Delivery Start Date must be between Opportunity Delivery Start Date and Delivery End Date",this.opportunityService.longInterval)),e.quote_currency!=this.oppQuoteDetails.currency&&(this._toaster.showError(t,"Quote Currency & Opportunity Currency does not match",this.opportunityService.longInterval),n=!1),n},this.resolveQuoteOpportunityIntegration=()=>new Promise((e,t)=>{this._quoteMainService.resolveQuoteOppIntegration(this.opportunityId,null,1).pipe(Object(S.a)(this._onDestroy)).subscribe(n=>{"S"==n.messType?(n.quoteMessageType&&("info"==n.quoteMessageType?this._toaster.showInfo("Quote Info",n.messText,this.opportunityService.mediumInterval):"warning"==n.quoteMessageType?this._toaster.showWarning("Quote Warning",n.messText,this.opportunityService.longInterval):"error"==n.quoteMessageType&&this._toaster.showError("Quote Error",n.messText,this.opportunityService.mediumInterval)),n.hasOwnProperty("showConfirmationPopup")&&1==n.showConfirmationPopup&&n.quoteId?this._util.openConfirmationSweetAlertWithCustom("Copy Quote Value?","Do you wish to copy Quote value to Opportunity ?").then(i=>{i?this._quoteMainService.updateValueInOpportunity(this.opportunityId,n.quoteId).pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType?this._toaster.showInfo("Value Updated",t.messText,this.opportunityService.mediumInterval):this._toaster.showError("Error in Updating",t.messText,this.opportunityService.mediumInterval),e(!0)},e=>{console.log(e),this._toaster.showError("Error","Error in Updating Quote value in Opportunity",this.opportunityService.mediumInterval),t(!1)}):e(!0)}):e(!0)):(this._toaster.showError("Error",n.messText,this.opportunityService.mediumInterval),e(!0))},e=>{console.log(e),this._toaster.showError("Error","Error in Checking Quote Configuration",this.opportunityService.mediumInterval),t(!1)})}),this.checkForPercentageApproval=e=>Object(f.c)(this,void 0,void 0,(function*(){try{return(yield this._quoteMainService.checkForPercentageApproval(e))||{}}catch(t){return console.error("Error in percentage approval check:",t),!1}})),this.showDeletedQuotes=()=>{this.showDeleted=!this.showDeleted,this._landingPageService.clearQuoteList(),this._landingPageService.getQuoteDetails(this.opportunityId,this.showDeleted)},this.openActivityLog=e=>Object(f.c)(this,void 0,void 0,(function*(){if(e.quote_header_id){const{QuoteActivityLogComponent:t}=yield Promise.all([n.e(0),n.e(313)]).then(n.bind(null,"YQWb"));this._dialog.open(t,{height:"100vh",width:"35vw",data:{quoteId:e.quote_header_id,showQuoteValue:this.checkFieldShouldbeRestricted("totalRevenue")},position:{right:"0"},disableClose:!1})}})),this.copyData=()=>{this.validateData()&&this.copyDialogRef.close({event:"submit"})},this.validateData=()=>(this.initialQuote.quoteName=this.initialQuote.quoteName.trim(),this.initialQuote.quoteName?this.initialQuote.currency?this.initialQuote.deliveryStartDate&&I(this.initialQuote.deliveryStartDate).isValid()?!(!this.initialQuote.deliveryEndDate||!I(this.initialQuote.deliveryEndDate).isValid())||(this._toaster.showError("Mandatory Field Error","Kindly update Delivery End Date in Opportunity before creating Quote !",this.opportunityService.mediumInterval),!1):(this._toaster.showError("Mandatory Field Error","Kindly update Delivery Start Date in Opportunity before creating Quote !",this.opportunityService.mediumInterval),!1):(this._toaster.showError("Mandatory Field Error","Kindly update Opportunity Currency before creating Quote !",this.opportunityService.mediumInterval),!1):(this._toaster.showError("Mandatory Field Error","Kindly enter Quote Name",this.opportunityService.mediumInterval),!1)),this.copyQuote=(e,t=!1,n)=>Object(f.c)(this,void 0,void 0,(function*(){if(!(e.quote_header_id&&"CHANGE_REQUEST"===n?this.enableChangeRequest:this.isCUDEnabled))return this._toaster.showWarning("Access Restricted!","",this.opportunityService.mediumInterval);{this.initialQuote.quoteName=e.quote_name+" Copy",this.initialQuote.quoteType=n;let i=!1;t&&(i=!0,e.quote_currency!=this.oppQuoteDetails.currency&&(i=!1)),this.copyDialogRef=this._dialog.open(this.copyQuoteTemplate,{height:i?"310px":"270px",width:"440px",data:{isFromImport:t,showImportOption:i,quoteType:n}}),this.copyDialogRef.afterClosed().subscribe(i=>{"submit"==i.event&&(this.spinner.show(),t&&(this.isQuoteBeingImported=!0),this.loadingContent=t?"Importing Quote...":"Copying Quote...",this._quoteMainService.createQuoteFromSourceQuote(e.quote_header_id,this.initialQuote.quoteName,{opportunity_id:this.opportunityId,opp_currency:this.oppQuoteDetails.currency,delivery_start_date:this.oppQuoteDetails.deliveryStartDate,delivery_end_date:this.oppQuoteDetails.deliveryEndDate},t?this.importFormControl.value:null,n).pipe(Object(S.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","CHANGE_REQUEST"!==n?"Quote Copied Successfully !":"Change Request Copied Successfully !",this.opportunityService.mediumInterval),this._landingPageService.getQuoteDetails(this.opportunityId)):this._toaster.showError("Error","CHANGE_REQUEST"!==n?"Error in Copying Quote":"Error in Copying Change Request !",this.opportunityService.mediumInterval),this.spinner.hide(),this.isQuoteBeingImported=!1},e=>{console.log(e),this.spinner.hide(),this.isQuoteBeingImported=!1,this._toaster.showError("Error","CHANGE_REQUEST"!==n?"Error in Copying Quote":"Error in Copying Change Request !",this.opportunityService.mediumInterval)})),this.initialQuote.quoteName="",this.importFormControl.reset("2")})}})),this.getOppQuoteList=()=>Object(f.c)(this,void 0,void 0,(function*(){if(this.limitLoader)return;this.limitLoader=!0;const e=this.offset,t=this.limit;try{const n=yield this._quoteMainService.getOpportunityQuoteList(this.opportunityId,t,e,this.searchQuery).pipe(Object(S.a)(this._onDestroy)).toPromise().catch(e=>{if("UnsubscribedError"===e.name||"The operation was canceled"===e.message)return null;throw e});if(!n)return;n&&n.is_rds_peak&&this._toaster.showError("Error",n.messText||"System unavailable Please Try Again Later",3e3),"S"===n.messType&&n.data&&n.data.length?(this.oppQuoteList=[...this.oppQuoteList,...n.data],this.offset+=this.limit,n.data.length<this.limit&&(this.allDataLoaded=!0),0===this._landingPageService._quotesList.length&&this.isCUDEnabled&&!this.dialogOpened&&(this.dialogOpened=!0,this.openImportQuoteDialog())):this.allDataLoaded=!0}catch(n){console.log(n),this._toaster.showError("Error","Error in getting Opportunity Quote list",this.opportunityService.mediumInterval)}finally{this.limitLoader=!1}})),this.openImportQuoteDialog=()=>Object(f.c)(this,void 0,void 0,(function*(){this.importDialogRef=this._dialog.open(this.importQuoteTemplate,{height:"70vh",width:"35vw",position:{right:"0",top:"28vh"},backdropClass:"quote-transparent-overlay-backdrop"})})),this.importQuote=(e,t)=>{this.copyQuote(t,!0,"TRADITIONAL"),this.importDialogRef.close()}}ngOnInit(){this.currentUser=this._login.getProfile().profile,this.commentToSubmitter="",this.allow_approval=this._landingPageService.checkQuoteActivateAccess(),this._landingPageService.quotesList.subscribe(e=>{this.quoteList=e,this.ifUserHasApprovals=e.find(e=>e.isApprover&&1===e.quote_status)||!1}),this.limit=25,this.offset=0,this._route.data.pipe(Object(Mi.a)(1)).subscribe(({opportunity:e})=>Object(f.c)(this,void 0,void 0,(function*(){this.opportunityId=parseInt(e.opportunityId),yield this.getQuoteConfiguration(),this.cr_activation_access=this._landingPageService.checkCRActivateAccess(),this.getQuoteAccessPrivilege(),yield this.getOppMetaDetails(),yield this._landingPageService.getQuoteDetails(this.opportunityId),this.getOppQuoteList()}))),this.quoteSearchSubscription=this.searchFormControl.valueChanges.pipe(Object(w.a)(500),Object(S.a)(this._onDestroy)).subscribe(e=>{this._landingPageService.searchQuoteList(e)}),this.importQuoteSearchSubscription=this.importSearchFormControl.valueChanges.pipe(Object(w.a)(500),Object(S.a)(this._onDestroy)).subscribe(e=>{this.searchingFlag=!0,this.onSearch()})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this._landingPageService.destroy(),this.spinner.hide(),this.quoteSearchSubscription&&this.quoteSearchSubscription.unsubscribe(),this.importQuoteSearchSubscription&&this.importQuoteSearchSubscription.unsubscribe()}openCreateDialog(e,t){this.createDialogRef=this._dialog.open(e,{height:"270px",width:"440px"}),this.quoteType=t,this.createDialogRef.afterClosed().subscribe(e=>{this.initialQuote={quoteName:"",currency:this.oppQuoteDetails.currency||null,deliveryStartDate:this.oppQuoteDetails.deliveryStartDate||null,deliveryEndDate:this.oppQuoteDetails.deliveryEndDate||null,businessType:this.initialQuote.businessType||null,serviceTypeId:this.oppQuoteDetails.serviceTypeId||null,quoteType:t}})}routeToCreatePage(){var e;this.initialQuote.quoteType=this.quoteType,this.validateData()&&(this._quoteMainService.setInitalQuoteDetail(this.initialQuote),null===(e=this.createDialogRef)||void 0===e||e.close(),this.$router.navigate(["../create/0"],{relativeTo:this._route}))}checkAndEnableChangeRequest(){const e=[{condition:"won"!==this.oppQuoteDetails.opportunityStatus,is_active:!0,message:'Opportunity status must be "Closed As Won" to enable change requests.'},{condition:!this.changeRequestEnabled,is_active:!0,message:"Change request functionality is not enabled in the general configuration."},{condition:this.has_parent_opportunity,is_active:!0,message:"Change requests cannot be made active for child opportunities."},{condition:!this.CR_ENABLED_OBJECT,is_active:!1,message:"You do not have the required permissions to enable change requests.."},{condition:!this.isValidServiceType,is_active:!1,message:"Change requests cannot be made active for the selected opportunity service type"},{condition:0===this._landingPageService._quotesList.filter(e=>1==e.flag).length,is_active:!0,message:"Change Request cannot be made active if there is no Main Active Quote."}].find(e=>e.is_active&&e.condition);return!e||(this.showToaster(e.message),!1)}showToaster(e){this._toaster.showWarning("Warning!",e,this.opportunityService.longInterval)}onSearch(){return Object(f.c)(this,void 0,void 0,(function*(){try{this.searchingFlag=!0,this.oppQuoteList=[],this.offset=0,yield this.getOppQuoteList()}finally{this.searchingFlag=!1}}))}clearSearch(){this.searchQuery="",this.onSearch()}onScroll(){this.limitLoader||this.getOppQuoteList()}openHelpDialog(){this.helpTopicId?this._help.openHelpDialog(this._login.getToken(),this.helpTopicId,this.currentUser):this._toaster.showError("Help Topic is not Configured","Kindly update Help Topic in Quote Configuration",this.opportunityService.longInterval)}openApproversPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){"SEND"===t&&(yield this.getQuoteApprovers(e)),"SEE"===t&&(yield this.getQuoteStatus(e)),this.submitQuoteForApprovalRef=this._dialog.open(this.submitQuoteForApprovalTemplate,{width:"30rem",minHeight:"40vh",data:{type:"SUBMIT",quoteDetails:e,popUpType:t},animation:{entryAnimation:{keyframes:[{transform:"scale(0.5) translateY(100%)",opacity:0},{transform:"scale(1) translateY(0)",opacity:1}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"scale(1) translateY(0)",opacity:1},{transform:"scale(0.5) translateY(100%)",opacity:0}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}},disableClose:!0})}))}closeApproverPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){if(console.log(e),"APPROVE"===t)this.approveOrRejectRef.close(t);else if("REJECT"===t){if(!this.commentToSubmitter||""===this.commentToSubmitter.trim())return void this._toaster.showWarning("Please enter comment before rejecting approval!","",this.opportunityService.longInterval);this.approveOrRejectRef.close(t)}else this.approveOrRejectRef.close("CANCEL")}))}closeSubmitApprovalPopUp(e,t){return Object(f.c)(this,void 0,void 0,(function*(){console.log(e),"SUBMIT"===t&&(yield this.sendForApproval(e),yield this._landingPageService.getQuoteDetails(this.opportunityId)),this.submitQuoteForApprovalRef.close()}))}sendForApproval(e){return Object(f.c)(this,void 0,void 0,(function*(){let t={quoteDetails:e,approvals:this.reviewersList.map(e=>e.associate_id)};try{const e=yield this._landingPageService.submitForQuoteApproval(t).toPromise();console.log("API Response:",e),e.length>0&&"S"===e.messType&&this._toaster.showSuccess("Quote sent for Approval","",this.opportunityService.mediumInterval)}catch(n){console.error("API Error:",n)}}))}getQuoteApprovers(e){return Object(f.c)(this,void 0,void 0,(function*(){this.miniLoader=!0,this._landingPageService.getQuoteApprovers({quoteDetails:e}).subscribe(e=>{console.log("Approvers:",e),this.reviewersList=null==e?void 0:e.data,0==this.reviewersList.length&&this._toaster.showWarning("No Approvers Found for the selected type!","",this.opportunityService.longInterval),this.miniLoader=!1},e=>{console.error("Error fetching approvers:",e),this.miniLoader=!1})}))}getQuoteStatus(e){var t;return Object(f.c)(this,void 0,void 0,(function*(){this.miniLoader=!0;try{let n={quote_id:e.quote_header_id};const i=yield new Promise((e,t)=>{this._landingPageService.getQuoteStatus(n).subscribe(t=>e(t),e=>t(e))});console.log("Approvers:",i);let o=i.data.approvers;this.submitterDetails=null==i?void 0:i.data.submitter[0],this.reviewersList=null==i?void 0:i.data.approvers,o.find(e=>!0===e.isApprover);const a=(null===(t=this.reviewersList.find(e=>e.comments))||void 0===t?void 0:t.comments)||" ";this.commentToSubmitter=a||" ",0===this.submitterDetails.length&&this._toaster.showWarning("No Approvers Found for the selected type!","",this.opportunityService.longInterval)}catch(n){console.error("Error fetching approvers:",n)}finally{this.miniLoader=!1}}))}reject(e){this.approveOrRejectRef=this._dialog.open(this.approveOrRejectTemplate,{width:"30rem",minHeight:"45vh",data:{type:"REJECT",actionTypeName:"Reject",quoteDetails:e},disableClose:!0,panelClass:"custom-mat-dialog-panel"}),this.approveOrRejectRef.afterClosed().subscribe(t=>{t&&"REJECT"==t&&this._landingPageService.approveOrReject({workflowId:e.approval_workflow_header_id,approval_status:"R",comments:this.commentToSubmitter,quoteDetails:e}).subscribe(e=>{console.log("Approvers:",e),e&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Rejected!","",1200))},e=>{var t;console.error("Error fetching approvers:",e);let n=null===(t=null==e?void 0:e.error)||void 0===t?void 0:t.err;"WORKFLOW_TRIGGERED"==(null==n?void 0:n.code)&&(null==n?void 0:n.err)?this._toaster.showWarning("Cannot Reject Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!=(null==n?void 0:n.code)&&(null==n?void 0:n.err)?this._toaster.showWarning("Error rejecting quote!",(null==n?void 0:n.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error rejecting quote!","Kindly try again after sometime",1200),this.miniLoader=!1})})}approve(e){this.approveOrRejectRef=this._dialog.open(this.approveOrRejectTemplate,{width:"30rem",minHeight:"45vh",data:{type:"APPROVE",actionTypeName:"Approve",quoteDetails:e},disableClose:!0,panelClass:"custom-mat-dialog-panel"}),this.approveOrRejectRef.afterClosed().subscribe(t=>{t&&"APPROVE"==t&&this._landingPageService.approveOrReject({workflowId:e.approval_workflow_header_id,approval_status:"A",comments:this.commentToSubmitter,quoteDetails:e}).subscribe(t=>{console.log("Approvers:",t),t&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Approved!","",1200),this.updateActiveQuote(e))},e=>{var t;console.error("Error fetching approvers:",e);let n=null===(t=null==e?void 0:e.error)||void 0===t?void 0:t.err;"WORKFLOW_TRIGGERED"==(null==n?void 0:n.code)&&(null==n?void 0:n.err)?this._toaster.showWarning("Cannot Approve Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!=(null==n?void 0:n.code)&&(null==n?void 0:n.err)?this._toaster.showWarning("Error approving quote!",(null==n?void 0:n.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error approving quote!","Kindly try again after sometime",1200),this.miniLoader=!1})})}openApproverDialog(e){var t,n;return Object(f.c)(this,void 0,void 0,(function*(){yield this.getQuoteStatus(e),this.isCUDEnabled||2!==(null==e?void 0:e.quote_status)||3!==(null==e?void 0:e.quote_status)||this.openApproversPopUp(e,"SEE"),this.approveOrRejectRef=this._dialog.open(this.approveOrRejectTemplate,{width:"35rem",minHeight:"45vh",data:{quoteDetails:e,submitterDetails:this.submitterDetails,approverDetails:this.reviewersList},disableClose:!0,panelClass:"custom-mat-dialog-panel"});const i=yield this.approveOrRejectRef.afterClosed().toPromise();if("CANCEL"!==i){if("APPROVE"===i){let n={workflowId:e.approval_workflow_header_id,approval_status:"A",comments:this.commentToSubmitter,quoteDetails:e};try{const t=yield this._landingPageService.approveOrReject(n).toPromise();console.log("Approvers:",t),t&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Approved!","",1200),this.updateActiveQuote(e))}catch(o){console.error("Error approving quote:",o);let e=null===(t=null==o?void 0:o.error)||void 0===t?void 0:t.err;"WORKFLOW_TRIGGERED"===(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Cannot Approve Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!==(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Error approving quote!",(null==e?void 0:e.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error approving quote!","Kindly try again after sometime",1200),this.miniLoader=!1}}if("REJECT"===i){let t={workflowId:e.approval_workflow_header_id,approval_status:"R",comments:this.commentToSubmitter,quoteDetails:e};try{const e=yield this._landingPageService.approveOrReject(t).toPromise();console.log("Approvers:",e),e&&(this.miniLoader=!1,this._toaster.showSuccess("Quote Rejected!","",1200))}catch(o){console.error("Error rejecting quote:",o);let e=null===(n=null==o?void 0:o.error)||void 0===n?void 0:n.err;"WORKFLOW_TRIGGERED"===(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Cannot Reject Quote!","Approval process already completed",this.opportunityService.longInterval):"WORKFLOW_TRIGGERED"!==(null==e?void 0:e.code)&&(null==e?void 0:e.err)?this._toaster.showWarning("Error rejecting quote!",(null==e?void 0:e.msg)||"Kindly try again after sometime",this.opportunityService.mediumInterval):this._toaster.showError("Error rejecting quote!","Kindly try again after sometime",1200),this.miniLoader=!1}}yield this._landingPageService.getQuoteDetails(this.opportunityId)}}))}getApproverTooltip(e){return`${(null==e?void 0:e.aid)||(null==e?void 0:e.associate_id)} - ${e.employee_name||""}\nStatus: ${e.status_name}`}getStatusCss(e){switch(e){case 4:return{background:"#E8E9EE",color:"#45546E"};case 1:return{background:"#FFF3E8",color:"#FA8C16"};case 2:return{background:"#EEF9E8",color:"#52C41A"};case 3:return{background:"#FFEBEC",color:"#FF3A46"};default:return{}}}checkFieldShouldbeRestricted(e){const t=this._ticket.getCurrentUserRole(),n=this.fieldConfig.find(t=>t.key===e);return!((null==n?void 0:n.role_access_restriction)&&n.role_access_restriction.includes(t))}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](v.g),k["\u0275\u0275directiveInject"](v.a),k["\u0275\u0275directiveInject"](G),k["\u0275\u0275directiveInject"](A.a),k["\u0275\u0275directiveInject"](F.a),k["\u0275\u0275directiveInject"](p.b),k["\u0275\u0275directiveInject"](T.a),k["\u0275\u0275directiveInject"](Q.a),k["\u0275\u0275directiveInject"](R.c),k["\u0275\u0275directiveInject"](ki.a),k["\u0275\u0275directiveInject"](Fi.c),k["\u0275\u0275directiveInject"](L.a),k["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(k["\u0275\u0275viewQuery"](Zi,!0),k["\u0275\u0275viewQuery"](eo,!0),k["\u0275\u0275viewQuery"](to,!0),k["\u0275\u0275viewQuery"](no,!0)),2&e){let e;k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.copyQuoteTemplate=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.importQuoteTemplate=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.submitQuoteForApprovalTemplate=e.first),k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.approveOrRejectTemplate=e.first)}},decls:46,vars:17,consts:[[1,"quote-header"],[1,"total-count"],[1,"toolbar"],[1,"search-container"],[2,"font-size","20px"],["placeholder","Search",1,"input-search",3,"formControl"],["mat-icon-button","","class","shine-icon-container d-flex align-items-center",3,"click",4,"ngIf"],["matTooltip","Help",1,"d-flex",2,"color","#515965","cursor","pointer",3,"click"],["fontSet","material-symbols-outlined",1,"d-flex","align-items-center"],[1,"d-flex","align-item-center","pl-1",2,"font-size","13px"],["mat-icon-button","",1,"d-flex","align-items-center","justify-content-center",3,"matTooltip","click"],[3,"ngClass"],["class","create-quote",4,"ngIf"],["class","create-action",4,"ngIf"],[4,"ngIf"],["createNewTemplate",""],["createNewQuoteDialog",""],["copyQuoteDialog",""],["importQuoteDialog",""],["submitForApproval",""],["approveOrReject",""],["createMoreOptions","matMenu"],["mat-menu-item","","matTooltip","Import a Existing Quote",3,"click"],["bdColor","rgba(0, 0, 0, 0)","size","small","color","#cf0001","type","ball-clip-rotate",3,"fullScreen"],[2,"color","#cf0001","margin-top","10vh !important","font-weight","400"],["mat-icon-button","",1,"shine-icon-container","d-flex","align-items-center",3,"click"],["fontSet","material-symbols-outlined","fontSet","material-symbols-outlined",3,"matTooltip","ngClass"],[1,"create-quote"],["mat-button","",3,"click"],["mat-button","",1,"dropdown",3,"matMenuTriggerFor"],[1,"create-action"],["mat-button","",1,"create-quote",3,"click"],[4,"ngIf","ngIfElse"],[1,"row","p-2"],["class","col-3 p-2",3,"quote","isCUDEnabled","isCRApplicable","showDeleted","showApprovalButtons","showQuoteValue","delete","edit","copy","copyAsCR","flagged","changeRequestFlagged","activityLog","openApproverPopUp",4,"ngFor","ngForOf"],[1,"col-3","p-2",3,"quote","isCUDEnabled","isCRApplicable","showDeleted","showApprovalButtons","showQuoteValue","delete","edit","copy","copyAsCR","flagged","changeRequestFlagged","activityLog","openApproverPopUp"],["class","body-card",4,"ngIf"],[1,"body-card"],["src","https://assets.kebs.app/images/no_data_found.png","height","200","width","270",1,"mt-5","mb-3"],[1,"text-wrapper"],[1,"text-main"],["class","text-content",4,"ngIf"],[1,"text-content"],["mat-stroked-button","",3,"click"],[1,"p-3","create-quote-dialog"],[1,"dialog-title"],["appearance","outline"],["matInput","","placeholder","Quote Name","required","true",3,"ngModel","ngModelChange"],["placeholder","Currency","required","true",3,"list","hideMatLabel","disabled","ngModel","ngModelChange"],["align","start"],["mat-stroked-button","","mat-dialog-close",""],["class","dialog-title",4,"ngIf"],["class","p-1",4,"ngIf"],[1,"p-1"],[1,"radio-btn-class",3,"formControl"],["value","1",3,"ngClass"],["value","2",1,"pl-3",3,"ngClass"],[1,"search-bar"],["appearance","outline",1,"w-100"],["matInput","","placeholder","Search by Opportunity Name, Opportunity ID, Quote Name or Quote ID ",3,"ngModel","formControl","ngModelChange"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",3,"click",4,"ngIf"],["class","d-flex justify-content-center align-items-center","style","flex-direction: column;",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",3,"click"],[2,"color","#a3a7ad","font-size","19px",3,"matTooltip"],[1,"d-flex","justify-content-center","align-items-center",2,"flex-direction","column"],["src","https://assets.kebs.app/images/no_data_found.png","height","180","width","250",1,"mt-5","mb-3"],[2,"color","#45546e","font-weight","700"],["diameter","25"],[2,"color","#45546e","font-weight","600","font-size","13px"],["infinite-scroll","",1,"pt-2","pb-2",2,"max-height","400px","overflow-y","auto",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],["class","d-flex justify-content-center align-items-center pt-1",4,"ngIf"],["hideToggle","",3,"expanded"],[1,"row","quote-title",2,"color","#ef4a61"],[1,"row","pt-2","pl-2","quote-title"],[2,"cursor","pointer",3,"click"],[1,"d-flex","justify-content-center","align-items-center","pt-1"],["class","submit-for-approval-dialog","style","min-height: 40vh",4,"ngIf"],["class","mini-loader",4,"ngIf"],[1,"submit-for-approval-dialog",2,"min-height","40vh"],["mat-dialog-title","",1,"row","header-submit-pop"],[1,"heading"],["mat-icon-button","",1,"d-flex","justify-content-center","align-items-center",3,"click"],[1,"qb-details"],[1,"row"],[1,"col-5","d-flex","align-items-center"],[1,"qb-title"],[1,"qb-value"],["type","small",3,"currencyList","showActualAmount"],[1,"approvers-profiles","ml-3","mt-3"],[1,"top-header","mb-2"],[1,"d-flex","align-items-center","justify-content-start","approvers-container"],[3,"matTooltip",4,"ngIf"],["class","approver-item",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center","align-items-center","mt-3","pt-3"],["mat-button","",1,"dialog-cancel-btn",3,"click"],["mat-raised-button","","mat-button","",1,"dialog-submit-btn",3,"disabled","matTooltip","click"],[3,"matTooltip"],[1,"approver-item"],["imgWidth","33px","imgHeight","33px",3,"id","matTooltip"],[1,"mini-loader"],["diameter","26",1,"spinner","d-flex","justify-content-center"],["matTooltip","Close","mat-icon-button","",1,"d-flex","justify-content-center","align-items-center",3,"click"],["class","top-header mb-2",4,"ngIf"],["class","d-flex align-items-center mb-3 submitter-details",4,"ngIf"],[1,"approvers-profiles","mb-3","mt-3"],[1,"top-header","mb-2",2,"font-weight","600 !important"],[1,"d-flex","align-items-center","justify-content-between"],[1,"uploader-details-desc","d-flex","align-items-center","justify-content-center",3,"ngStyle"],[1,"d-flex","align-items-center","justify-content-start"],[1,"top-header",2,"font-weight","600 !important"],["rows","5","cols","40",3,"ngModel","readonly","ngModelChange"],["class","d-flex justify-content-center align-items-center mt-3 pt-3",4,"ngIf"],[1,"d-flex","align-items-center","mb-3","submitter-details"],["imgWidth","28px","imgHeight","28px",3,"id"],[2,"font-size","12px"],[1,"reviwer-name"],["type","name",3,"oid"],["mat-raised-button","","mat-button","",1,"dialog-submit-btn",3,"click"]],template:function(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",0),k["\u0275\u0275elementStart"](1,"div",1),k["\u0275\u0275elementStart"](2,"p"),k["\u0275\u0275text"](3),k["\u0275\u0275pipe"](4,"async"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"p"),k["\u0275\u0275text"](6,"Total Quotations"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"div",2),k["\u0275\u0275elementStart"](8,"div",3),k["\u0275\u0275elementStart"](9,"mat-icon",4),k["\u0275\u0275text"](10,"search"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](11,"input",5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](12,oo,3,4,"button",6),k["\u0275\u0275elementStart"](13,"div",7),k["\u0275\u0275listener"]("click",(function(){return t.openHelpDialog()})),k["\u0275\u0275elementStart"](14,"mat-icon",8),k["\u0275\u0275text"](15,"info"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"span",9),k["\u0275\u0275text"](17," Help "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"button",10),k["\u0275\u0275listener"]("click",(function(){return t.showDeletedQuotes()})),k["\u0275\u0275elementStart"](19,"mat-icon",11),k["\u0275\u0275text"](20,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](21,ao,6,1,"div",12),k["\u0275\u0275template"](22,so,3,0,"div",13),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](23,uo,2,1,"ng-container",14),k["\u0275\u0275pipe"](24,"sortQuotes"),k["\u0275\u0275pipe"](25,"async"),k["\u0275\u0275template"](26,go,1,1,"ng-template",null,15,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](28,vo,4,0,"ng-container",14),k["\u0275\u0275template"](29,fo,19,7,"ng-template",null,16,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](31,Co,20,11,"ng-template",null,17,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](33,Io,12,6,"ng-template",null,18,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](35,To,2,2,"ng-template",null,19,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275template"](37,$o,2,2,"ng-template",null,20,k["\u0275\u0275templateRefExtractor"]),k["\u0275\u0275elementStart"](39,"mat-menu",null,21),k["\u0275\u0275elementStart"](41,"button",22),k["\u0275\u0275listener"]("click",(function(){return t.openImportQuoteDialog()})),k["\u0275\u0275text"](42,"Import A Quote"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](43,"ngx-spinner",23),k["\u0275\u0275elementStart"](44,"p",24),k["\u0275\u0275text"](45),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){let e=null;k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate"]((null==(e=k["\u0275\u0275pipeBind1"](4,11,t._landingPageService.quotesList))?null:e.length)||0),k["\u0275\u0275advance"](8),k["\u0275\u0275property"]("formControl",t.searchFormControl),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.ifUserHasApprovals),k["\u0275\u0275advance"](6),k["\u0275\u0275property"]("matTooltip",t.showDeleted?"Active Quotes":"Show Deleted Quotes"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",t.showDeleted?"del-icon":"no-del-icon"),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",t.isCUDEnabled),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t.enableChangeRequest),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",k["\u0275\u0275pipeBind1"](24,13,k["\u0275\u0275pipeBind1"](25,15,t._landingPageService.quotesList))),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("ngIf",t._landingPageService.isQuoteLoading),k["\u0275\u0275advance"](15),k["\u0275\u0275property"]("fullScreen",!1),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",t.loadingContent," ")}},directives:[a.a,u.e,u.v,u.k,i.NgIf,s.a,o.a,i.NgClass,m.g,m.d,R.a,m.f,i.NgForOf,Hi,r.c,c.b,u.F,u.y,X.a,p.c,p.d,Xi.b,Xi.a,r.g,r.i,H.c,Yi.a,Ki.a,Ki.c,Ki.g,Ki.h,p.i,Ti.a,Y.a,i.NgStyle,K.a],pipes:[i.AsyncPipe,Ji],styles:['.quote-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));grid-auto-rows:200px;grid-gap:24px;padding:24px}.quote-grid[_ngcontent-%COMP%]   quote-card[_ngcontent-%COMP%]{height:100%;width:100%}.quote-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:24px;height:85px}.quote-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]{color:#515965;font-size:14px;font-weight:500}.quote-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0!important}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]{flex:1;justify-content:end;grid-gap:24px;display:flex;padding-inline:24px;align-items:center}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;cursor:pointer;display:flex;align-items:center}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .shine-icon-container[_ngcontent-%COMP%]{position:relative;overflow:hidden;width:auto;height:50px;display:inline-block;margin:0;border-radius:5px}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .shine-icon[_ngcontent-%COMP%]{position:relative;font-size:18px;color:#cf0001}@keyframes shine{0%{opacity:.5;transform:translate(-100%,-100%)}50%{opacity:1;transform:translate(100%,100%)}to{opacity:.5;transform:translate(200%,200%)}}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .shine-icon-container[_ngcontent-%COMP%]:after{content:"";position:absolute;top:-110%;left:-210%;width:200%;height:200%;opacity:0;transform:rotate(30deg);background:hsla(0,0%,100%,.13);background:linear-gradient(90deg,hsla(0,0%,100%,.13) 0,hsla(0,0%,100%,.13) 77%,hsla(0,0%,100%,.5) 92%,hsla(0,0%,100%,0));animation:shine 2s ease-in-out infinite}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .shine-icon-container[_ngcontent-%COMP%]:hover:after{opacity:1}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .shine-icon-container[_ngcontent-%COMP%]:active:after{opacity:0}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{color:#45546e;padding-right:12px;border-right:2px solid #e8e9ee;font-size:16px;height:32px;display:flex;justify-content:end;align-items:center}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin:0}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]{outline:none;border:none;min-width:65px;max-width:65px;background:none;color:inherit;transition:all 1.5s;width:auto;white-space:nowrap;font-size:14px}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:focus, .quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:not(:placeholder-shown){min-width:150px;max-width:400px;width:auto;white-space:normal}.quote-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]::placeholder{color:inherit;opacity:1}.body-card[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #e8e9ee;background:#fff;height:calc(100vh - 215px);margin-inline:24px;display:flex;justify-content:center;align-items:center;flex-direction:column}.body-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-bottom:24px}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   .text-main[_ngcontent-%COMP%]{color:#45546e;text-align:center;font-size:16px;font-weight:700;line-height:24px;letter-spacing:.32px;text-transform:capitalize}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{color:#45546e;text-align:center;font-size:14px;font-weight:400;margin-bottom:0!important}.body-card[_ngcontent-%COMP%]   .create-action[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:315px}.create-quote[_ngcontent-%COMP%]{font-size:13px;color:#fff;font-weight:700!important;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:4px;display:flex;align-items:center;justify-content:space-around}.create-quote[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]{padding:0 10px 0 0;min-width:24px}.create-quote[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{font-size:13px;color:#fff;font-weight:700!important}.create-quote-dialog[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px;letter-spacing:.28px;margin-bottom:0!important}.create-quote-dialog[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%], .create-quote-dialog[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#45546e;font-weight:500;line-height:24px;text-transform:capitalize}.create-quote-dialog[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%]{font-size:16px}  .create-action .mat-stroked-button:not(.mat-button-disabled),   .create-quote-dialog .mat-stroked-button:not(.mat-button-disabled){border-color:#45546e}.no-del-icon[_ngcontent-%COMP%]{color:#45546e;font-size:22px}.del-icon[_ngcontent-%COMP%]{color:#ef4a61;font-size:22px}.quote-transparent-overlay-backdrop[_ngcontent-%COMP%]    .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}.quote-title[_ngcontent-%COMP%]{font-size:14px;font-weight:700;color:#45546e;text-transform:none}.radio-btn-class[_ngcontent-%COMP%]{font-size:13px}.radio-btn-class[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#f27a6c}.radio-btn-class[_ngcontent-%COMP%]   .mat-radio-button.mat-accent.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-persistent-ripple[_ngcontent-%COMP%], .radio-btn-class[_ngcontent-%COMP%]   .mat-radio-button.mat-accent[_ngcontent-%COMP%]   .mat-radio-ripple[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]:not(.mat-radio-persistent-ripple), .radio-btn-class[_ngcontent-%COMP%]   .mat-radio-button.mat-accent[_ngcontent-%COMP%]:active   .mat-radio-persistent-ripple[_ngcontent-%COMP%], .radio-btn-class[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:#f27a6c}.radio-btn-class[_ngcontent-%COMP%]   .radio-label-color[_ngcontent-%COMP%]{color:#f27a6c}.submit-for-approval-dialog[_ngcontent-%COMP%]{overflow-y:auto;overflow-x:hidden;padding:1rem;min-height:40vh}.dialog-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700;line-height:28px}.dialog-desc[_ngcontent-%COMP%], .dialog-title[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;text-align:center}.dialog-desc[_ngcontent-%COMP%]{font-size:14px;font-weight:400;line-height:20px;color:#475467}.dialog-submit-btn[_ngcontent-%COMP%]{background:#ef4a61!important;border-radius:4px;color:#fff!important;border:1px solid #ef4a61!important}.dialog-cancel-btn[_ngcontent-%COMP%], .dialog-submit-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:5rem;font-size:14px;font-weight:500!important}.dialog-cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:4px;font-family:Plus Jakarta Sans;margin-left:auto;margin-right:1.5rem}.heading[_ngcontent-%COMP%]{justify-content:start;font-weight:700;font-size:18px;padding-top:.2rem;font-family:Plus Jakarta Sans!important}.heading[_ngcontent-%COMP%], .heading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;align-items:center;color:#272a47}.heading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{justify-content:center;font-size:22px}.top-header[_ngcontent-%COMP%]{font-size:13px;font-weight:600;line-height:16px;letter-spacing:.02em;text-align:left;color:#45546e;font-family:Plus Jakarta Sans}.approvers-profiles[_ngcontent-%COMP%]{align-items:center;justify-content:start;gap:5%}.approvers-profiles[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{border:1px solid #dadce2;font-family:Plus Jakarta Sans;color:#45546e;font-size:13px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;margin-top:1%;height:5rem;resize:none;padding:1rem;border-radius:3px;width:95%}.approvers-profiles[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border:2px solid #ef4a61!important;outline:none}.approvers-profiles[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;color:#45546e;display:flex;align-items:center;justify-content:center}.approvers-profiles[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]{font-size:13px;font-weight:500;line-height:16px;letter-spacing:.02em;text-align:left;color:#45546e;font-family:Plus Jakarta Sans}.approvers-profiles[_ngcontent-%COMP%]   .uploader-details-desc[_ngcontent-%COMP%]{margin-top:3%;font-size:12px;font-weight:600;line-height:16px;letter-spacing:.02em;text-align:left;display:flex;align-items:center;border-radius:4px;height:2rem;padding:2%}.approvers-profiles[_ngcontent-%COMP%]   .approvers-container[_ngcontent-%COMP%]{position:relative}.approvers-profiles[_ngcontent-%COMP%]   .approver-item[_ngcontent-%COMP%]{margin-left:-10px;z-index:auto}.approvers-profiles[_ngcontent-%COMP%]   .approver-item[_ngcontent-%COMP%]:first-child{margin-left:0}.submitter-details[_ngcontent-%COMP%]{font-size:13px;font-weight:500;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;font-family:Plus Jakarta Sans;white-space:nowrap}.qb-details[_ngcontent-%COMP%]{padding:0 1rem}.qb-title[_ngcontent-%COMP%]{font-size:13px;font-weight:700;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.reviewer-name[_ngcontent-%COMP%]{font-size:12px;margin-top:-2%;font-weight:700}.qb-value[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#45546e;text-transform:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mini-loader[_ngcontent-%COMP%]{min-height:40vh;display:flex;align-items:center;justify-content:center}.header-submit-pop[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;color:#5f5f5f;font-size:11px!important;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}']}),e})()},{path:"create/:quoteId",component:Pi},{path:"edit/:quoteId",component:Pi}];let Bo=(()=>{class e{}return e.\u0275mod=k["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=k["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[v.k.forChild(Go)],v.k]}),e})();var zo=n("lWFW"),Wo=n("Xi0T"),Ho=n("OQIA");let Xo=(()=>{class e{}return e.\u0275mod=k["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=k["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Bo,o.b,a.b,s.b,r.e,c.c,l.b,u.E,d.h,p.g,m.e,h.g,g.h,zo.a,Wo.a,p.g,u.E,u.p,R.b,H.b,z.b,Ki.b,Xi.c,Ho.a,Yi.b]]}),e})()},WM9j:function(e,t,n){"use strict";for(var i=n("BuRe"),o=[],a=0;a<256;++a)o.push((a+256).toString(16).substr(1));t.a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase();if(!Object(i.a)(n))throw TypeError("Stringified UUID is invalid");return n}},c9BX:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL"),o=n("3Pt+");let a=(()=>{class e{constructor(e){this.ngControl=e,this.showSuffix=!1,this.decimalPart=2,this.disabledD=!1,this.allowNegative=!0,this.resolveFcCs=e=>{if(null==e)return this.ngControl.valueAccessor.writeValue(0),void this.ngControl.control.setValue(0,{emitEvent:!1,emitModelToViewChange:!1});let t=this.convertValueFormat(e);void 0!==t?(this.showSuffix&&(t+=" "+(this.suffix?this.suffix:this.currency)),this.ngControl.valueAccessor.writeValue(t),this.ngControl.control.setValue(this.parseValue(e),{emitEvent:!1,emitModelToViewChange:!1})):(this.ngControl.valueAccessor.writeValue(0),this.ngControl.control.setValue(0,{emitEvent:!1,emitModelToViewChange:!1})),this.setControlDisabledState(this.disabledD)},this.convertValueFormat=e=>{if("number"==typeof e&&(e=e.toString()),e=e.replace(/,/g,"").replace(/[^0-9.-]/g,""),this.allowNegative||(e=e.replace(/-/g,"")),"0-"==e)return"-";e.endsWith("-")&&(e=e.slice(0,-1));let t=e.split("."),n=t[0],i=void 0!==t[1]?"."+t[1]:"";return this.maxDigits&&n.length>this.maxDigits&&(n=n.substring(0,this.maxDigits)),(0===this.decimalPart?this.allowNegative?/^-?\d*$/:/^\d*$/:new RegExp(`^${this.allowNegative?"-?":""}\\d*(\\.\\d{0,${this.decimalPart}})?$`)).test(e)||(i=t.length>1?"."+t[1].substring(0,this.decimalPart):""),isNaN(parseFloat(e))?void 0:(n="INR"===this.currency?new Intl.NumberFormat("en-IN").format(Number(n)):new Intl.NumberFormat("en-US").format(Number(n)),i&&this.decimalPart>0?`${n}${i}`:n)},this.parseValue=e=>{if("string"==typeof e){const t=e.trim();let n=""===t?0:parseFloat(t.replace(/,/g,""));return!this.allowNegative&&n<0&&(n=Math.abs(n)),n}return"number"==typeof e?this.allowNegative?e:Math.abs(e):0}}ngOnInit(){this.resolveFcCs(this.ngControl.control.value),this.valueSubscription=this.ngControl.control.valueChanges.subscribe(e=>{null!=e&&this.resolveFcCs(e)})}ngOnChanges(e){(e.currency||e.allowNegative)&&this.resolveFcCs(this.ngControl.control.value)}setControlDisabledState(e){e?this.ngControl.control.disable({emitEvent:!1}):this.ngControl.control.enable({emitEvent:!1})}ngOnDestroy(){this.valueSubscription&&this.valueSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.u))},e.\u0275dir=i["\u0275\u0275defineDirective"]({type:e,selectors:[["","appFcCs",""]],inputs:{currency:"currency",showSuffix:"showSuffix",suffix:"suffix",decimalPart:"decimalPart",disabledD:"disabledD",maxDigits:"maxDigits",allowNegative:"allowNegative"},features:[i["\u0275\u0275NgOnChangesFeature"]]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n("mrSG"),o=n("XNiG"),a=n("xG9w"),s=n("fXoL"),r=n("tk/3"),l=n("LcQX"),c=n("XXEo"),u=n("flaP");let d=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new o.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,o,a,s){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:o,limit:a,filterConfig:s,orgIds:r})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,o,a,s){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:o,limit:a,filterConfig:s,orgIds:r})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:o})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,o,s,r,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=r&&r.length>1&&(yield this.getManpowerCostByOId(r,n,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,s,l));let c=yield this.getNonManpowerCost(t,n,o,s,2),u=yield this.getAllocatedCost(),d=0;d=(i?i.cost:0)+c.length>0?a.reduce(a.pluck(c,"cost"),(e,t)=>e+t,0):0;let p=u.length>0?a.reduce(a.pluck(u,"percentage"),(e,t)=>e+t,0):0;return{cost:d,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:c,allocatedCost:u,allocatedCostValue:d*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,o){return new Promise((a,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:o}).subscribe(e=>a(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,n,i,o){return new Promise((a,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:o}).subscribe(e=>a(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((o,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](r.c),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a),s["\u0275\u0275inject"](u.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var i=n("mrSG"),o=n("fXoL"),a=n("3Pt+"),s=n("jtHE"),r=n("XNiG"),l=n("NJ67"),c=n("1G5W"),u=n("xG9w"),d=n("t44d"),p=n("kmnG"),m=n("ofXK"),h=n("d3UM"),g=n("FKr1"),v=n("WJ5W"),f=n("Qu3c");const y=["singleSelect"];function _(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275text"](1,"Select one"),o["\u0275\u0275elementEnd"]()),2&e&&o["\u0275\u0275property"]("value",null)}function b(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(n)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let C=(()=>{class e extends l.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new s.a,this.change=new o.EventEmitter,this._onDestroy=new r.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=u.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2),o["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](y,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-select",1,2),o["\u0275\u0275elementStart"](3,"mat-option"),o["\u0275\u0275element"](4,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](5,_,2,1,"mat-option",4),o["\u0275\u0275template"](6,b,2,3,"mat-option",5),o["\u0275\u0275pipe"](7,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275property"]("ngStyle",t.isDisabled()),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.showSelect),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[p.c,m.NgStyle,h.c,a.v,a.k,a.F,g.p,v.a,m.NgIf,m.NgForOf,f.a],pipes:[m.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()},vBGi:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("fXoL");let o=(()=>{class e{transform(e,t,n,i){if(t&&t.length&&n&&i)for(const o of t)if(o[i]==e)return o[n];return"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"displayValue",type:e,pure:!0}),e})()},yu80:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n("mrSG"),o=n("xG9w"),a=n("XNiG"),s=n("2Vo4"),r=n("fXoL"),l=n("tk/3"),c=n("flaP"),u=n("XXEo"),d=n("LcQX");let p=(()=>{class e{constructor(e,t,n,i){this.$http=e,this._roles=t,this._auth=n,this._util=i,this.applicationName="AMS",this.currentUser=this._auth.getProfile().profile,this.activitySubject=new a.b,this.getActivityObservable=this.activitySubject.asObservable(),this.actualHoursSubject=new s.a({}),this.getActualHoursObservable=this.actualHoursSubject.asObservable(),this.taskRefreshSubject=new a.b,this.getTaskRefreshObservable=this.taskRefreshSubject.asObservable(),this.task_status_colors=[{statusName:"Open",statusColor:"#928F8D"},{statusName:"In Progress",statusColor:"#ff7200"},{statusName:"Completed",statusColor:"#4caf50"}],this.supportTeams=[],this.statusColorArray=[],this.priorityArray=[],this.getAmsAccessForUser(),this.getCurrentUserRole(),this.getSupportTeamData()}setActivityObservable(e){this.activitySubject.next(e)}setTaskRefreshObservable(e){this.taskRefreshSubject.next(e)}setActualHoursObservable(e){this.actualHoursSubject.next(e)}showMessage(e){this._util.showToastMessage(e)}showErrorMessage(e){this._util.showErrorMessage(e,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}getAmsAccessForUser(){let e=o.findWhere(this._roles.roles,{application_id:95,object_id:13});return!e||"Update"!=e.operation&&"*"!=e.operation}getStatusObjectEntries(){let e=o.where(this._roles.roles,{application_id:95,object_id:66});return e.length>0?JSON.parse(e[0].object_entries):null}getTicketCreationObjectEntries(){return Object(i.c)(this,void 0,void 0,(function*(){return o.where(this._roles.roles,{application_id:95,object_id:565})}))}getCreateAccess(){let e=o.where(this._roles.roles,{application_id:95,object_id:67});return e.length>0&&"*"==e[0].object_value}checkIfTicketIsEditable(e,t){if(this.supportTeams.length>0)for(let n of this.supportTeams){n.team_coordinator="string"==typeof n.team_coordinator?JSON.parse(n.team_coordinator):n.team_coordinator;for(let e of n.team_coordinator)if(e==this.currentUser.oid)return!0;n.team_head="string"==typeof n.team_head?JSON.parse(n.team_head):n.team_head;for(let e of n.team_head)if(e==this.currentUser.oid)return!0}return!o.contains(e.ticket_non_editable,t.status[0]._id)}getSupportTeamData(){return new Promise((e,t)=>{this.getSupportTeams().subscribe(n=>{"S"==n.messType&&n.data.length>0?(this.supportTeams=n.data,e(!0)):t("Not data found !")},e=>{t(e)})})}checkIfUserIsCustomer(){return!(!this._roles.roles||!this._roles.roles[0]||35!=this._roles.roles[0].role_id)}getCurrentCustomerDetails(){return this.$http.post("/api/ams/configuration/getCurrentCustomerDetails",{})}getStatusList(){return this.$http.post("/api/ams/configuration/getStatusList",{statusValues:this.getStatusObjectEntries()})}getTypeList(){return this.$http.post("/api/ams/configuration/getTypeList",{})}getAmsPostingDate(){return this.$http.post("/api/ams/configuration/getAmsPostingDate",{})}getAMSNewReleases(){return this.$http.post("/api/ams/configuration/getAMSNewReleases",{})}saveTicketToCollection(e){return this.$http.post("/api/ams/ticket/createTicket",{ticketDetails:e})}createTask(e,t){return this.$http.post("/api/ams/tasks/createTasks",{ticket_id:e,tasks:t})}createTaskFromTemplate(e,t,n,i,o){return this.$http.post("/api/ams/tasks/assignTaskFromTemplate",{task_template_id:e,ticket_id:t,estimated_closure_date:n,created_on:i,assigned_to:o})}addTask(e,t,n){return this.$http.post("/api/ams/tasks/addTasks",{task_id:e,ticket_id:t,tasks:n})}updateTaskObject(e,t,n,i){return this.$http.post("/api/ams/tasks/updateTaskObject",{task_id:e,sub_task_id:t,key:n,object:i})}editTaskStatus(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskStatus",{task_id:e,task_item:t,status:n,ticket_id:i})}editTaskDueDate(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskDueDate",{task_id:e,task_item:t,due_on:n,ticket_id:i})}editTaskPlannedHours(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskPlannedHours",{task_id:e,task_item:t,planned_hours:n,ticket_id:i})}editTaskName(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskName",{task_id:e,task_item:t,task_name:n,ticket_id:i})}editTaskAssigned(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskAssigned",{task_id:e,task_item:t,assigned_to:n,ticket_id:i})}addTaskActualHours(e,t,n,i,o){return this.$http.post("/api/ams/tasks/addTaskActualHours",{task_id:e,task_item:t,actual_hours:n,timesheet_id:i,ticket_id:o})}editTaskActualHours(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskActualHours",{task_id:e,task_item:t,actual_hours:n,ticket_id:i})}deleteTaskActualHours(e,t,n,i,o){return this.$http.post("/api/ams/tasks/deleteTaskActualHours",{task_id:e,task_item:t,actual_hours:n,ticket_id:i,timesheet_id:o})}getTicketMasterData(){return this.$http.post("/api/ams/configuration/getTicketMasterData",{})}getTicketProperties(){return this.$http.post("/api/ams/configuration/getTicketProperties",{})}getTicketById(e){return this.$http.post("/api/ams/ticket/getTicketById",{ticketId:e})}getTicketBasedOnSearch(e,t){return this.$http.post("/api/ams/ticket/getTicketBasedOnSearch",{searchParameter:e,ticketList:t})}getTicketMetaData(e){return this.$http.post("/api/ams/ticket/getTicketMetaData",{ticketId:e})}getTicketAttachment(e){return this.$http.post("/api/ams/ticket/getTicketAttachment",{ticketId:e})}getFileDataForDownload(e){return this.$http.post("/api/ams/configuration/getAMSAttachment",{key:e})}deleteTicketAttachment(e,t){return this.$http.post("/api/ams/configuration/deleteAMSAttachment",{ticketId:e,file:t})}updateTicketAttachment(e,t){return this.$http.post("/api/ams/configuration/updateAMSAttachment",{ticketId:e,file:t})}getTicketsForEmployee(e){return this.$http.post("/api/ams/ticket/getAllTicketsForEmployee",{employee_oid:e,org_codes:this._roles.getUserRoleOrgCodes("AMS"),role_id:this._roles.roles[0].role_id,is_read_only:this.getAmsAccessForUser()})}getConsultantBasedOnProjectModule(e,t){return this.$http.post("/api/ams/configuration/getConsultantBasedOnProjectModule",{project_item_id:e,module_id:t})}getActivity(e){return this.$http.post("/api/ams/activities/getActivity",{activityId:e})}addNoteActivity(e,t){return this.$http.post("/api/ams/activities/addNoteActivity",{activity_id:e,activities:t})}editActivity(e,t){return this.$http.post("/api/ams/activities/editActivity",{activity_id:e,activity_details:t})}getTodoDetails(e){return this.$http.post("/api/ams/todo/getTodo",{todoId:e})}getTicketCTAs(e){return this.$http.post("/api/ams/configuration/getTicketCTAs",{ticketId:e})}getTicketTasks(e){return this.$http.post("/api/ams/tasks/getTaskById",{task_id:e})}getTaskTemplate(){return this.$http.post("/api/ams/tasks/getTaskTemplates",{})}getConfigBasedOnProject(e){return this.$http.post("/api/ams/configuration/getConfigBasedOnProject",{itemId:e})}getModuleBasedOnSubmodule(e){return this.$http.post("/api/ams/configuration/getModuleBasedOnSubmodule",{subModuleId:e})}createTodo(e,t){return this.$http.post("/api/ams/todo/createToDo",{ticket_id:e,to_do_list:t})}insertTodo(e,t,n){return this.$http.post("/api/ams/todo/insertToDo",{ticket_id:e,to_do_id:t,to_do_list:n})}editTodo(e,t,n,i){return this.$http.post("/api/ams/todo/editToDo",{ticket_id:e,to_do_id:t,to_do_details:n,change_type:i})}getTrDetails(e){return this.$http.post("/api/ams/tr/getTr",{trId:e})}createTr(e,t){return this.$http.post("/api/ams/tr/createTr",{ticket_id:e,tr_list:t})}insertTr(e,t,n){return this.$http.post("/api/ams/tr/insertTr",{ticket_id:e,tr_id:t,tr_list:n})}editTr(e,t,n){return this.$http.post("/api/ams/tr/editTr",{ticket_id:e,tr_id:t,tr_details:n})}editTicketPriority(e,t,n){return this.$http.post("/api/ams/ticket/editTicketPriority",{ticket_id:e,prev_priority:t,priority:n})}editTicketStatus(e,t,n,i,o,a){return this.$http.post("/api/ams/ticket/editTicketStatus",{ticket_id:e,status_id:i,prev_status:t,status_name:o,status_ref_id:n,wf_plugin_data:a})}editTicketType(e,t,n,i){return this.$http.post("/api/ams/ticket/editTicketType",{ticket_id:e,type_id:n,prev_type:t,type_name:i})}editTicketConsultants(e,t,n,i,o,a){return this.$http.post("/api/ams/ticket/editTicketConsultants",{ticket_id:e,employee_oid:n,employee_name:i,level_name:o,emp_ref_id:a,prev_oid:t})}getFlaggedTickets(e){return this.$http.post("/api/ams/configuration/getFlaggedTickets",{associateOId:e})}updateFlaggedTickets(e,t){return this.$http.post("/api/ams/configuration/updateFlaggedTickets",{associateOId:e,flaggedTickets:t})}getTicketsFilter(e,t,n,i){return this.$http.post("/api/ams/reports/getTicketsFilter",{status:e,filters:t,ticketList:n,searchParameter:i})}getStatusCount(e){return this.$http.post("/api/ams/reports/getStatusCount",{ticketList:e})}downloadTickets(e){return this.$http.post("/api/ams/reports/downloadTickets",{ticketList:e})}getSLAByTicketId(e){return this.$http.post("/api/ams/ticket/getSLAByTicketId",{ticketId:e})}getLocationList(){return this.$http.post("/api/tsPrimary/getLocationList",{})}updateTaskLocation(e,t,n,i,o,a){return this.$http.post("/api/ams/tasks/updateTaskLocation",{task_id:e,taskItem:t,old_loc:n,new_loc:i,value:o,ticket_id:a})}getSupportTeams(){return this.$http.post("/api/ams/configuration/getSupportTeams",{})}getReportedByList(e){return this.$http.post("/api/ams/configuration/getReportedByList",{project_item_id:e})}editTicketDetails(e,t,n,i){return this.$http.post("/api/ams/ticket/editTicketDetails",{ticket_id:e,key:t,value:n,prev_value:i})}getTicketsFilterSR(e,t){return this.$http.post("/api/ams/reports/getTicketsFilter",{filterConfig:e,ticketList:t})}updateNewWfPluginId(e,t,n){return this.$http.post("/api/ams/ticket/updateNewWfPluginId",{wf_plugin_data:t,ticket_id:e,status_object:n})}getActiveLabel(){return this.$http.post("/api/ams/ticket/getActiveLabelData",{})}getActivePriority(){return new Promise((e,t)=>{0==this.priorityArray.length?this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(t=>{this.priorityArray=t,e(this.priorityArray)}):e(this.priorityArray)})}getActivePriorityFromDB(){return Object(i.c)(this,void 0,void 0,(function*(){return this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(e=>(this.priorityArray=e,this.priorityArray))}))}getStatusColor(){return new Promise((e,t)=>{0==this.statusColorArray.length?this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(t=>{this.statusColorArray=t,e(this.statusColorArray)}):e(this.statusColorArray)})}getStatusColorFromDB(){return Object(i.c)(this,void 0,void 0,(function*(){this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(e=>(this.statusColorArray=e,this.statusColorArray))}))}checkAdminRole(){return this.$http.post("/api/ams/reports/checkForAdmin",{object_id:530,application_id:95})}getSupportTeamsForAdmin(){return this.$http.post("/api/ams/reports/getSupportTeamsForAdmin",{})}updateSupportTeamForAdmin(e){return this.$http.post("/api/ams/reports/updateSupportTeamForAdmin",{formData:e})}deleteSupportTeamForAdmin(e){return this.$http.post("/api/ams/reports/deleteSupportTeamForAdmin",{team_id:e})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](l.c),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](u.a),r["\u0275\u0275inject"](d.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);