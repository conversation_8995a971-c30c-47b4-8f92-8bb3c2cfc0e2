{"name": "new-web-application", "version": "0.0.901", "scripts": {"ng": "ng", "start-dev": "node --max_old_space_size=11111 ./node_modules/@angular/cli/bin/ng serve --port 4401 --watch=true  --proxy-config proxy.conf.json", "start-dev1": "node --max_old_space_size=5000 ./node_modules/@angular/cli/bin/ng serve --port 4403 --watch=true  --proxy-config proxy.conf.json", "start-okr": "node --max_old_space_size=3072 ./node_modules/@angular/cli/bin/ng serve --port 4401 --watch=true  --proxy-config okr-proxy.conf.json", "start": "ng serve", "build-ecs:zip": "npm --no-git-tag-version version patch && node --max-old-space-size=8000 ./node_modules/@angular/cli/bin/ng build --prod --build-optimizer --outputHashing=all && gzipper compress ./dist", "start-gp": "node --max_old_space_size=11111 ./node_modules/@angular/cli/bin/ng serve --port 4401 --watch=true  --proxy-config gp-proxy.conf.json --disable-host-check true", "build-prod-patch": "npm version patch && node ./replace.build.js && node --max_old_space_size=3024 ./node_modules/@angular/cli/bin/ng build --prod --aot --build-optimizer --base-href=./ && node ./versioning.build.js && gzipper compress ./dist", "build-prod-no-patch": "node ./replace.build.js && ng build --prod --base-href=./ && node ./versioning.build.js", "build-prd:zip": "npm --no-git-tag-version version patch && ng build --prod --build-optimizer --outputHashing=all && gzipper compress ./dist", "build-dev:zip": "npm --no-git-tag-version version patch && ng build --configuration=dev --build-optimizer --outputHashing=all && gzipper compress ./dist", "compodoc": "npx compodoc -p tsconfig.app.json src", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "build:stats": "ng build --prod --statsJson=true", "analyze": "webpack-bundle-analyzer dist/newWebApplication/stats.json"}, "private": true, "dependencies": {"1.0.2": "^1.0.0", "@amcharts/amcharts4": "^4.10.5", "@angular-slider/ngx-slider": "^2.0.4", "@angular/animations": "~11.0.0", "@angular/cdk": "^10.2.7", "@angular/common": "~11.0.0", "@angular/compiler": "~11.0.0", "@angular/core": "~11.0.0", "@angular/forms": "~11.0.0", "@angular/material": "^10.2.4", "@angular/material-moment-adapter": "^10.1.3", "@angular/platform-browser": "~11.0.0", "@angular/platform-browser-dynamic": "~11.0.0", "@angular/router": "~11.0.0", "@angular/service-worker": "~11.0.0", "@azure/msal-angular": "^2.0.2", "@azure/msal-browser": "^2.16.1", "@microsoft/microsoft-graph-client": "^2.0.0", "@ncstate/sat-popover": "^6.0.0", "@ngmodule/material-carousel": "^0.6.0", "@sentry/angular": "^7.52.1", "@sentry/node": "^7.52.1", "@teamhive/ngx-tooltip": "^0.3.15", "@types/gapi": "0.0.40", "@types/gapi.auth2": "0.0.55", "@types/intro.js": "^2.4.7", "@vime/angular": "^5.0.28", "accounting": "^0.4.1", "angular-2-local-storage": "^3.0.2", "angular-gridster2": "^11.2.0", "angular-mentions": "^1.2.0", "angular-shepherd": "^11.0.0", "angular2-virtual-scroll": "^0.4.16", "canvas-confetti": "^1.4.0", "clipboard": "^2.0.11", "clone": "^2.1.2", "colors": "^1.4.0", "country-state-city": "^1.0.5", "d3": "^7.8.5", "d3-org-chart": "^3.1.1", "devextreme": "^21.1.6", "devextreme-angular": "^21.1.6", "devextreme-aspnet-data-nojquery": "^2.9.3", "dhtmlx-gantt": "file:gantt_7.1.8_enterprise", "drag-drop-crop-resize": "0.0.4", "events": "^3.3.0", "exceljs": "^4.3.0", "file-saver": "^2.0.2", "fixed-filter-dialog": "0.0.2", "fuzzy-matching": "^0.4.3", "gojs": "^2.3.11", "gzipper": "^4.3.0", "html2pdf.js": "^0.9.2", "http-server": "^0.12.3", "input-search-d1": "0.0.2", "intro.js": "^3.0.1", "intuit-oauth": "^4.0.0", "is-unicode-supported": "^1.1.0", "iv-viewer": "^2.0.1", "jspdf": "^2.3.1", "jwt-decode": "^3.1.2", "kebs-attachments-tab": "0.0.1", "kebs-date-inline-edit-d1": "0.0.1", "kebs-help-application": "^15.0.0", "kebs-lms-otp-input": "0.0.1", "kebs-mul-sel-inf-search": "^0.2.5", "kebs-notes-tab": "0.0.7", "kebs-org-chart3": "0.0.5", "kebs-progress-circle": "^0.1.5", "kebs-select-search-d1": "0.0.4", "kebs-status-inline-tooltip": "0.0.4", "mail-box-modal": "^1.0.3", "mat-progress-buttons": "^9.1.1", "microsoft-adal-angular6": "^1.3.0", "milestone-slider-d1": "0.0.6", "moment": "^2.27.0", "moment-business-days": "^1.2.0", "moment-precise-range-plugin": "^1.3.0", "msal": "^1.4.13", "mul-sel-search": "0.0.7", "multi-text-inline-edit-d1": "0.0.2", "multilingual-number-to-words": "^5.0.3", "name-pop-up": "0.0.1", "ng2-file-upload": "^1.4.0", "ng2-img-cropper": "^0.9.0", "ng2-search-filter": "^0.5.1", "ng2-tooltip-directive": "2.9.22", "ngx-bar-rating": "^2.0.0", "ngx-chips": "^2.2.2", "ngx-content-loading": "^0.1.3", "ngx-daterangepicker-material": "^4.0.1", "ngx-doc-viewer": "^2.0.4", "ngx-editor": "^9.0.1", "ngx-filter-pipe": "^2.1.2", "ngx-image-cropper": "^3.3.5", "ngx-img-cropper": "^10.0.0", "ngx-infinite-scroll": "^9.1.0", "ngx-mat-select-search": "^3.2.0", "ngx-moment": "^5.0.0", "ngx-org-chart": "^1.1.1", "ngx-owl-carousel-o": "^4.0.0", "ngx-pagination": "^5.0.0", "ngx-quicklink": "^0.2.4", "ngx-spinner": "^10.0.1", "ngx-toastr": "^14.0.0", "ngx-ui-carousel": "^1.3.2", "ngx-virtual-scroller": "^4.0.3", "number-to-words": "^1.2.4", "otp-verify-d1": "0.0.1", "postcss-loader": "^6.2.0", "replace-in-file": "^6.2.0", "rxjs": "^6.6.3", "rxjs-compat": "^6.6.3", "subsink": "^1.0.1", "sweetalert": "^2.1.2", "sweetalert2": "^9.17.2", "text-inline-edit-d1": "0.0.3", "to-words": "^2.3.1", "tslib": "^2.0.3", "ui-carousel": "^0.2.0", "underscore": "^1.10.2", "uuid": "^8.3.2", "xlsx": "^0.16.6", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-builders/custom-webpack": "^10.0.1", "@angular-devkit/build-angular": "~0.1100.0", "@angular/cli": "^11.0.0", "@angular/compiler-cli": "~11.0.0", "@compodoc/compodoc": "^1.1.11", "@types/d3": "^7.4.3", "@types/d3-org-chart": "^3.1.2", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.5.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^6.0.0-next.1", "grunt": "^1.0.4", "grunt-contrib-htmlmin": "^3.1.0", "grunt-contrib-uglify-es": "^3.3.0", "grunt-prettify": "^0.4.0", "grunt-string-replace": "^1.3.1", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.1.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~3.3.0", "karma-jasmine-html-reporter": "^1.5.0", "moment-locales-webpack-plugin": "^1.2.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.0.5", "webpack-bundle-analyzer": "^3.9.0"}}