(window.webpackJsonp=window.webpackJsonp||[]).push([[158],{"2TRZ":function(t,e,n){"use strict";n.d(e,"a",(function(){return C})),n.d(e,"b",(function(){return p}));var r=n("xo8x"),i=n("tU+D"),o=Math.SQRT2;function a(t){return((t=Math.exp(t))+1/t)/2}var s=function(t,e){var n,r,i=t[0],s=t[1],l=t[2],u=e[2],c=e[0]-i,h=e[1]-s,d=c*c+h*h;if(d<1e-12)r=Math.log(u/l)/o,n=function(t){return[i+t*c,s+t*h,l*Math.exp(o*t*r)]};else{var f=Math.sqrt(d),p=(u*u-l*l+4*d)/(2*l*2*f),g=(u*u-l*l-4*d)/(2*u*2*f),m=Math.log(Math.sqrt(p*p+1)-p),y=Math.log(Math.sqrt(g*g+1)-g);r=(y-m)/o,n=function(t){var e,n=t*r,u=a(m),d=l/(2*f)*(u*(e=o*n+m,((e=Math.exp(2*e))-1)/(e+1))-function(t){return((t=Math.exp(t))-1/t)/2}(m));return[i+d*c,s+d*h,l*u/a(o*n+m)]}}return n.duration=1e3*r,n},l=n("HQnw"),u=n("REAR"),c=n("u/C0"),h=t=>()=>t;function d(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function f(t,e,n){this.k=t,this.x=e,this.y=n}f.prototype={constructor:f,scale:function(t){return 1===t?this:new f(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new f(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var p=new f(1,0,0);function g(t){t.stopImmediatePropagation()}var m=function(t){t.preventDefault(),t.stopImmediatePropagation()};function y(t){return!(t.ctrlKey&&"wheel"!==t.type||t.button)}function v(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function x(){return this.__zoom||p}function w(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function _(){return navigator.maxTouchPoints||"ontouchstart"in this}function b(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}var C=function(){var t,e,n,o=y,a=v,C=b,k=w,E=_,M=[0,1/0],$=[[-1/0,-1/0],[1/0,1/0]],S=250,B=s,N=Object(r.a)("start","zoom","end"),z=500,A=0,T=10;function O(t){t.property("__zoom",x).on("wheel.zoom",P,{passive:!1}).on("mousedown.zoom",D).on("dblclick.zoom",I).filter(E).on("touchstart.zoom",F).on("touchmove.zoom",q).on("touchend.zoom touchcancel.zoom",U).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function X(t,e){return(e=Math.max(M[0],Math.min(M[1],e)))===t.k?t:new f(e,t.x,t.y)}function R(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new f(t.k,r,i)}function L(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function j(t,e,n,r){t.on("start.zoom",(function(){H(this,arguments).event(r).start()})).on("interrupt.zoom end.zoom",(function(){H(this,arguments).event(r).end()})).tween("zoom",(function(){var t=this,i=arguments,o=H(t,i).event(r),s=a.apply(t,i),l=null==n?L(s):"function"==typeof n?n.apply(t,i):n,u=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),c=t.__zoom,h="function"==typeof e?e.apply(t,i):e,d=B(c.invert(l).concat(u/c.k),h.invert(l).concat(u/h.k));return function(t){if(1===t)t=h;else{var e=d(t),n=u/e[2];t=new f(n,l[0]-e[0]*n,l[1]-e[1]*n)}o.zoom(null,t)}}))}function H(t,e,n){return!n&&t.__zooming||new Y(t,e)}function Y(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=a.apply(t,e),this.taps=0}function P(t,...e){if(o.apply(this,arguments)){var n=H(this,e).event(t),r=this.__zoom,i=Math.max(M[0],Math.min(M[1],r.k*Math.pow(2,k.apply(this,arguments)))),a=Object(u.a)(t);if(n.wheel)n.mouse[0][0]===a[0]&&n.mouse[0][1]===a[1]||(n.mouse[1]=r.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(r.k===i)return;n.mouse=[a,r.invert(a)],Object(c.a)(this),n.start()}m(t),n.wheel=setTimeout(s,150),n.zoom("mouse",C(R(X(r,i),n.mouse[0],n.mouse[1]),n.extent,$))}function s(){n.wheel=null,n.end()}}function D(t,...e){if(!n&&o.apply(this,arguments)){var r=t.currentTarget,a=H(this,e,!0).event(t),s=Object(l.a)(t.view).on("mousemove.zoom",p,!0).on("mouseup.zoom",y,!0),h=Object(u.a)(t,r),d=t.clientX,f=t.clientY;Object(i.a)(t.view),g(t),a.mouse=[h,this.__zoom.invert(h)],Object(c.a)(this),a.start()}function p(t){if(m(t),!a.moved){var e=t.clientX-d,n=t.clientY-f;a.moved=e*e+n*n>A}a.event(t).zoom("mouse",C(R(a.that.__zoom,a.mouse[0]=Object(u.a)(t,r),a.mouse[1]),a.extent,$))}function y(t){s.on("mousemove.zoom mouseup.zoom",null),Object(i.b)(t.view,a.moved),m(t),a.event(t).end()}}function I(t,...e){if(o.apply(this,arguments)){var n=this.__zoom,r=Object(u.a)(t.changedTouches?t.changedTouches[0]:t,this),i=n.invert(r),s=n.k*(t.shiftKey?.5:2),c=C(R(X(n,s),r,i),a.apply(this,e),$);m(t),S>0?Object(l.a)(this).transition().duration(S).call(j,c,r,t):Object(l.a)(this).call(O.transform,c,r,t)}}function F(n,...r){if(o.apply(this,arguments)){var i,a,s,l,h=n.touches,d=h.length,f=H(this,r,n.changedTouches.length===d).event(n);for(g(n),a=0;a<d;++a)s=h[a],l=[l=Object(u.a)(s,this),this.__zoom.invert(l),s.identifier],f.touch0?f.touch1||f.touch0[2]===l[2]||(f.touch1=l,f.taps=0):(f.touch0=l,i=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(f.taps<2&&(e=l[0],t=setTimeout((function(){t=null}),z)),Object(c.a)(this),f.start())}}function q(t,...e){if(this.__zooming){var n,r,i,o,a=H(this,e).event(t),s=t.changedTouches,l=s.length;for(m(t),n=0;n<l;++n)r=s[n],i=Object(u.a)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],h=a.touch0[1],d=a.touch1[0],f=a.touch1[1],p=(p=d[0]-c[0])*p+(p=d[1]-c[1])*p,g=(g=f[0]-h[0])*g+(g=f[1]-h[1])*g;r=X(r,Math.sqrt(p/g)),i=[(c[0]+d[0])/2,(c[1]+d[1])/2],o=[(h[0]+f[0])/2,(h[1]+f[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",C(R(r,i,o),a.extent,$))}}function U(t,...r){if(this.__zooming){var i,o,a=H(this,r).event(t),s=t.changedTouches,c=s.length;for(g(t),n&&clearTimeout(n),n=setTimeout((function(){n=null}),z),i=0;i<c;++i)o=s[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=Object(u.a)(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<T)){var h=Object(l.a)(this).on("dblclick.zoom");h&&h.apply(this,arguments)}}}return O.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",x),t!==i?j(t,e,n,r):i.interrupt().each((function(){H(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()}))},O.scaleBy=function(t,e,n,r){O.scaleTo(t,(function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n}),n,r)},O.scaleTo=function(t,e,n,r){O.transform(t,(function(){var t=a.apply(this,arguments),r=this.__zoom,i=null==n?L(t):"function"==typeof n?n.apply(this,arguments):n,o=r.invert(i),s="function"==typeof e?e.apply(this,arguments):e;return C(R(X(r,s),i,o),t,$)}),n,r)},O.translateBy=function(t,e,n,r){O.transform(t,(function(){return C(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),a.apply(this,arguments),$)}),null,r)},O.translateTo=function(t,e,n,r,i){O.transform(t,(function(){var t=a.apply(this,arguments),i=this.__zoom,o=null==r?L(t):"function"==typeof r?r.apply(this,arguments):r;return C(p.translate(o[0],o[1]).scale(i.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,$)}),r,i)},Y.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=Object(l.a)(this.that).datum();N.call(t,this.that,new d(t,{sourceEvent:this.sourceEvent,target:O,type:t,transform:this.that.__zoom,dispatch:N}),e)}},O.wheelDelta=function(t){return arguments.length?(k="function"==typeof t?t:h(+t),O):k},O.filter=function(t){return arguments.length?(o="function"==typeof t?t:h(!!t),O):o},O.touchable=function(t){return arguments.length?(E="function"==typeof t?t:h(!!t),O):E},O.extent=function(t){return arguments.length?(a="function"==typeof t?t:h([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),O):a},O.scaleExtent=function(t){return arguments.length?(M[0]=+t[0],M[1]=+t[1],O):[M[0],M[1]]},O.translateExtent=function(t){return arguments.length?($[0][0]=+t[0][0],$[1][0]=+t[1][0],$[0][1]=+t[0][1],$[1][1]=+t[1][1],O):[[$[0][0],$[0][1]],[$[1][0],$[1][1]]]},O.constrain=function(t){return arguments.length?(C=t,O):C},O.duration=function(t){return arguments.length?(S=+t,O):S},O.interpolate=function(t){return arguments.length?(B=t,O):B},O.on=function(){var t=N.on.apply(N,arguments);return t===N?O:t},O.clickDistance=function(t){return arguments.length?(A=(t=+t)*t,O):Math.sqrt(A)},O.tapDistance=function(t){return arguments.length?(T=+t,O):T},O}},"5HU2":function(t,e,n){"use strict";n.d(e,"a",(function(){return V}));var r=n("XwrM"),i=n("HQnw"),o=n("UHDu"),a=n("NU5n"),s=n("2TRZ");function l(t){var e=0,n=t.children,r=n&&n.length;if(r)for(;--r>=0;)e+=n[r].value;else e=1;t.value=e}function u(t,e){var n,r,i,o,a,s=new f(t),l=+t.value&&(s.value=t.value),u=[s];for(null==e&&(e=c);n=u.pop();)if(l&&(n.value=+n.data.value),(i=e(n.data))&&(a=i.length))for(n.children=new Array(a),o=a-1;o>=0;--o)u.push(r=n.children[o]=new f(i[o])),r.parent=n,r.depth=n.depth+1;return s.eachBefore(d)}function c(t){return t.children}function h(t){t.data=t.data.data}function d(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function f(t){this.data=t,this.depth=this.height=0,this.parent=null}f.prototype=u.prototype={constructor:f,count:function(){return this.eachAfter(l)},each:function(t){var e,n,r,i,o=this,a=[o];do{for(e=a.reverse(),a=[];o=e.pop();)if(t(o),n=o.children)for(r=0,i=n.length;r<i;++r)a.push(n[r])}while(a.length);return this},eachAfter:function(t){for(var e,n,r,i=this,o=[i],a=[];i=o.pop();)if(a.push(i),e=i.children)for(n=0,r=e.length;n<r;++n)o.push(e[n]);for(;i=a.pop();)t(i);return this},eachBefore:function(t){for(var e,n,r=this,i=[r];r=i.pop();)if(t(r),e=r.children)for(n=e.length-1;n>=0;--n)i.push(e[n]);return this},sum:function(t){return this.eachAfter((function(e){for(var n=+t(e.data)||0,r=e.children,i=r&&r.length;--i>=0;)n+=r[i].value;e.value=n}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),r=e.ancestors(),i=null;for(t=n.pop(),e=r.pop();t===e;)i=t,t=n.pop(),e=r.pop();return i}(e,t),r=[e];e!==n;)r.push(e=e.parent);for(var i=r.length;t!==n;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){var t=[];return this.each((function(e){t.push(e)})),t},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(n){n!==t&&e.push({source:n.parent,target:n})})),e},copy:function(){return u(this).eachBefore(h)}};var p=n("Q9qd");const{version:g}=p,m=Object.freeze({children:t=>t.children,nodeSize:t=>t.data.size,spacing:0});function y(t){const e=Object.assign({},m,t);function n(t){const n=e[t];return"function"==typeof n?n:()=>n}function r(t){const e=o(function(){const t=i(),e=n("nodeSize"),r=n("spacing");return class extends t{constructor(t){super(t),Object.assign(this,{x:0,y:0,relX:0,prelim:0,shift:0,change:0,lExt:this,lExtRelX:0,lThr:null,rExt:this,rExtRelX:0,rThr:null})}get size(){return e(this.data)}spacing(t){return r(this.data,t.data)}get x(){return this.data.x}set x(t){this.data.x=t}get y(){return this.data.y}set y(t){this.data.y=t}update(){return v(this),x(this),this}}}(),t,t=>t.children);return e.update(),e.data}function i(){const t=n("nodeSize"),e=n("spacing");return class n extends u.prototype.constructor{constructor(t){super(t)}copy(){const t=o(this.constructor,this,t=>t.children);return t.each(t=>t.data=t.data.data),t}get size(){return t(this)}spacing(t){return e(this,t)}get nodes(){return this.descendants()}get xSize(){return this.size[0]}get ySize(){return this.size[1]}get top(){return this.y}get bottom(){return this.y+this.ySize}get left(){return this.x-this.xSize/2}get right(){return this.x+this.xSize/2}get root(){const t=this.ancestors();return t[t.length-1]}get numChildren(){return this.hasChildren?this.children.length:0}get hasChildren(){return!this.noChildren}get noChildren(){return null===this.children}get firstChild(){return this.hasChildren?this.children[0]:null}get lastChild(){return this.hasChildren?this.children[this.numChildren-1]:null}get extents(){return(this.children||[]).reduce((t,e)=>n.maxExtents(t,e.extents),this.nodeExtents)}get nodeExtents(){return{top:this.top,bottom:this.bottom,left:this.left,right:this.right}}static maxExtents(t,e){return{top:Math.min(t.top,e.top),bottom:Math.max(t.bottom,e.bottom),left:Math.min(t.left,e.left),right:Math.max(t.right,e.right)}}}}function o(t,e,n){const r=(e,i)=>{const o=new t(e);Object.assign(o,{parent:i,depth:null===i?0:i.depth+1,height:0,length:1});const a=n(e)||[];return o.children=0===a.length?null:a.map(t=>r(t,o)),o.children&&Object.assign(o,o.children.reduce((t,e)=>({height:Math.max(t.height,e.height+1),length:t.length+e.length}),o)),o};return r(e,null)}return Object.assign(r,{nodeSize(t){return arguments.length?(e.nodeSize=t,r):e.nodeSize},spacing(t){return arguments.length?(e.spacing=t,r):e.spacing},children(t){return arguments.length?(e.children=t,r):e.children},hierarchy(t,n){const r=void 0===n?e.children:n;return o(i(),t,r)},dump(t){const e=n("nodeSize"),r=t=>n=>{const i=t+"  ",o=t+"    ",{x:a,y:s}=n,l=e(n),u=n.children||[],c=0===u.length?" ":`,${i}children: [${o}${u.map(r(o)).join(o)}${i}],${t}`;return`{ size: [${l.join(", ")}],${i}x: ${a}, y: ${s}${c}},`};return r("\n")(t)}}),r}y.version=g;const v=(t,e=0)=>(t.y=e,(t.children||[]).reduce((e,n)=>{const[r,i]=e;v(n,t.y+t.ySize);const o=(0===r?n.lExt:n.rExt).bottom;return 0!==r&&_(t,r,i),[r+1,B(o,r,i)]},[0,null]),w(t),S(t),t),x=(t,e,n)=>{void 0===e&&(e=-t.relX-t.prelim,n=0);const r=e+t.relX;return t.relX=r+t.prelim-n,t.prelim=0,t.x=n+t.relX,(t.children||[]).forEach(e=>x(e,r,t.x)),t},w=t=>{(t.children||[]).reduce((t,e)=>{const[n,r]=t,i=n+e.shift,o=r+i+e.change;return e.relX+=o,[i,o]},[0,0])},_=(t,e,n)=>{const r=t.children[e-1],i=t.children[e];let o=r,a=r.relX,s=i,l=i.relX,u=!0;for(;o&&s;){o.bottom>n.lowY&&(n=n.next);const r=a+o.prelim-(l+s.prelim)+o.xSize/2+s.xSize/2+o.spacing(s);(r>0||r<0&&u)&&(l+=r,b(i,r),C(t,e,n.index,r)),u=!1;const c=o.bottom,h=s.bottom;c<=h&&(o=E(o),o&&(a+=o.relX)),c>=h&&(s=k(s),s&&(l+=s.relX))}!o&&s?M(t,e,s,l):o&&!s&&$(t,e,o,a)},b=(t,e)=>{t.relX+=e,t.lExtRelX+=e,t.rExtRelX+=e},C=(t,e,n,r)=>{const i=t.children[e],o=e-n;if(o>1){const e=r/o;t.children[n+1].shift+=e,i.shift-=e,i.change-=r-e}},k=t=>t.hasChildren?t.firstChild:t.lThr,E=t=>t.hasChildren?t.lastChild:t.rThr,M=(t,e,n,r)=>{const i=t.firstChild,o=i.lExt,a=t.children[e];o.lThr=n;const s=r-n.relX-i.lExtRelX;o.relX+=s,o.prelim-=s,i.lExt=a.lExt,i.lExtRelX=a.lExtRelX},$=(t,e,n,r)=>{const i=t.children[e],o=i.rExt,a=t.children[e-1];o.rThr=n;const s=r-n.relX-i.rExtRelX;o.relX+=s,o.prelim-=s,i.rExt=a.rExt,i.rExtRelX=a.rExtRelX},S=t=>{if(t.hasChildren){const e=t.firstChild,n=t.lastChild;Object.assign(t,{prelim:(e.prelim+e.relX-e.xSize/2+n.relX+n.prelim+n.xSize/2)/2,lExt:e.lExt,lExtRelX:e.lExtRelX,rExt:n.rExt,rExtRelX:n.rExtRelX})}},B=(t,e,n)=>{for(;null!==n&&t>=n.lowY;)n=n.next;return{lowY:t,index:e,next:n}};var N=Array.prototype.slice,z=function(t){return function(){return t}};class A{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function T(t){return new A(t,!0)}const O=Math.PI,X=2*O,R=1e-6,L=X-R;function j(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}class H{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?j:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error("invalid digits: "+t);if(e>15)return j;const n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,i,o){this._append`C${+t},${+e},${+n},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,n,r,i){if(t=+t,e=+e,n=+n,r=+r,(i=+i)<0)throw new Error("negative radius: "+i);let o=this._x1,a=this._y1,s=n-t,l=r-e,u=o-t,c=a-e,h=u*u+c*c;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>R)if(Math.abs(c*s-l*u)>R&&i){let d=n-o,f=r-a,p=s*s+l*l,g=d*d+f*f,m=Math.sqrt(p),y=Math.sqrt(h),v=i*Math.tan((O-Math.acos((p+h-g)/(2*m*y)))/2),x=v/y,w=v/m;Math.abs(x-1)>R&&this._append`L${t+x*u},${e+x*c}`,this._append`A${i},${i},0,0,${+(c*d>u*f)},${this._x1=t+w*s},${this._y1=e+w*l}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,n,r,i,o){if(t=+t,e=+e,o=!!o,(n=+n)<0)throw new Error("negative radius: "+n);let a=n*Math.cos(r),s=n*Math.sin(r),l=t+a,u=e+s,c=1^o,h=o?r-i:i-r;null===this._x1?this._append`M${l},${u}`:(Math.abs(this._x1-l)>R||Math.abs(this._y1-u)>R)&&this._append`L${l},${u}`,n&&(h<0&&(h=h%X+X),h>L?this._append`A${n},${n},0,1,${c},${t-a},${e-s}A${n},${n},0,1,${c},${this._x1=l},${this._y1=u}`:h>R&&this._append`A${n},${n},0,${+(h>=O)},${c},${this._x1=t+n*Math.cos(i)},${this._y1=e+n*Math.sin(i)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function Y(t){return t[0]}function P(t){return t[1]}function D(t){return t.source}function I(t){return t.target}const F={selection:r.b,select:i.a,max:function(t,e){let n;if(void 0===e)for(const r of t)null!=r&&(n<r||void 0===n&&r>=r)&&(n=r);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n<i||void 0===n&&i>=i)&&(n=i)}return n},min:function(t,e){let n;if(void 0===e)for(const r of t)null!=r&&(n>r||void 0===n&&r>=r)&&(n=r);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n>i||void 0===n&&i>=i)&&(n=i)}return n},sum:function(t,e){let n=0;if(void 0===e)for(let r of t)(r=+r)&&(n+=r);else{let r=-1;for(let i of t)(i=+e(i,++r,t))&&(n+=i)}return n},cumsum:function(t,e){var n=0,r=0;return Float64Array.from(t,void 0===e?t=>n+=+t||0:i=>n+=+e(i,r++,t)||0)},tree:o.a,stratify:a.a,zoom:s.a,zoomIdentity:s.b,linkHorizontal:function(){return function(t){let e=D,n=I,r=Y,i=P,o=null,a=null,s=function(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(null==n)e=null;else{const t=Math.floor(n);if(!(t>=0))throw new RangeError("invalid digits: "+n);e=t}return t},()=>new H(e)}(l);function l(){let l;const u=N.call(arguments),c=e.apply(this,u),h=n.apply(this,u);if(null==o&&(a=t(l=s())),a.lineStart(),u[0]=c,a.point(+r.apply(this,u),+i.apply(this,u)),u[0]=h,a.point(+r.apply(this,u),+i.apply(this,u)),a.lineEnd(),l)return a=null,l+""||null}return l.source=function(t){return arguments.length?(e=t,l):e},l.target=function(t){return arguments.length?(n=t,l):n},l.x=function(t){return arguments.length?(r="function"==typeof t?t:z(+t),l):r},l.y=function(t){return arguments.length?(i="function"==typeof t?t:z(+t),l):i},l.context=function(e){return arguments.length?(null==e?o=a=null:a=t(o=e),l):o},l}(T)},flextree:y};class q{constructor(){const t={id:"ID"+Math.floor(1e6*Math.random()),firstDraw:!0,ctx:document.createElement("canvas").getContext("2d"),initialExpandLevel:1,nodeDefaultBackground:"none",lastTransform:{x:0,y:0,k:1},allowedNodesCount:{},zoomBehavior:null,generateRoot:null,svgWidth:800,svgHeight:window.innerHeight-100,container:"body",data:null,connections:[],defaultFont:"Helvetica",nodeId:t=>t.nodeId||t.id,parentNodeId:t=>t.parentNodeId||t.parentId,rootMargin:40,nodeWidth:t=>250,nodeHeight:t=>150,neighbourMargin:(t,e)=>80,siblingsMargin:t=>20,childrenMargin:t=>60,compactMarginPair:t=>100,compactMarginBetween:t=>20,nodeButtonWidth:t=>40,nodeButtonHeight:t=>40,nodeButtonX:t=>-20,nodeButtonY:t=>-20,linkYOffset:30,pagingStep:t=>5,minPagingVisibleNodes:t=>2e3,scaleExtent:[.001,20],duration:400,imageName:"Chart",setActiveNodeCentered:!0,layout:"top",compact:!0,createZoom:t=>F.zoom(),onZoomStart:t=>{},onZoom:t=>{},onZoomEnd:t=>{},onNodeClick:t=>t,onExpandOrCollapse:t=>t,nodeContent:t=>`<div style="padding:5px;font-size:10px;">Sample Node(id=${t.id}), override using <br/> \n            <code>chart.nodeContent({data}=>{ <br/>\n             &nbsp;&nbsp;&nbsp;&nbsp;return '' // Custom HTML <br/>\n             })</code>\n             <br/> \n             Or check different <a href="https://github.com/bumbeishvili/org-chart#jump-to-examples" target="_blank">layout examples</a>\n             </div>`,buttonContent:({node:t,state:e})=>`<div style="border:1px solid #E4E2E9;border-radius:3px;padding:3px;font-size:9px;margin:auto auto;background-color:white"> ${{left:e=>e?`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                      <path d="M14.283 3.50094L6.51 11.4749C6.37348 11.615 6.29707 11.8029 6.29707 11.9984C6.29707 12.194 6.37348 12.3819 6.51 12.5219L14.283 20.4989C14.3466 20.5643 14.4226 20.6162 14.5066 20.6516C14.5906 20.6871 14.6808 20.7053 14.772 20.7053C14.8632 20.7053 14.9534 20.6871 15.0374 20.6516C15.1214 20.6162 15.1974 20.5643 15.261 20.4989C15.3918 20.365 15.4651 20.1852 15.4651 19.9979C15.4651 19.8107 15.3918 19.6309 15.261 19.4969L7.9515 11.9984L15.261 4.50144C15.3914 4.36756 15.4643 4.18807 15.4643 4.00119C15.4643 3.81431 15.3914 3.63482 15.261 3.50094C15.1974 3.43563 15.1214 3.38371 15.0374 3.34827C14.9534 3.31282 14.8632 3.29456 14.772 3.29456C14.6808 3.29456 14.5906 3.31282 14.5066 3.34827C14.4226 3.38371 14.3466 3.43563 14.283 3.50094V3.50094Z" fill="#716E7B" stroke="#716E7B"/>\n                      </svg></span><span style="color:#716E7B">${t.data._directSubordinatesPaging} </span></div>`:`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                          <path d="M7.989 3.49944C7.85817 3.63339 7.78492 3.8132 7.78492 4.00044C7.78492 4.18768 7.85817 4.36749 7.989 4.50144L15.2985 11.9999L7.989 19.4969C7.85817 19.6309 7.78492 19.8107 7.78492 19.9979C7.78492 20.1852 7.85817 20.365 7.989 20.4989C8.05259 20.5643 8.12863 20.6162 8.21261 20.6516C8.2966 20.6871 8.38684 20.7053 8.478 20.7053C8.56916 20.7053 8.6594 20.6871 8.74338 20.6516C8.82737 20.6162 8.90341 20.5643 8.967 20.4989L16.74 12.5234C16.8765 12.3834 16.9529 12.1955 16.9529 11.9999C16.9529 11.8044 16.8765 11.6165 16.74 11.4764L8.967 3.50094C8.90341 3.43563 8.82737 3.38371 8.74338 3.34827C8.6594 3.31282 8.56916 3.29456 8.478 3.29456C8.38684 3.29456 8.2966 3.31282 8.21261 3.34827C8.12863 3.38371 8.05259 3.43563 7.989 3.50094V3.49944Z" fill="#716E7B" stroke="#716E7B"/>\n                          </svg></span><span style="color:#716E7B">${t.data._directSubordinatesPaging} </span></div>`,bottom:e=>e?`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                       <path d="M19.497 7.98903L12 15.297L4.503 7.98903C4.36905 7.85819 4.18924 7.78495 4.002 7.78495C3.81476 7.78495 3.63495 7.85819 3.501 7.98903C3.43614 8.05257 3.38462 8.12842 3.34944 8.21213C3.31427 8.29584 3.29615 8.38573 3.29615 8.47653C3.29615 8.56733 3.31427 8.65721 3.34944 8.74092C3.38462 8.82463 3.43614 8.90048 3.501 8.96403L11.4765 16.74C11.6166 16.8765 11.8044 16.953 12 16.953C12.1956 16.953 12.3834 16.8765 12.5235 16.74L20.499 8.96553C20.5643 8.90193 20.6162 8.8259 20.6517 8.74191C20.6871 8.65792 20.7054 8.56769 20.7054 8.47653C20.7054 8.38537 20.6871 8.29513 20.6517 8.21114C20.6162 8.12715 20.5643 8.05112 20.499 7.98753C20.3651 7.85669 20.1852 7.78345 19.998 7.78345C19.8108 7.78345 19.6309 7.85669 19.497 7.98753V7.98903Z" fill="#716E7B" stroke="#716E7B"/>\n                       </svg></span><span style="margin-left:1px;color:#716E7B" >${t.data._directSubordinatesPaging} </span></div>\n                       `:`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                       <path d="M11.457 8.07005L3.49199 16.4296C3.35903 16.569 3.28485 16.7543 3.28485 16.9471C3.28485 17.1398 3.35903 17.3251 3.49199 17.4646L3.50099 17.4736C3.56545 17.5414 3.64304 17.5954 3.72904 17.6324C3.81504 17.6693 3.90765 17.6883 4.00124 17.6883C4.09483 17.6883 4.18745 17.6693 4.27344 17.6324C4.35944 17.5954 4.43703 17.5414 4.50149 17.4736L12.0015 9.60155L19.4985 17.4736C19.563 17.5414 19.6405 17.5954 19.7265 17.6324C19.8125 17.6693 19.9052 17.6883 19.9987 17.6883C20.0923 17.6883 20.1849 17.6693 20.2709 17.6324C20.3569 17.5954 20.4345 17.5414 20.499 17.4736L20.508 17.4646C20.641 17.3251 20.7151 17.1398 20.7151 16.9471C20.7151 16.7543 20.641 16.569 20.508 16.4296L12.543 8.07005C12.4729 7.99653 12.3887 7.93801 12.2954 7.89801C12.202 7.85802 12.1015 7.8374 12 7.8374C11.8984 7.8374 11.798 7.85802 11.7046 7.89801C11.6113 7.93801 11.527 7.99653 11.457 8.07005Z" fill="#716E7B" stroke="#716E7B"/>\n                       </svg></span><span style="margin-left:1px;color:#716E7B" >${t.data._directSubordinatesPaging} </span></div>\n                    `,right:e=>e?`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                       <path d="M7.989 3.49944C7.85817 3.63339 7.78492 3.8132 7.78492 4.00044C7.78492 4.18768 7.85817 4.36749 7.989 4.50144L15.2985 11.9999L7.989 19.4969C7.85817 19.6309 7.78492 19.8107 7.78492 19.9979C7.78492 20.1852 7.85817 20.365 7.989 20.4989C8.05259 20.5643 8.12863 20.6162 8.21261 20.6516C8.2966 20.6871 8.38684 20.7053 8.478 20.7053C8.56916 20.7053 8.6594 20.6871 8.74338 20.6516C8.82737 20.6162 8.90341 20.5643 8.967 20.4989L16.74 12.5234C16.8765 12.3834 16.9529 12.1955 16.9529 11.9999C16.9529 11.8044 16.8765 11.6165 16.74 11.4764L8.967 3.50094C8.90341 3.43563 8.82737 3.38371 8.74338 3.34827C8.6594 3.31282 8.56916 3.29456 8.478 3.29456C8.38684 3.29456 8.2966 3.31282 8.21261 3.34827C8.12863 3.38371 8.05259 3.43563 7.989 3.50094V3.49944Z" fill="#716E7B" stroke="#716E7B"/>\n                       </svg></span><span style="color:#716E7B">${t.data._directSubordinatesPaging} </span></div>`:`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                       <path d="M14.283 3.50094L6.51 11.4749C6.37348 11.615 6.29707 11.8029 6.29707 11.9984C6.29707 12.194 6.37348 12.3819 6.51 12.5219L14.283 20.4989C14.3466 20.5643 14.4226 20.6162 14.5066 20.6516C14.5906 20.6871 14.6808 20.7053 14.772 20.7053C14.8632 20.7053 14.9534 20.6871 15.0374 20.6516C15.1214 20.6162 15.1974 20.5643 15.261 20.4989C15.3918 20.365 15.4651 20.1852 15.4651 19.9979C15.4651 19.8107 15.3918 19.6309 15.261 19.4969L7.9515 11.9984L15.261 4.50144C15.3914 4.36756 15.4643 4.18807 15.4643 4.00119C15.4643 3.81431 15.3914 3.63482 15.261 3.50094C15.1974 3.43563 15.1214 3.38371 15.0374 3.34827C14.9534 3.31282 14.8632 3.29456 14.772 3.29456C14.6808 3.29456 14.5906 3.31282 14.5066 3.34827C14.4226 3.38371 14.3466 3.43563 14.283 3.50094V3.50094Z" fill="#716E7B" stroke="#716E7B"/>\n                       </svg></span><span style="color:#716E7B">${t.data._directSubordinatesPaging} </span></div>`,top:e=>e?`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                        <path d="M11.457 8.07005L3.49199 16.4296C3.35903 16.569 3.28485 16.7543 3.28485 16.9471C3.28485 17.1398 3.35903 17.3251 3.49199 17.4646L3.50099 17.4736C3.56545 17.5414 3.64304 17.5954 3.72904 17.6324C3.81504 17.6693 3.90765 17.6883 4.00124 17.6883C4.09483 17.6883 4.18745 17.6693 4.27344 17.6324C4.35944 17.5954 4.43703 17.5414 4.50149 17.4736L12.0015 9.60155L19.4985 17.4736C19.563 17.5414 19.6405 17.5954 19.7265 17.6324C19.8125 17.6693 19.9052 17.6883 19.9987 17.6883C20.0923 17.6883 20.1849 17.6693 20.2709 17.6324C20.3569 17.5954 20.4345 17.5414 20.499 17.4736L20.508 17.4646C20.641 17.3251 20.7151 17.1398 20.7151 16.9471C20.7151 16.7543 20.641 16.569 20.508 16.4296L12.543 8.07005C12.4729 7.99653 12.3887 7.93801 12.2954 7.89801C12.202 7.85802 12.1015 7.8374 12 7.8374C11.8984 7.8374 11.798 7.85802 11.7046 7.89801C11.6113 7.93801 11.527 7.99653 11.457 8.07005Z" fill="#716E7B" stroke="#716E7B"/>\n                        </svg></span><span style="margin-left:1px;color:#716E7B">${t.data._directSubordinatesPaging} </span></div>\n                        `:`<div style="display:flex;"><span style="align-items:center;display:flex;"><svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                        <path d="M19.497 7.98903L12 15.297L4.503 7.98903C4.36905 7.85819 4.18924 7.78495 4.002 7.78495C3.81476 7.78495 3.63495 7.85819 3.501 7.98903C3.43614 8.05257 3.38462 8.12842 3.34944 8.21213C3.31427 8.29584 3.29615 8.38573 3.29615 8.47653C3.29615 8.56733 3.31427 8.65721 3.34944 8.74092C3.38462 8.82463 3.43614 8.90048 3.501 8.96403L11.4765 16.74C11.6166 16.8765 11.8044 16.953 12 16.953C12.1956 16.953 12.3834 16.8765 12.5235 16.74L20.499 8.96553C20.5643 8.90193 20.6162 8.8259 20.6517 8.74191C20.6871 8.65792 20.7054 8.56769 20.7054 8.47653C20.7054 8.38537 20.6871 8.29513 20.6517 8.21114C20.6162 8.12715 20.5643 8.05112 20.499 7.98753C20.3651 7.85669 20.1852 7.78345 19.998 7.78345C19.8108 7.78345 19.6309 7.85669 19.497 7.98753V7.98903Z" fill="#716E7B" stroke="#716E7B"/>\n                        </svg></span><span style="margin-left:1px;color:#716E7B">${t.data._directSubordinatesPaging} </span></div>\n                    `}[e.layout](t.children)}  </div>`,pagingButton:(t,e,n,r)=>{const i=r.pagingStep(t.parent);return`\n                   <div style="margin-top:90px;">\n                      <div style="display:flex;width:170px;border-radius:20px;padding:5px 15px; padding-bottom:4px;;background-color:#E5E9F2">\n                      <div><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                      <path d="M5.59 7.41L10.18 12L5.59 16.59L7 18L13 12L7 6L5.59 7.41ZM16 6H18V18H16V6Z" fill="#716E7B" stroke="#716E7B"/>\n                      </svg>\n                      </div><div style="line-height:2"> Show next ${Math.min(t.parent.data._directSubordinatesPaging-t.parent.data._pagingStep,i)}  nodes </div></div>\n                   </div>\n                `},nodeUpdate:function(t,e,n){F.select(this).select(".node-rect").attr("stroke",t=>t.data._highlighted||t.data._upToTheRootHighlighted?"#E27396":"none").attr("stroke-width",t.data._highlighted||t.data._upToTheRootHighlighted?10:1)},nodeEnter:t=>t,nodeExit:t=>t,linkUpdate:function(t,e,n){F.select(this).attr("stroke",t=>t.data._upToTheRootHighlighted?"#E27396":"#E4E2E9").attr("stroke-width",t=>t.data._upToTheRootHighlighted?5:1),t.data._upToTheRootHighlighted&&F.select(this).raise()},hdiagonal:function(t,e,n){const r=t.x,i=t.y,o=e.x,a=e.y;let s=n&&null!=n.x?n.x:r,l=n&&null!=n.y?n.y:i,u=o-r<0?-1:1,c=a-i<0?-1:1,h=Math.abs(o-r)/2<35?Math.abs(o-r)/2:35;h=Math.abs(a-i)/2<h?Math.abs(a-i)/2:h,Math.abs(a-i);let d=Math.abs(o-r)/2-h;return`\n                          M ${s} ${l}\n                          L ${s} ${i}\n                          L ${r} ${i}\n                          L ${r+d*u} ${i}\n                          C ${r+d*u+h*u} ${i} \n                            ${r+d*u+h*u} ${i} \n                            ${r+d*u+h*u} ${i+h*c}\n                          L ${r+d*u+h*u} ${a-h*c} \n                          C ${r+d*u+h*u}  ${a} \n                            ${r+d*u+h*u}  ${a} \n                            ${o-d*u}  ${a}\n                          L ${o} ${a}\n               `},diagonal:function(t,e,n,r={sy:0}){const i=t.x;let o=t.y;const a=e.x,s=e.y;let l=n&&null!=n.x?n.x:i,u=n&&null!=n.y?n.y:o,c=a-i<0?-1:1,h=s-o<0?-1:1;o+=r.sy;let d=Math.abs(a-i)/2<35?Math.abs(a-i)/2:35;d=Math.abs(s-o)/2<d?Math.abs(s-o)/2:d;let f=Math.abs(s-o)/2-d;return`\n                          M ${l} ${u}\n                          L ${i} ${u}\n                          L ${i} ${o}\n                          L ${i} ${o+f*h}\n                          C  ${i} ${o+f*h+d*h} ${i} ${o+f*h+d*h} ${i+d*c} ${o+f*h+d*h}\n                          L ${i+(Math.abs(a-i)-2*d)*c+d*c} ${o+f*h+d*h}\n                          C  ${a}  ${o+f*h+d*h} ${a}  ${o+f*h+d*h} ${a} ${s-f*h}\n                          L ${a} ${s}\n               `},defs:function(t,e){return`<defs>\n                    ${e.map(e=>{const n=this.getTextWidth(e.label,{ctx:t.ctx,fontSize:2,defaultFont:t.defaultFont});return`\n                       <marker id="${e.from+"_"+e.to}" refX="${e._source.x<e._target.x?-7:7}" refY="5" markerWidth="500"  markerHeight="500"  orient="${e._source.x<e._target.x?"auto":"auto-start-reverse"}" >\n                       <rect rx=0.5 width=${e.label?n+3:0} height=3 y=1  fill="#E27396"></rect>\n                       <text font-size="2px" x=1 fill="white" y=3>${e.label||""}</text>\n                       </marker>\n\n                       <marker id="arrow-${e.from+"_"+e.to}"  markerWidth="500"  markerHeight="500"  refY="2"  refX="1" orient="${e._source.x<e._target.x?"auto":"auto-start-reverse"}" >\n                       <path transform="translate(0)" d='M0,0 V4 L2,2 Z' fill='#E27396' />\n                       </marker>\n                    `}).join("")}\n                    </defs>\n                    `},connectionsUpdate:function(t,e,n){F.select(this).attr("stroke",t=>"#E27396").attr("stroke-linecap","round").attr("stroke-width",t=>"5").attr("pointer-events","none").attr("marker-start",t=>`url(#${t.from+"_"+t.to})`).attr("marker-end",t=>`url(#arrow-${t.from+"_"+t.to})`)},linkGroupArc:F.linkHorizontal().x(t=>t.x).y(t=>t.y),layoutBindings:{left:{nodeLeftX:t=>0,nodeRightX:t=>t.width,nodeTopY:t=>-t.height/2,nodeBottomY:t=>t.height/2,nodeJoinX:t=>t.x+t.width,nodeJoinY:t=>t.y-t.height/2,linkJoinX:t=>t.x+t.width,linkJoinY:t=>t.y,linkX:t=>t.x,linkY:t=>t.y,linkCompactXStart:t=>t.x+t.width/2,linkCompactYStart:t=>t.y+(t.compactEven?t.height/2:-t.height/2),compactLinkMidX:(t,e)=>t.firstCompactNode.x,compactLinkMidY:(t,e)=>t.firstCompactNode.y+t.firstCompactNode.flexCompactDim[0]/4+e.compactMarginPair(t)/4,linkParentX:t=>t.parent.x+t.parent.width,linkParentY:t=>t.parent.y,buttonX:t=>t.width,buttonY:t=>t.height/2,centerTransform:({rootMargin:t,centerY:e,scale:n})=>`translate(${t},${e}) scale(${n})`,compactDimension:{sizeColumn:t=>t.height,sizeRow:t=>t.width,reverse:t=>t.slice().reverse()},nodeFlexSize:({height:t,width:e,siblingsMargin:n,childrenMargin:r,state:i,node:o})=>i.compact&&o.flexCompactDim?[o.flexCompactDim[0],o.flexCompactDim[1]]:[t+n,e+r],zoomTransform:({centerY:t,scale:e})=>`translate(0,${t}) scale(${e})`,diagonal:this.hdiagonal.bind(this),swap:t=>{const e=t.x;t.x=t.y,t.y=e},nodeUpdateTransform:({x:t,y:e,height:n})=>`translate(${t},${e-n/2})`},top:{nodeLeftX:t=>-t.width/2,nodeRightX:t=>t.width/2,nodeTopY:t=>0,nodeBottomY:t=>t.height,nodeJoinX:t=>t.x-t.width/2,nodeJoinY:t=>t.y+t.height,linkJoinX:t=>t.x,linkJoinY:t=>t.y+t.height,linkCompactXStart:t=>t.x+(t.compactEven?t.width/2:-t.width/2),linkCompactYStart:t=>t.y+t.height/2,compactLinkMidX:(t,e)=>t.firstCompactNode.x+t.firstCompactNode.flexCompactDim[0]/4+e.compactMarginPair(t)/4,compactLinkMidY:t=>t.firstCompactNode.y,compactDimension:{sizeColumn:t=>t.width,sizeRow:t=>t.height,reverse:t=>t},linkX:t=>t.x,linkY:t=>t.y,linkParentX:t=>t.parent.x,linkParentY:t=>t.parent.y+t.parent.height,buttonX:t=>t.width/2,buttonY:t=>t.height,centerTransform:({rootMargin:t,scale:e,centerX:n})=>`translate(${n},${t}) scale(${e})`,nodeFlexSize:({height:t,width:e,siblingsMargin:n,childrenMargin:r,state:i,node:o})=>i.compact&&o.flexCompactDim?[o.flexCompactDim[0],o.flexCompactDim[1]]:[e+n,t+r],zoomTransform:({centerX:t,scale:e})=>`translate(${t},0}) scale(${e})`,diagonal:this.diagonal.bind(this),swap:t=>{},nodeUpdateTransform:({x:t,y:e,width:n})=>`translate(${t-n/2},${e})`},bottom:{nodeLeftX:t=>-t.width/2,nodeRightX:t=>t.width/2,nodeTopY:t=>-t.height,nodeBottomY:t=>0,nodeJoinX:t=>t.x-t.width/2,nodeJoinY:t=>t.y-t.height-t.height,linkJoinX:t=>t.x,linkJoinY:t=>t.y-t.height,linkCompactXStart:t=>t.x+(t.compactEven?t.width/2:-t.width/2),linkCompactYStart:t=>t.y-t.height/2,compactLinkMidX:(t,e)=>t.firstCompactNode.x+t.firstCompactNode.flexCompactDim[0]/4+e.compactMarginPair(t)/4,compactLinkMidY:t=>t.firstCompactNode.y,linkX:t=>t.x,linkY:t=>t.y,compactDimension:{sizeColumn:t=>t.width,sizeRow:t=>t.height,reverse:t=>t},linkParentX:t=>t.parent.x,linkParentY:t=>t.parent.y-t.parent.height,buttonX:t=>t.width/2,buttonY:t=>0,centerTransform:({rootMargin:t,scale:e,centerX:n,chartHeight:r})=>`translate(${n},${r-t}) scale(${e})`,nodeFlexSize:({height:t,width:e,siblingsMargin:n,childrenMargin:r,state:i,node:o})=>i.compact&&o.flexCompactDim?[o.flexCompactDim[0],o.flexCompactDim[1]]:[e+n,t+r],zoomTransform:({centerX:t,scale:e})=>`translate(${t},0}) scale(${e})`,diagonal:this.diagonal.bind(this),swap:t=>{t.y=-t.y},nodeUpdateTransform:({x:t,y:e,width:n,height:r})=>`translate(${t-n/2},${e-r})`},right:{nodeLeftX:t=>-t.width,nodeRightX:t=>0,nodeTopY:t=>-t.height/2,nodeBottomY:t=>t.height/2,nodeJoinX:t=>t.x-t.width-t.width,nodeJoinY:t=>t.y-t.height/2,linkJoinX:t=>t.x-t.width,linkJoinY:t=>t.y,linkX:t=>t.x,linkY:t=>t.y,linkParentX:t=>t.parent.x-t.parent.width,linkParentY:t=>t.parent.y,buttonX:t=>0,buttonY:t=>t.height/2,linkCompactXStart:t=>t.x-t.width/2,linkCompactYStart:t=>t.y+(t.compactEven?t.height/2:-t.height/2),compactLinkMidX:(t,e)=>t.firstCompactNode.x,compactLinkMidY:(t,e)=>t.firstCompactNode.y+t.firstCompactNode.flexCompactDim[0]/4+e.compactMarginPair(t)/4,centerTransform:({rootMargin:t,centerY:e,scale:n,chartWidth:r})=>`translate(${r-t},${e}) scale(${n})`,nodeFlexSize:({height:t,width:e,siblingsMargin:n,childrenMargin:r,state:i,node:o})=>i.compact&&o.flexCompactDim?[o.flexCompactDim[0],o.flexCompactDim[1]]:[t+n,e+r],compactDimension:{sizeColumn:t=>t.height,sizeRow:t=>t.width,reverse:t=>t.slice().reverse()},zoomTransform:({centerY:t,scale:e})=>`translate(0,${t}) scale(${e})`,diagonal:this.hdiagonal.bind(this),swap:t=>{const e=t.x;t.x=-t.y,t.y=e},nodeUpdateTransform:({x:t,y:e,width:n,height:r})=>`translate(${t-n},${e-r/2})`}}};this.getChartState=()=>t,Object.keys(t).forEach(e=>{this[e]=function(n){return arguments.length?(t[e]=n,this):t[e]}}),this.initializeEnterExitUpdatePattern()}initializeEnterExitUpdatePattern(){F.selection.prototype.patternify=function(t){var e=t.selector,n=t.tag,r=t.data||[e],i=this.selectAll("."+e).data(r,(t,e)=>"object"==typeof t&&t.id?t.id:e);return i.exit().remove(),(i=i.enter().append(n).merge(i)).attr("class",e),i}}getNodeChildren({data:t,children:e,_children:n},r){return r.push(t),e&&e.forEach(t=>{this.getNodeChildren(t,r)}),n&&n.forEach(t=>{this.getNodeChildren(t,r)}),r}initialZoom(t){return this.getChartState().lastTransform.k=t,this}render(){const t=this.getChartState();if(!t.data||0==t.data.length)return console.log("ORG CHART - Data is empty"),t.container&&(Object(i.a)(t.container).select(".nodes-wrapper").remove(),Object(i.a)(t.container).select(".links-wrapper").remove(),Object(i.a)(t.container).select(".connections-wrapper").remove()),this;const e=F.select(t.container),n=e.node().getBoundingClientRect();n.width>0&&(t.svgWidth=n.width);const r={id:"ID"+Math.floor(1e6*Math.random()),chartWidth:t.svgWidth,chartHeight:t.svgHeight};if(t.calc=r,r.centerX=r.chartWidth/2,r.centerY=r.chartHeight/2,t.firstDraw){const e={zoom:null};e.zoom=t.createZoom().clickDistance(10).on("start",(e,n)=>t.onZoomStart(e)).on("end",(e,n)=>t.onZoomEnd(e)).on("zoom",(e,n)=>{t.onZoom(e),this.zoomed(e,n)}).scaleExtent(t.scaleExtent),t.zoomBehavior=e.zoom}t.flexTreeLayout=y({nodeSize:e=>{const n=t.nodeWidth(e),r=t.nodeHeight(e),i=t.siblingsMargin(e),o=t.childrenMargin(e);return t.layoutBindings[t.layout].nodeFlexSize({state:t,node:e,width:n,height:r,siblingsMargin:i,childrenMargin:o})}}).spacing((e,n)=>e.parent==n.parent?0:t.neighbourMargin(e,n)),this.setLayouts({expandNodesFirst:!1});const o=e.patternify({tag:"svg",selector:"svg-chart-container"}).attr("width",t.svgWidth).attr("height",t.svgHeight).attr("font-family",t.defaultFont);t.firstDraw&&o.call(t.zoomBehavior).on("dblclick.zoom",null).attr("cursor","move"),t.svg=o;const a=o.patternify({tag:"g",selector:"chart"});return t.centerG=a.patternify({tag:"g",selector:"center-group"}),t.linksWrapper=t.centerG.patternify({tag:"g",selector:"links-wrapper"}),t.nodesWrapper=t.centerG.patternify({tag:"g",selector:"nodes-wrapper"}),t.connectionsWrapper=t.centerG.patternify({tag:"g",selector:"connections-wrapper"}),t.defsWrapper=o.patternify({tag:"g",selector:"defs-wrapper"}),t.firstDraw&&t.centerG.attr("transform",()=>t.layoutBindings[t.layout].centerTransform({centerX:r.centerX,centerY:r.centerY,scale:t.lastTransform.k,rootMargin:t.rootMargin,root:t.root,chartHeight:r.chartHeight,chartWidth:r.chartWidth})),t.chart=a,this.update(t.root),F.select(window).on("resize."+t.id,()=>{const e=F.select(t.container).node().getBoundingClientRect();t.svg.attr("width",e.width)}),t.firstDraw&&(t.firstDraw=!1),this}addNode(t){const e=this.getChartState();if(t&&(null==e.parentNodeId(t)||e.parentNodeId(t)==e.nodeId(t))&&0==e.data.length)return e.data.push(t),this.render(),this;const n=e.generateRoot(e.data).descendants(),r=n.filter(({data:n})=>e.nodeId(n).toString()===e.nodeId(t).toString())[0];return n.filter(({data:n})=>e.nodeId(n).toString()===e.parentNodeId(t).toString()),r?(console.log(`ORG CHART - ADD - Node with id "${e.nodeId(t)}" already exists in tree`),this):(t._centered&&!t._expanded&&(t._expanded=!0),e.data.push(t),this.updateNodesState(),this)}removeNode(t){const e=this.getChartState(),n=e.generateRoot(e.data).descendants().filter(({data:n})=>e.nodeId(n)==t)[0];return n?(n.descendants().forEach(t=>t.data._filteredOut=!0),e.data=e.data.filter(t=>!t._filteredOut),0==e.data.length?this.render():this.updateNodesState.bind(this)(),this):(console.log(`ORG CHART - REMOVE - Node with id "${t}" not found in the tree`),this)}groupBy(t,e,n){const r={};return t.forEach(t=>{const n=e(t);r[n]||(r[n]=[]),r[n].push(t)}),Object.keys(r).forEach(t=>{r[t]=n(r[t])}),Object.entries(r)}calculateCompactFlexDimensions(t){const e=this.getChartState();t.eachBefore(t=>{t.firstCompact=null,t.compactEven=null,t.flexCompactDim=null,t.firstCompactNode=null}),t.eachBefore(t=>{if(t.children&&t.children.length>1){const n=t.children.filter(t=>!t.children);if(n.length<2)return;n.forEach((t,e)=>{e||(t.firstCompact=!0),t.compactEven=!(e%2),t.row=Math.floor(e/2)});const r=F.max(n.filter(t=>t.compactEven),e.layoutBindings[e.layout].compactDimension.sizeColumn),i=F.max(n.filter(t=>!t.compactEven),e.layoutBindings[e.layout].compactDimension.sizeColumn),o=2*Math.max(r,i),a=this.groupBy(n,t=>t.row,t=>F.max(t,t=>e.layoutBindings[e.layout].compactDimension.sizeRow(t)+e.compactMarginBetween(t))),s=F.sum(a.map(t=>t[1]));n.forEach(t=>{t.firstCompactNode=n[0],t.flexCompactDim=t.firstCompact?[o+e.compactMarginPair(t),s-e.compactMarginBetween(t)]:[0,0]}),t.flexCompactDim=null}})}calculateCompactFlexPositions(t){const e=this.getChartState();t.eachBefore(t=>{if(t.children){const n=t.children.filter(t=>t.flexCompactDim),r=n[0];if(!r)return;n.forEach((t,n,i)=>{0==n&&(r.x-=r.flexCompactDim[0]/2),n&n%2-1?t.x=r.x+.25*r.flexCompactDim[0]-e.compactMarginPair(t)/4:n&&(t.x=r.x+.75*r.flexCompactDim[0]+e.compactMarginPair(t)/4)});const i=r.x+.5*r.flexCompactDim[0];r.x=r.x+.25*r.flexCompactDim[0]-e.compactMarginPair(r)/4;const o=t.x-i;Math.abs(o)<10&&n.forEach(t=>t.x+=o);const a=this.groupBy(n,t=>t.row,t=>F.max(t,t=>e.layoutBindings[e.layout].compactDimension.sizeRow(t))),s=F.cumsum(a.map(t=>t[1]+e.compactMarginBetween(t)));n.forEach((t,e)=>{t.y=t.row?r.y+s[t.row-1]:r.y})}})}update({x0:t,y0:e,x:n=0,y:r=0,width:i,height:o}){const a=this.getChartState();a.compact&&this.calculateCompactFlexDimensions(a.root);const s=a.flexTreeLayout(a.root);a.compact&&this.calculateCompactFlexPositions(a.root);const l=s.descendants(),u=s.descendants().slice(1);l.forEach(a.layoutBindings[a.layout].swap);const c=a.connections,h={};a.allNodes.forEach(t=>h[a.nodeId(t.data)]=t);const d={};l.forEach(t=>d[a.nodeId(t.data)]=t),c.forEach(t=>{const e=h[t.to];t._source=h[t.from],t._target=e});const f=c.filter(t=>d[t.from]&&d[t.to]),p=a.defs.bind(this)(a,f);p!==a.defsWrapper.html()&&a.defsWrapper.html(p);const g=a.linksWrapper.selectAll("path.link").data(u,t=>a.nodeId(t.data)),m=g.enter().insert("path","g").attr("class","link").attr("d",n=>{const r={x:a.layoutBindings[a.layout].linkJoinX({x:t,y:e,width:i,height:o}),y:a.layoutBindings[a.layout].linkJoinY({x:t,y:e,width:i,height:o})};return a.layoutBindings[a.layout].diagonal(r,r,r)}).merge(g);m.attr("fill","none"),this.isEdge()?m.style("display",t=>t.data._pagingButton?"none":"auto"):m.attr("display",t=>t.data._pagingButton?"none":"auto"),m.each(a.linkUpdate),m.transition().duration(a.duration).attr("d",t=>{const e=a.compact&&t.flexCompactDim?{x:a.layoutBindings[a.layout].compactLinkMidX(t,a),y:a.layoutBindings[a.layout].compactLinkMidY(t,a)}:{x:a.layoutBindings[a.layout].linkX(t),y:a.layoutBindings[a.layout].linkY(t)},n={x:a.layoutBindings[a.layout].linkParentX(t),y:a.layoutBindings[a.layout].linkParentY(t)},r=a.compact&&t.flexCompactDim?{x:a.layoutBindings[a.layout].linkCompactXStart(t),y:a.layoutBindings[a.layout].linkCompactYStart(t)}:e;return a.layoutBindings[a.layout].diagonal(e,n,r,{sy:a.linkYOffset})}),g.exit().transition().duration(a.duration).attr("d",t=>{const e={x:a.layoutBindings[a.layout].linkJoinX({x:n,y:r,width:i,height:o}),y:a.layoutBindings[a.layout].linkJoinY({x:n,y:r,width:i,height:o})};return a.layoutBindings[a.layout].diagonal(e,e,null,{sy:a.linkYOffset})}).remove();const y=a.connectionsWrapper.selectAll("path.connection").data(f),v=y.enter().insert("path","g").attr("class","connection").attr("d",n=>{const r={x:a.layoutBindings[a.layout].linkJoinX({x:t,y:e,width:i,height:o}),y:a.layoutBindings[a.layout].linkJoinY({x:t,y:e,width:i,height:o})};return a.layoutBindings[a.layout].diagonal(r,r,null,{sy:a.linkYOffset})}).merge(y);v.attr("fill","none"),v.transition().duration(a.duration).attr("d",t=>{const e=a.layoutBindings[a.layout].linkX({x:t._source.x,y:t._source.y,width:t._source.width,height:t._source.height}),n=a.layoutBindings[a.layout].linkY({x:t._source.x,y:t._source.y,width:t._source.width,height:t._source.height}),r=a.layoutBindings[a.layout].linkJoinX({x:t._target.x,y:t._target.y,width:t._target.width,height:t._target.height}),i=a.layoutBindings[a.layout].linkJoinY({x:t._target.x,y:t._target.y,width:t._target.width,height:t._target.height});return a.linkGroupArc({source:{x:e,y:n},target:{x:r,y:i}})}),v.each(a.connectionsUpdate),y.exit().transition().duration(a.duration).attr("opacity",0).remove();const x=a.nodesWrapper.selectAll("g.node").data(l,({data:t})=>a.nodeId(t)),w=x.enter().append("g").attr("class","node").attr("transform",n=>n==a.root?`translate(${t},${e})`:`translate(${a.layoutBindings[a.layout].nodeJoinX({x:t,y:e,width:i,height:o})},${a.layoutBindings[a.layout].nodeJoinY({x:t,y:e,width:i,height:o})})`).attr("cursor","pointer").on("click.node",(t,e)=>{const{data:n}=e;[...t.srcElement.classList].includes("node-button-foreign-object")||([...t.srcElement.classList].includes("paging-button-wrapper")?this.loadPagingNodes(e):n._pagingButton?console.log("event fired, no handlers"):a.onNodeClick(e))}).on("keydown.node",(t,e)=>{if("Enter"===t.key||" "===t.key||"Spacebar"===t.key){if([...t.srcElement.classList].includes("node-button-foreign-object"))return;if([...t.srcElement.classList].includes("paging-button-wrapper"))return void this.loadPagingNodes(e);"Enter"!==t.key&&" "!==t.key&&"Spacebar"!==t.key||this.onButtonClick(t,e)}});w.each(a.nodeEnter),w.patternify({tag:"rect",selector:"node-rect",data:t=>[t]});const _=w.merge(x).style("font","12px sans-serif");_.patternify({tag:"foreignObject",selector:"node-foreign-object",data:t=>[t]}).style("overflow","visible").patternify({tag:"xhtml:div",selector:"node-foreign-object-div",data:t=>[t]}),this.restyleForeignObjectElements();const b=w.patternify({tag:"g",selector:"node-button-g",data:t=>[t]}).on("click",(t,e)=>this.onButtonClick(t,e)).on("keydown",(t,e)=>{"Enter"!==t.key&&" "!==t.key&&"Spacebar"!==t.key||this.onButtonClick(t,e)});b.patternify({tag:"rect",selector:"node-button-rect",data:t=>[t]}).attr("opacity",0).attr("pointer-events","all").attr("width",t=>a.nodeButtonWidth(t)).attr("height",t=>a.nodeButtonHeight(t)).attr("x",t=>a.nodeButtonX(t)).attr("y",t=>a.nodeButtonY(t)),b.patternify({tag:"foreignObject",selector:"node-button-foreign-object",data:t=>[t]}).attr("width",t=>a.nodeButtonWidth(t)).attr("height",t=>a.nodeButtonHeight(t)).attr("x",t=>a.nodeButtonX(t)).attr("y",t=>a.nodeButtonY(t)).style("overflow","visible").patternify({tag:"xhtml:div",selector:"node-button-div",data:t=>[t]}).style("pointer-events","none").style("display","flex").style("width","100%").style("height","100%"),_.transition().attr("opacity",0).duration(a.duration).attr("transform",({x:t,y:e,width:n,height:r})=>a.layoutBindings[a.layout].nodeUpdateTransform({x:t,y:e,width:n,height:r})).attr("opacity",1),_.select(".node-rect").attr("width",({width:t})=>t).attr("height",({height:t})=>t).attr("x",({})=>0).attr("y",({})=>0).attr("cursor","pointer").attr("rx",3).attr("fill",a.nodeDefaultBackground),_.select(".node-button-g").attr("transform",({width:t,height:e})=>`translate(${a.layoutBindings[a.layout].buttonX({width:t,height:e})},${a.layoutBindings[a.layout].buttonY({width:t,height:e})})`).attr("display",({data:t})=>t._directSubordinates>0?null:"none").attr("opacity",({data:t,children:e,_children:n})=>t._pagingButton?0:e||n?1:0),_.select(".node-button-foreign-object .node-button-div").html(t=>a.buttonContent({node:t,state:a})),_.select(".node-button-text").attr("text-anchor","middle").attr("alignment-baseline","middle").attr("font-size",({children:t})=>t?40:26).text(({children:t})=>t?"-":"+").attr("y",this.isEdge()?10:0),_.each(a.nodeUpdate);const C=x.exit();C.each(a.nodeExit);const k=C.data().reduce((t,e)=>t.depth<e.depth?t:e,{depth:1/0});C.attr("opacity",1).transition().duration(a.duration).attr("transform",t=>{let{x:e,y:n,width:r,height:i}=k.parent||{};return`translate(${a.layoutBindings[a.layout].nodeJoinX({x:e,y:n,width:r,height:i})},${a.layoutBindings[a.layout].nodeJoinY({x:e,y:n,width:r,height:i})})`}).on("end",(function(){F.select(this).remove()})).attr("opacity",0),l.forEach(t=>{t.x0=t.x,t.y0=t.y});const E=a.allNodes.filter(t=>t.data._centered)[0];if(E){let t=[E];E.data._centeredWithDescendants&&(t=a.compact?E.descendants().filter((t,e)=>e<7):E.descendants().filter((t,e,n)=>{const r=Math.round(n.length/2);return n.length%2?e>r-2&&e<r****:e>r-2&&e<r+2})),E.data._centeredWithDescendants=null,E.data._centered=null,this.fit({animate:!0,scale:!1,nodes:t})}}isEdge(){return window.navigator.userAgent.includes("Edge")}hdiagonal(t,e,n,r){return this.getChartState().hdiagonal(t,e,n,r)}diagonal(t,e,n,r){return this.getChartState().diagonal(t,e,n,r)}restyleForeignObjectElements(){const t=this.getChartState();t.svg.selectAll(".node-foreign-object").attr("width",({width:t})=>t).attr("height",({height:t})=>t).attr("x",({})=>0).attr("y",({})=>0),t.svg.selectAll(".node-foreign-object-div").style("width",({width:t})=>t+"px").style("height",({height:t})=>t+"px").html((function(e,n,r){return e.data._pagingButton?`<div class="paging-button-wrapper"><div style="pointer-events:none">${t.pagingButton(e,n,r,t)}</div></div>`:t.nodeContent.bind(this)(e,n,r,t)}))}onButtonClick(t,e){const n=this.getChartState();e.data._pagingButton||(n.setActiveNodeCentered&&(e.data._centered=!0,e.data._centeredWithDescendants=!0),e.children?(e._children=e.children,e.children=null,this.setExpansionFlagToChildren(e,!1)):(e.children=e._children,e._children=null,e.children&&e.children.forEach(({data:t})=>t._expanded=!0)),this.update(e),t.stopPropagation(),n.onExpandOrCollapse(e))}setExpansionFlagToChildren({data:t,children:e,_children:n},r){t._expanded=r,e&&e.forEach(t=>{this.setExpansionFlagToChildren(t,r)}),n&&n.forEach(t=>{this.setExpansionFlagToChildren(t,r)})}expandSomeNodes(t){if(t.data._expanded){let e=t.parent;for(;e&&e._children;)e.children=e._children,e._children=null,e=e.parent}t._children&&t._children.forEach(t=>this.expandSomeNodes(t)),t.children&&t.children.forEach(t=>this.expandSomeNodes(t))}updateNodesState(){const t=this.getChartState();this.setLayouts({expandNodesFirst:!0}),this.update(t.root)}setLayouts({expandNodesFirst:t=!0}){const e=this.getChartState();e.generateRoot=F.stratify().id(t=>e.nodeId(t)).parentId(t=>e.parentNodeId(t)),e.root=e.generateRoot(e.data);const n=e.root.descendants();e.initialExpandLevel>1&&n.length>0&&(n.forEach(t=>{t.depth<=e.initialExpandLevel&&(t.data._expanded=!0)}),e.initialExpandLevel=1);const r={};e.root.descendants().filter(t=>t.children).filter(t=>!t.data._pagingStep).forEach(t=>{t.data._pagingStep=e.minPagingVisibleNodes(t)}),e.root.eachBefore((t,e)=>{t.data._directSubordinatesPaging=t.children?t.children.length:0,t.children&&t.children.forEach((e,n)=>{if(e.data._pagingButton=!1,n>t.data._pagingStep&&(r[e.id]=!0),n===t.data._pagingStep&&t.children.length-1>t.data._pagingStep&&(e.data._pagingButton=!0),r[e.parent.id]&&(r[e.id]=!0),e.data._expanded||e.data._centered||e.data._highlighted||e.data._upToTheRootHighlighted){let t=e;for(;t&&(r[t.id]||t.data._pagingButton);)r[t.id]=!1,t.data._pagingButton&&(t.data._pagingButton=!1,t.parent.children.forEach(t=>{t.data._expanded=!0,r[t.id]=!1})),t=t.parent}})}),e.root=F.stratify().id(t=>e.nodeId(t)).parentId(t=>e.parentNodeId(t))(e.data.filter(t=>!0!==r[t.id])),e.root.each((t,n,r)=>{let i=t._hierarchyHeight||t.height,o=e.nodeWidth(t),a=e.nodeHeight(t);Object.assign(t,{width:o,height:a,_hierarchyHeight:i})}),e.root.x0=0,e.root.y0=0,e.allNodes=e.root.descendants(),e.allNodes.forEach(t=>{Object.assign(t.data,{_directSubordinates:t.children?t.children.length:0,_totalSubordinates:t.descendants().length-1})}),e.root.children&&(t&&e.root.children.forEach(this.expand),e.root.children.forEach(t=>this.collapse(t)),0==e.initialExpandLevel&&(e.root._children=e.root.children,e.root.children=null),[e.root].forEach(t=>this.expandSomeNodes(t)))}collapse(t){t.children&&(t._children=t.children,t._children.forEach(t=>this.collapse(t)),t.children=null)}expand(t){t._children&&(t.children=t._children,t.children.forEach(t=>this.expand(t)),t._children=null)}zoomed(t,e){const n=this.getChartState(),r=n.chart,i=t.transform;n.lastTransform=i,r.attr("transform",i),this.isEdge()&&this.restyleForeignObjectElements()}zoomTreeBounds({x0:t,x1:e,y0:n,y1:r,params:i={animate:!0,scale:!0,onCompleted:()=>{}}}){const{centerG:o,svgWidth:a,svgHeight:s,svg:l,zoomBehavior:u,duration:c,lastTransform:h}=this.getChartState();let d=Math.min(8,.9/Math.max((e-t)/a,(r-n)/s)),f=F.zoomIdentity.translate(a/2,s/2);f=f.scale(i.scale?d:h.k),f=f.translate(-(t+e)/2,-(n+r)/2),l.transition().duration(i.animate?c:0).call(u.transform,f),o.transition().duration(i.animate?c:0).attr("transform","translate(0,0)").on("end",(function(){i.onCompleted&&i.onCompleted()}))}fit({animate:t=!0,nodes:e,scale:n=!0,onCompleted:r=(()=>{})}={}){const i=this.getChartState(),{root:o}=i;let a=e||o.descendants();const s=F.min(a,t=>t.x+i.layoutBindings[i.layout].nodeLeftX(t)),l=F.max(a,t=>t.x+i.layoutBindings[i.layout].nodeRightX(t)),u=F.min(a,t=>t.y+i.layoutBindings[i.layout].nodeTopY(t)),c=F.max(a,t=>t.y+i.layoutBindings[i.layout].nodeBottomY(t));return this.zoomTreeBounds({params:{animate:t,scale:n,onCompleted:r},x0:s-50,x1:l+50,y0:u-50,y1:c+50}),this}loadPagingNodes(t){const e=this.getChartState();t.data._pagingButton=!1;const n=t.parent.data._pagingStep,r=e.pagingStep(t.parent);t.parent.data._pagingStep=n+r,this.updateNodesState()}setExpanded(t,e=!0){const n=this.getChartState(),r=n.allNodes.filter(({data:e})=>n.nodeId(e)==t)[0];if(!r)return console.log(`ORG CHART - ${e?"EXPAND":"COLLAPSE"} - Node with id (${t})  not found in the tree`),this;if(r.data._expanded=e,0==e){const t=r.parent||{descendants:()=>[]};t.descendants().filter(e=>e!=t).forEach(t=>t.data._expanded=!1)}return this}setCentered(t){const e=this.getChartState(),n=e.generateRoot(e.data).descendants().filter(({data:n})=>e.nodeId(n).toString()==t.toString())[0];return n?(n.ancestors().forEach(t=>t.data._expanded=!0),n.data._centered=!0,n.data._expanded=!0,this):(console.log(`ORG CHART - CENTER - Node with id (${t}) not found in the tree`),this)}setHighlighted(t){const e=this.getChartState(),n=e.generateRoot(e.data).descendants().filter(n=>e.nodeId(n.data).toString()===t.toString())[0];return n?(n.ancestors().forEach(t=>t.data._expanded=!0),n.data._highlighted=!0,n.data._expanded=!0,n.data._centered=!0,this):(console.log(`ORG CHART - HIGHLIGHT - Node with id (${t})  not found in the tree`),this)}setUpToTheRootHighlighted(t){const e=this.getChartState(),n=e.generateRoot(e.data).descendants().filter(n=>e.nodeId(n.data).toString()===t.toString())[0];return n?(n.ancestors().forEach(t=>t.data._expanded=!0),n.data._upToTheRootHighlighted=!0,n.data._expanded=!0,n.ancestors().forEach(t=>t.data._upToTheRootHighlighted=!0),this):(console.log(`ORG CHART - HIGHLIGHTROOT - Node with id (${t}) not found in the tree`),this)}clearHighlighting(){const t=this.getChartState();return t.allNodes.forEach(t=>{t.data._highlighted=!1,t.data._upToTheRootHighlighted=!1}),this.update(t.root),this}fullscreen(t){const e=this.getChartState(),n=F.select(t||e.container).node();F.select(document).on("fullscreenchange."+e.id,(function(t){(document.fullscreenElement||document.mozFullscreenElement||document.webkitFullscreenElement)==n?setTimeout(t=>{e.svg.attr("height",window.innerHeight-40)},500):e.svg.attr("height",e.svgHeight)})),n.requestFullscreen?n.requestFullscreen():n.mozRequestFullScreen?n.mozRequestFullScreen():n.webkitRequestFullscreen?n.webkitRequestFullscreen():n.msRequestFullscreen&&n.msRequestFullscreen()}zoomIn(){const{svg:t,zoomBehavior:e}=this.getChartState();t.transition().call(e.scaleBy,1.3)}zoomOut(){const{svg:t,zoomBehavior:e}=this.getChartState();t.transition().call(e.scaleBy,.78)}toDataURL(t,e){var n=new XMLHttpRequest;n.onload=function(){var t=new FileReader;t.onloadend=function(){e(t.result)},t.readAsDataURL(n.response)},n.open("GET",t),n.responseType="blob",n.send()}exportImg({full:t=!1,scale:e=3,onLoad:n=(t=>t),save:r=!0,backgroundColor:i="#FAFAFA"}={}){const o=this,a=this.getChartState(),{svg:s,root:l}=a;let u=0;const c=s.selectAll("img");let h=c.size();const d=()=>{JSON.parse(JSON.stringify(o.lastTransform()));const s=o.duration();t&&o.fit();const{svg:u}=o.getChartState();setTimeout(t=>{o.downloadImage({node:u.node(),scale:e,isSvg:!1,backgroundColor:i,onAlreadySerialized:t=>{o.update(l)},imageName:a.imageName,onLoad:n,save:r})},t?s+10:0)};h>0?c.each((function(){o.toDataURL(this.src,t=>{this.src=t,++u==h&&d()})})):d()}exportSvg(){const{svg:t,imageName:e}=this.getChartState();return this.downloadImage({imageName:e,node:t.node(),scale:3,isSvg:!0}),this}expandAll(){const{data:t}=this.getChartState();return t.forEach(t=>t._expanded=!0),this.render(),this}collapseAll(){const{allNodes:t}=this.getChartState();return t.forEach(t=>t.data._expanded=!1),this.initialExpandLevel(0),this.render(),this}downloadImage({node:t,scale:e=2,imageName:n="graph",isSvg:r=!1,save:i=!0,backgroundColor:o="#FAFAFA",onAlreadySerialized:a=(t=>{}),onLoad:s=(t=>{})}){const l=t;function u(t,e){var n=document.createElement("a");"string"==typeof n.download?(document.body.appendChild(n),n.download=e,n.href=t,n.click(),document.body.removeChild(n)):location.replace(t)}function c(t){const e="http://www.w3.org/2000/xmlns/";t=t.cloneNode(!0);const n=window.location.href+"#",r=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,null,!1);for(;r.nextNode();)for(const t of r.currentNode.attributes)t.value.includes(n)&&(t.value=t.value.replace(n,"#"));return t.setAttributeNS(e,"xmlns","http://www.w3.org/2000/svg"),t.setAttributeNS(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),(new XMLSerializer).serializeToString(t)}if(r){let t=c(l);return t='<?xml version="1.0" standalone="no"?>\r\n'+t,u(f="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(t),n+".svg"),void a()}const h=e,d=document.createElement("img");d.onload=function(){const t=document.createElement("canvas"),e=l.getBoundingClientRect();t.width=e.width*h,t.height=e.height*h;const r=t.getContext("2d");r.fillStyle=o,r.fillRect(0,0,e.width*h,e.height*h),r.drawImage(d,0,0,e.width*h,e.height*h);let a=t.toDataURL("image/png");s&&s(a),i&&u(a,n+".png")};var f="data:image/svg+xml; charset=utf8, "+encodeURIComponent(c(l));a(),d.src=f}getTextWidth(t,{fontSize:e=14,fontWeight:n=400,defaultFont:r="Helvetice",ctx:i}={}){return i.font=`${n||""} ${e}px ${r} `,i.measureText(t).width}clear(){const t=this.getChartState();F.select(window).on("resize."+t.id,null),t.svg&&t.svg.selectAll("*").remove()}}var U=n("iYt/"),W=n("fXoL");const J=["chartContainer"];let V=(()=>{class t{constructor(t){this.renderer=t,this.disablePointerEvents=!1,this.currentTranslation={x:0,y:0}}ngOnInit(){console.log("Hi"),console.log(this.data)}ngAfterViewInit(){this.chart||(this.chart=new q,console.log(this.chart));let t=this.updateChart();if(this.chart.fit(),console.log(t),this.disablePointerEvents){let t=this.chart.getSVG();U.e(t).classed("no-pointer-events",!0)}}ngOnChanges(){this.updateChart(),console.log("cx")}updateChart(){if(!this.data)return;if(!this.chart)return;let t=this.chartContainer.nativeElement;t.style.width="100%",t.style.height="100%",this.chart.container(t).data(this.data).nodeHeight(t=>105).nodeWidth(t=>230).svgWidth(50).initialExpandLevel(t=>4).svgHeight(window.innerHeight-100).childrenMargin(t=>50).compactMarginBetween(t=>25).compactMarginPair(t=>50).neighbourMargin((t,e)=>25).siblingsMargin(t=>25).minPagingVisibleNodes(t=>4).pagingStep(t=>4).buttonContent(({node:t})=>`<div style="px;color:#716E7B;border-radius:5px;padding:4px;font-size:10px;margin:auto auto;background-color:white;border: 1px solid #E4E2E9"> <span style="font-size:9px">${t.children?'<i class="fa fa-angle-up" aria-hidden="true"></i>':'<i class="fa fa-angle-down" aria-hidden="true"></i>'}</span> ${t.data._directSubordinates}  </div>`).linkUpdate((function(t,e,n){U.e(this).attr("stroke",t=>t.data._upToTheRootHighlighted?"#152785":"#E4E2E9").attr("stroke-width",t=>t.data._upToTheRootHighlighted?5:1),t.data._upToTheRootHighlighted&&U.e(this).raise()})).nodeContent((function(t,e,n,r){return console.log(t.data.name),`\n                  <div style="font-family: 'Inter', sans-serif;background-color:#FFFFFF; position:absolute;margin-top:-1px; margin-left:-1px;width:${t.width}px;height:${t.height}px;border-radius:10px;border: 1px solid #E4E2E9">\n                    <div style="background-color:#FFFFFF;position:absolute;margin-top:-25px;margin-left:15px;border-radius:100px;width:50px;height:50px;" ></div>\n                    \n                    <img src=" ${t.data.profileUrl}" style="position:absolute;margin-top:-20px;margin-left:20px;border-radius:100px;width:40px;height:40px;border: 2px solid green;" />\n              \n                    <div style="font-size:15px;color:#08011E;margin-left:20px;margin-top:32px"> ${t.data.name} </div>\n                    <div style="color:#716E7B;margin-left:20px;margin-top:3px;font-size:10px;"> ${t.data.positionName} </div>\n\n\n                </div>\n      `})).render(),U.f().scaleExtent([1,5]).translateExtent([[0,0],[60,40]]),this.chart.fit()}fitChartToScreen(){this.chart.fit()}}return t.\u0275fac=function(e){return new(e||t)(W["\u0275\u0275directiveInject"](W.Renderer2))},t.\u0275cmp=W["\u0275\u0275defineComponent"]({type:t,selectors:[["app-project-structure-org-chart"]],viewQuery:function(t,e){if(1&t&&W["\u0275\u0275viewQuery"](J,!0),2&t){let t;W["\u0275\u0275queryRefresh"](t=W["\u0275\u0275loadQuery"]())&&(e.chartContainer=t.first)}},inputs:{data:"data"},features:[W["\u0275\u0275NgOnChangesFeature"]],decls:2,vars:0,consts:[["id","chartContainer",1,"svg-container"],["chartContainer",""]],template:function(t,e){1&t&&W["\u0275\u0275element"](0,"div",0,1)},styles:[".svg-contanier[_ngcontent-%COMP%]{display:inline-block;position:relative;width:100%;padding-bottom:100%;vertical-align:top;overflow:hidden}.svg-contanier[_ngcontent-%COMP%]   .no-pointer-events[_ngcontent-%COMP%]{pointer-events:none}.svg-contanier[_ngcontent-%COMP%]   .svg-content[_ngcontent-%COMP%]{display:inline-block;position:absolute;top:0;left:0}"]}),t})()},"68h0":function(t,e,n){"use strict";function r(){return[]}e.a=function(t){return null==t?r:function(){return this.querySelectorAll(t)}}},AKqJ:function(t,e,n){"use strict";n.d(e,"b",(function(){return s}));var r=n("wakA");function i(t){return function(){this.style.removeProperty(t)}}function o(t,e,n){return function(){this.style.setProperty(t,e,n)}}function a(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}function s(t,e){return t.style.getPropertyValue(e)||Object(r.a)(t).getComputedStyle(t,null).getPropertyValue(e)}e.a=function(t,e,n){return arguments.length>1?this.each((null==e?i:"function"==typeof e?a:o)(t,e,null==n?"":n)):s(this.node(),t)}},HQnw:function(t,e,n){"use strict";var r=n("XwrM");e.a=function(t){return"string"==typeof t?new r.a([[document.querySelector(t)]],[document.documentElement]):new r.a([[t]],r.c)}},IeU1:function(t,e,n){"use strict";function r(t){return null==t?null:function(t){if("function"!=typeof t)throw new Error;return t}(t)}n.d(e,"a",(function(){return r}))},MBxo:function(t,e,n){"use strict";var r=n("HQnw");n.d(e,"a",(function(){return r.a}))},NU5n:function(t,e,n){"use strict";var r=n("IeU1"),i=n("a8tM"),o={depth:-1},a={},s={};function l(t){return t.id}function u(t){return t.parentId}function c(t){let e=t.length;if(e<2)return"";for(;--e>1&&!h(t,e););return t.slice(0,e)}function h(t,e){if("/"===t[e]){let n=0;for(;e>0&&"\\"===t[--e];)++n;if(0==(1&n))return!0}return!1}e.a=function(){var t,e=l,n=u;function d(r){var l,u,d,f,p,g,m,y,v=Array.from(r),x=e,w=n,_=new Map;if(null!=t){const e=v.map((e,n)=>function(t){let e=(t=""+t).length;return h(t,e-1)&&!h(t,e-2)&&(t=t.slice(0,-1)),"/"===t[0]?t:"/"+t}(t(e,n,r))),n=e.map(c),i=new Set(e).add("");for(const t of n)i.has(t)||(i.add(t),e.push(t),n.push(c(t)),v.push(s));x=(t,n)=>e[n],w=(t,e)=>n[e]}for(d=0,l=v.length;d<l;++d)g=v[d]=new i.a(u=v[d]),null!=(m=x(u,d,r))&&(m+="")&&(y=g.id=m,_.set(y,_.has(y)?a:g)),null!=(m=w(u,d,r))&&(m+="")&&(g.parent=m);for(d=0;d<l;++d)if(m=(g=v[d]).parent){if(!(p=_.get(m)))throw new Error("missing: "+m);if(p===a)throw new Error("ambiguous: "+m);p.children?p.children.push(g):p.children=[g],g.parent=p}else{if(f)throw new Error("multiple roots");f=g}if(!f)throw new Error("no root");if(null!=t){for(;f.data===s&&1===f.children.length;)f=f.children[0],--l;for(let t=v.length-1;t>=0&&(g=v[t]).data===s;--t)g.data=null}if(f.parent=o,f.eachBefore((function(t){t.depth=t.parent.depth+1,--l})).eachBefore(i.b),f.parent=null,l>0)throw new Error("cycle");return f}return d.id=function(t){return arguments.length?(e=Object(r.a)(t),d):e},d.parentId=function(t){return arguments.length?(n=Object(r.a)(t),d):n},d.path=function(e){return arguments.length?(t=Object(r.a)(e),d):t},d}},PtdH:function(t,e,n){"use strict";var r=n("mcxf");e.a=function(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),r.a.hasOwnProperty(e)?{space:r.a[e],local:t}:t}},Q9qd:function(t){t.exports=JSON.parse('{"_args":[["d3-flextree@2.1.2","C:\\\\Users\\\\<USER>\\\\Documents\\\\kebs-new\\\\KEBS\\\\newWebApplication"]],"_from":"d3-flextree@2.1.2","_id":"d3-flextree@2.1.2","_inBundle":false,"_integrity":"sha512-gJiHrx5uTTHq44bjyIb3xpbmmdZcWLYPKeO9EPVOq8EylMFOiH2+9sWqKAiQ4DcFuOZTAxPOQyv0Rnmji/g15A==","_location":"/d3-flextree","_phantomChildren":{},"_requested":{"type":"version","registry":true,"raw":"d3-flextree@2.1.2","name":"d3-flextree","escapedName":"d3-flextree","rawSpec":"2.1.2","saveSpec":null,"fetchSpec":"2.1.2"},"_requiredBy":["/d3-org-chart"],"_resolved":"https://registry.npmjs.org/d3-flextree/-/d3-flextree-2.1.2.tgz","_spec":"2.1.2","_where":"C:\\\\Users\\\\<USER>\\\\Documents\\\\kebs-new\\\\KEBS\\\\newWebApplication","author":{"name":"Chris Maloney","url":"http://chrismaloney.org"},"bugs":{"url":"https://github.com/klortho/d3-flextree/issues"},"dependencies":{"d3-hierarchy":"^1.1.5"},"description":"Flexible tree layout algorithm that allows for variable node sizes.","devDependencies":{"babel-plugin-external-helpers":"^6.22.0","babel-preset-es2015-rollup":"^3.0.0","d3":"^4.13.0","d3-selection-multi":"^1.0.1","eslint":"^4.19.1","jsdom":"^11.6.2","npm-run-all":"^4.1.2","rollup":"^0.55.3","rollup-plugin-babel":"^2.7.1","rollup-plugin-commonjs":"^8.0.2","rollup-plugin-copy":"^0.2.3","rollup-plugin-json":"^2.3.0","rollup-plugin-node-resolve":"^3.0.2","rollup-plugin-uglify":"^3.0.0","uglify-es":"^3.3.9"},"homepage":"https://github.com/klortho/d3-flextree","jsnext:main":"index","keywords":["d3","d3-module","layout","tree","hierarchy","d3-hierarchy","plugin","d3-plugin","infovis","visualization","2d"],"license":"WTFPL","main":"build/d3-flextree.js","module":"index","name":"d3-flextree","repository":{"type":"git","url":"git+https://github.com/klortho/d3-flextree.git"},"scripts":{"build":"rollup -c","build:demo":"rollup -c --environment BUILD:demo","build:dev":"rollup -c --environment BUILD:dev","build:prod":"rollup -c --environment BUILD:prod","build:test":"rollup -c --environment BUILD:test","clean":"rm -rf build demo test","lint":"eslint index.js src","prepare":"npm-run-all clean build lint test","test":"npm-run-all test:*","test:browser":"node test/browser-tests.js","test:main":"node test/bundle.js"},"version":"2.1.2"}')},REAR:function(t,e,n){"use strict";e.a=function(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}},UHDu:function(t,e,n){"use strict";var r=n("a8tM");function i(t,e){return t.parent===e.parent?1:2}function o(t){var e=t.children;return e?e[0]:t.t}function a(t){var e=t.children;return e?e[e.length-1]:t.t}function s(t,e,n){var r=n/(e.i-t.i);e.c-=r,e.s+=n,t.c+=r,e.z+=n,e.m+=n}function l(t,e,n){return t.a.parent===e.parent?t.a:n}function u(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}u.prototype=Object.create(r.a.prototype),e.a=function(){var t=i,e=1,n=1,r=null;function c(i){var o=function(t){for(var e,n,r,i,o,a=new u(t,0),s=[a];e=s.pop();)if(r=e._.children)for(e.children=new Array(o=r.length),i=o-1;i>=0;--i)s.push(n=e.children[i]=new u(r[i],i)),n.parent=e;return(a.parent=new u(null,0)).children=[a],a}(i);if(o.eachAfter(h),o.parent.m=-o.z,o.eachBefore(d),r)i.eachBefore(f);else{var a=i,s=i,l=i;i.eachBefore((function(t){t.x<a.x&&(a=t),t.x>s.x&&(s=t),t.depth>l.depth&&(l=t)}));var c=a===s?1:t(a,s)/2,p=c-a.x,g=e/(s.x+c+p),m=n/(l.depth||1);i.eachBefore((function(t){t.x=(t.x+p)*g,t.y=t.depth*m}))}return i}function h(e){var n=e.children,r=e.parent.children,i=e.i?r[e.i-1]:null;if(n){!function(t){for(var e,n=0,r=0,i=t.children,o=i.length;--o>=0;)(e=i[o]).z+=n,e.m+=n,n+=e.s+(r+=e.c)}(e);var u=(n[0].z+n[n.length-1].z)/2;i?(e.z=i.z+t(e._,i._),e.m=e.z-u):e.z=u}else i&&(e.z=i.z+t(e._,i._));e.parent.A=function(e,n,r){if(n){for(var i,u=e,c=e,h=n,d=u.parent.children[0],f=u.m,p=c.m,g=h.m,m=d.m;h=a(h),u=o(u),h&&u;)d=o(d),(c=a(c)).a=e,(i=h.z+g-u.z-f+t(h._,u._))>0&&(s(l(h,e,r),e,i),f+=i,p+=i),g+=h.m,f+=u.m,m+=d.m,p+=c.m;h&&!a(c)&&(c.t=h,c.m+=g-p),u&&!o(d)&&(d.t=u,d.m+=f-m,r=e)}return r}(e,i,e.parent.A||r[0])}function d(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function f(t){t.x*=e,t.y=t.depth*n}return c.separation=function(e){return arguments.length?(t=e,c):t},c.size=function(t){return arguments.length?(r=!1,e=+t[0],n=+t[1],c):r?null:[e,n]},c.nodeSize=function(t){return arguments.length?(r=!0,e=+t[0],n=+t[1],c):r?[e,n]:null},c}},XwrM:function(t,e,n){"use strict";n.d(e,"c",(function(){return it})),n.d(e,"a",(function(){return ot}));var r=n("ZqYF");function i(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}var o=n("68h0"),a=n("aRnI"),s=Array.prototype.find;function l(){return this.firstElementChild}var u=Array.prototype.filter;function c(){return Array.from(this.children)}var h=function(t){return new Array(t.length)};function d(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}d.prototype={constructor:d,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var f=function(t){return function(){return t}};function p(t,e,n,r,i,o){for(var a,s=0,l=e.length,u=o.length;s<u;++s)(a=e[s])?(a.__data__=o[s],r[s]=a):n[s]=new d(t,o[s]);for(;s<l;++s)(a=e[s])&&(i[s]=a)}function g(t,e,n,r,i,o,a){var s,l,u,c=new Map,h=e.length,f=o.length,p=new Array(h);for(s=0;s<h;++s)(l=e[s])&&(p[s]=u=a.call(l,l.__data__,s,e)+"",c.has(u)?i[s]=l:c.set(u,l));for(s=0;s<f;++s)u=a.call(t,o[s],s,o)+"",(l=c.get(u))?(r[s]=l,l.__data__=o[s],c.delete(u)):n[s]=new d(t,o[s]);for(s=0;s<h;++s)(l=e[s])&&c.get(p[s])===l&&(i[s]=l)}function m(t){return t.__data__}function y(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function v(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}var x=n("PtdH");function w(t){return function(){this.removeAttribute(t)}}function _(t){return function(){this.removeAttributeNS(t.space,t.local)}}function b(t,e){return function(){this.setAttribute(t,e)}}function C(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function k(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}function E(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}var M=n("AKqJ");function $(t){return function(){delete this[t]}}function S(t,e){return function(){this[t]=e}}function B(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}function N(t){return t.trim().split(/^|\s+/)}function z(t){return t.classList||new A(t)}function A(t){this._node=t,this._names=N(t.getAttribute("class")||"")}function T(t,e){for(var n=z(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function O(t,e){for(var n=z(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function X(t){return function(){T(this,t)}}function R(t){return function(){O(this,t)}}function L(t,e){return function(){(e.apply(this,arguments)?T:O)(this,t)}}function j(){this.textContent=""}function H(t){return function(){this.textContent=t}}function Y(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function P(){this.innerHTML=""}function D(t){return function(){this.innerHTML=t}}function I(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function F(){this.nextSibling&&this.parentNode.appendChild(this)}function q(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}A.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var U=n("auFx");function W(){return null}function J(){var t=this.parentNode;t&&t.removeChild(this)}function V(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Z(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function G(t){return t.trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}))}function Q(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)n=e[r],t.type&&n.type!==t.type||n.name!==t.name?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function K(t,e,n){return function(){var r,i=this.__on,o=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(i)for(var a=0,s=i.length;a<s;++a)if((r=i[a]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),void(r.value=e);this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}var tt=n("wakA");function et(t,e,n){var r=Object(tt.a)(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}function nt(t,e){return function(){return et(this,t,e)}}function rt(t,e){return function(){return et(this,t,e.apply(this,arguments))}}var it=[null];function ot(t,e){this._groups=t,this._parents=e}function at(){return new ot([[document.documentElement]],it)}ot.prototype=at.prototype={constructor:ot,select:function(t){"function"!=typeof t&&(t=Object(r.a)(t));for(var e=this._groups,n=e.length,i=new Array(n),o=0;o<n;++o)for(var a,s,l=e[o],u=l.length,c=i[o]=new Array(u),h=0;h<u;++h)(a=l[h])&&(s=t.call(a,a.__data__,h,l))&&("__data__"in a&&(s.__data__=a.__data__),c[h]=s);return new ot(i,this._parents)},selectAll:function(t){t="function"==typeof t?function(t){return function(){return i(t.apply(this,arguments))}}(t):Object(o.a)(t);for(var e=this._groups,n=e.length,r=[],a=[],s=0;s<n;++s)for(var l,u=e[s],c=u.length,h=0;h<c;++h)(l=u[h])&&(r.push(t.call(l,l.__data__,h,u)),a.push(l));return new ot(r,a)},selectChild:function(t){return this.select(null==t?l:function(t){return function(){return s.call(this.children,t)}}("function"==typeof t?t:Object(a.a)(t)))},selectChildren:function(t){return this.selectAll(null==t?c:function(t){return function(){return u.call(this.children,t)}}("function"==typeof t?t:Object(a.a)(t)))},filter:function(t){"function"!=typeof t&&(t=Object(a.b)(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var o,s=e[i],l=s.length,u=r[i]=[],c=0;c<l;++c)(o=s[c])&&t.call(o,o.__data__,c,s)&&u.push(o);return new ot(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,m);var n=e?g:p,r=this._parents,i=this._groups;"function"!=typeof t&&(t=f(t));for(var o=i.length,a=new Array(o),s=new Array(o),l=new Array(o),u=0;u<o;++u){var c=r[u],h=i[u],d=h.length,v=y(t.call(c,c&&c.__data__,u,r)),x=v.length,w=s[u]=new Array(x),_=a[u]=new Array(x),b=l[u]=new Array(d);n(c,h,w,_,b,v,e);for(var C,k,E=0,M=0;E<x;++E)if(C=w[E]){for(E>=M&&(M=E+1);!(k=_[M])&&++M<x;);C._next=k||null}}return(a=new ot(a,r))._enter=s,a._exit=l,a},enter:function(){return new ot(this._enter||this._groups.map(h),this._parents)},exit:function(){return new ot(this._exit||this._groups.map(h),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=Math.min(i,r.length),a=new Array(i),s=0;s<o;++s)for(var l,u=n[s],c=r[s],h=u.length,d=a[s]=new Array(h),f=0;f<h;++f)(l=u[f]||c[f])&&(d[f]=l);for(;s<i;++s)a[s]=n[s];return new ot(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=v);for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var a,s=n[o],l=s.length,u=i[o]=new Array(l),c=0;c<l;++c)(a=s[c])&&(u[c]=a);u.sort(e)}return new ot(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,s=o.length;a<s;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=Object(x.a)(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?_:w:"function"==typeof e?n.local?E:k:n.local?C:b)(n,e))},style:M.a,property:function(t,e){return arguments.length>1?this.each((null==e?$:"function"==typeof e?B:S)(t,e)):this.node()[t]},classed:function(t,e){var n=N(t+"");if(arguments.length<2){for(var r=z(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?L:e?X:R)(n,e))},text:function(t){return arguments.length?this.each(null==t?j:("function"==typeof t?Y:H)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?P:("function"==typeof t?I:D)(t)):this.node().innerHTML},raise:function(){return this.each(F)},lower:function(){return this.each(q)},append:function(t){var e="function"==typeof t?t:Object(U.a)(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var n="function"==typeof t?t:Object(U.a)(t),i=null==e?W:"function"==typeof e?e:Object(r.a)(e);return this.select((function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)}))},remove:function(){return this.each(J)},clone:function(t){return this.select(t?Z:V)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=G(t+""),a=o.length;if(!(arguments.length<2)){for(s=e?K:Q,r=0;r<a;++r)this.each(s(o[r],e,n));return this}var s=this.node().__on;if(s)for(var l,u=0,c=s.length;u<c;++u)for(r=0,l=s[u];r<a;++r)if((i=o[r]).type===l.type&&i.name===l.name)return l.value},dispatch:function(t,e){return this.each(("function"==typeof e?rt:nt)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}},e.b=at},ZqYF:function(t,e,n){"use strict";function r(){}e.a=function(t){return null==t?r:function(){return this.querySelector(t)}}},a8tM:function(t,e,n){"use strict";function r(t){var e=0,n=t.children,r=n&&n.length;if(r)for(;--r>=0;)e+=n[r].value;else e=1;t.value=e}function i(t,e){t instanceof Map?(t=[void 0,t],void 0===e&&(e=a)):void 0===e&&(e=o);for(var n,r,i,s,c,h=new u(t),d=[h];n=d.pop();)if((i=e(n.data))&&(c=(i=Array.from(i)).length))for(n.children=i,s=c-1;s>=0;--s)d.push(r=i[s]=new u(i[s])),r.parent=n,r.depth=n.depth+1;return h.eachBefore(l)}function o(t){return t.children}function a(t){return Array.isArray(t)?t[1]:null}function s(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function l(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function u(t){this.data=t,this.depth=this.height=0,this.parent=null}n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return u})),u.prototype=i.prototype={constructor:u,count:function(){return this.eachAfter(r)},each:function(t,e){let n=-1;for(const r of this)t.call(e,r,++n,this);return this},eachAfter:function(t,e){for(var n,r,i,o=this,a=[o],s=[],l=-1;o=a.pop();)if(s.push(o),n=o.children)for(r=0,i=n.length;r<i;++r)a.push(n[r]);for(;o=s.pop();)t.call(e,o,++l,this);return this},eachBefore:function(t,e){for(var n,r,i=this,o=[i],a=-1;i=o.pop();)if(t.call(e,i,++a,this),n=i.children)for(r=n.length-1;r>=0;--r)o.push(n[r]);return this},find:function(t,e){let n=-1;for(const r of this)if(t.call(e,r,++n,this))return r},sum:function(t){return this.eachAfter((function(e){for(var n=+t(e.data)||0,r=e.children,i=r&&r.length;--i>=0;)n+=r[i].value;e.value=n}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),r=e.ancestors(),i=null;for(t=n.pop(),e=r.pop();t===e;)i=t,t=n.pop(),e=r.pop();return i}(e,t),r=[e];e!==n;)r.push(e=e.parent);for(var i=r.length;t!==n;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(n){n!==t&&e.push({source:n.parent,target:n})})),e},copy:function(){return i(this).eachBefore(s)},[Symbol.iterator]:function*(){var t,e,n,r,i=this,o=[i];do{for(t=o.reverse(),o=[];i=t.pop();)if(yield i,e=i.children)for(n=0,r=e.length;n<r;++n)o.push(e[n])}while(o.length)}}},aRnI:function(t,e,n){"use strict";function r(t){return function(e){return e.matches(t)}}n.d(e,"a",(function(){return r})),e.b=function(t){return function(){return this.matches(t)}}},auFx:function(t,e,n){"use strict";var r=n("PtdH"),i=n("mcxf");function o(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===i.b&&e.documentElement.namespaceURI===i.b?e.createElement(t):e.createElementNS(n,t)}}function a(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}e.a=function(t){var e=Object(r.a)(t);return(e.local?a:o)(e)}},"iYt/":function(t,e,n){"use strict";n.d(e,"a",(function(){return H.a})),n.d(e,"b",(function(){return H.b})),n.d(e,"c",(function(){return q})),n.d(e,"d",(function(){return U})),n.d(e,"e",(function(){return W.a})),n.d(e,"f",(function(){return J.a})),n("xo8x"),n("tU+D");var r=function(t,e,n){t.prototype=e.prototype=n,n.constructor=t};function i(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function o(){}var a=.7,s=1/a,l="\\s*([+-]?\\d+)\\s*",u="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",c="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",h=/^#([0-9a-f]{3,8})$/,d=new RegExp("^rgb\\("+[l,l,l]+"\\)$"),f=new RegExp("^rgb\\("+[c,c,c]+"\\)$"),p=new RegExp("^rgba\\("+[l,l,l,u]+"\\)$"),g=new RegExp("^rgba\\("+[c,c,c,u]+"\\)$"),m=new RegExp("^hsl\\("+[u,c,c]+"\\)$"),y=new RegExp("^hsla\\("+[u,c,c,u]+"\\)$"),v={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function x(){return this.rgb().formatHex()}function w(){return this.rgb().formatRgb()}function _(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=h.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?b(e):3===n?new M(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?C(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?C(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=d.exec(t))?new M(e[1],e[2],e[3],1):(e=f.exec(t))?new M(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=p.exec(t))?C(e[1],e[2],e[3],e[4]):(e=g.exec(t))?C(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=m.exec(t))?N(e[1],e[2]/100,e[3]/100,1):(e=y.exec(t))?N(e[1],e[2]/100,e[3]/100,e[4]):v.hasOwnProperty(t)?b(v[t]):"transparent"===t?new M(NaN,NaN,NaN,0):null}function b(t){return new M(t>>16&255,t>>8&255,255&t,1)}function C(t,e,n,r){return r<=0&&(t=e=n=NaN),new M(t,e,n,r)}function k(t){return t instanceof o||(t=_(t)),t?new M((t=t.rgb()).r,t.g,t.b,t.opacity):new M}function E(t,e,n,r){return 1===arguments.length?k(t):new M(t,e,n,null==r?1:r)}function M(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function $(){return"#"+B(this.r)+B(this.g)+B(this.b)}function S(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}function B(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function N(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new A(t,e,n,r)}function z(t){if(t instanceof A)return new A(t.h,t.s,t.l,t.opacity);if(t instanceof o||(t=_(t)),!t)return new A;if(t instanceof A)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),s=NaN,l=a-i,u=(a+i)/2;return l?(s=e===a?(n-r)/l+6*(n<r):n===a?(r-e)/l+2:(e-n)/l+4,l/=u<.5?a+i:2-a-i,s*=60):l=u>0&&u<1?0:s,new A(s,l,u,t.opacity)}function A(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function T(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}function O(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}r(o,_,{copy:function(t){return Object.assign(new this.constructor,this,t)},displayable:function(){return this.rgb().displayable()},hex:x,formatHex:x,formatHsl:function(){return z(this).formatHsl()},formatRgb:w,toString:w}),r(M,E,i(o,{brighter:function(t){return t=null==t?s:Math.pow(s,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?a:Math.pow(a,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:$,formatHex:$,formatRgb:S,toString:S})),r(A,(function(t,e,n,r){return 1===arguments.length?z(t):new A(t,e,n,null==r?1:r)}),i(o,{brighter:function(t){return t=null==t?s:Math.pow(s,t),new A(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?a:Math.pow(a,t),new A(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new M(T(t>=240?t-240:t+120,i,r),T(t,i,r),T(t<120?t+240:t-120,i,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"hsl(":"hsla(")+(this.h||0)+", "+100*(this.s||0)+"%, "+100*(this.l||0)+"%"+(1===t?")":", "+t+")")}}));var X=function(t){return function(){return t}};function R(t,e){var n=e-t;return n?function(t,e){return function(n){return t+n*e}}(t,n):X(isNaN(t)?e:t)}function L(t){return function(e){var n,r,i=e.length,o=new Array(i),a=new Array(i),s=new Array(i);for(n=0;n<i;++n)r=E(e[n]),o[n]=r.r||0,a[n]=r.g||0,s[n]=r.b||0;return o=t(o),a=t(a),s=t(s),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=s(t),r+""}}}function j(t){return{type:t}}!function t(e){var n=function(t){return 1==(t=+t)?R:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):X(isNaN(e)?n:e)}}(e);function r(t,e){var r=n((t=E(t)).r,(e=E(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=R(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return r.gamma=t,r}(1),L((function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1];return O((n-r/e)*e,r>0?t[r-1]:2*i-o,i,o,r<e-1?t[r+2]:2*o-i)}})),L((function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e);return O((n-r/e)*e,t[(r+e-1)%e],t[r%e],t[(r+1)%e],t[(r+2)%e])}})),new RegExp(/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g.source,"g"),n("HQnw"),n("REAR"),n("u/C0"),Math,["w","e"].map(j),["n","s"].map(j),["n","w","e","s","nw","ne","sw","se"].map(j);var H=n("yUfG");class Y extends Map{constructor(t,e=D){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[n,r]of t)this.set(n,r)}get(t){return super.get(P(this,t))}has(t){return super.has(P(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},n){const r=e(n);return t.has(r)&&(n=t.get(n),t.delete(r)),n}(this,t))}}function P({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):n}function D(t){return null!==t&&"object"==typeof t?t.valueOf():t}function I(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}Set;const F=Symbol("implicit");function q(){var t=new Y,e=[],n=[],r=F;function i(i){let o=t.get(i);if(void 0===o){if(r!==F)return r;t.set(i,o=e.push(i)-1)}return n[o%n.length]}return i.domain=function(n){if(!arguments.length)return e.slice();e=[],t=new Y;for(const r of n)t.has(r)||t.set(r,e.push(r)-1);return i},i.range=function(t){return arguments.length?(n=Array.from(t),i):n.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return q(e,n).unknown(r)},I.apply(i,arguments),i}var U=function(t){for(var e=t.length/6|0,n=new Array(e),r=0;r<e;)n[r]="#"+t.slice(6*r,6*++r);return n}("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),W=n("MBxo"),J=n("2TRZ")},mcxf:function(t,e,n){"use strict";n.d(e,"b",(function(){return r}));var r="http://www.w3.org/1999/xhtml";e.a={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},"tU+D":function(t,e,n){"use strict";n.d(e,"b",(function(){return a}));var r=n("HQnw");const i={capture:!0,passive:!1};var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};function a(t,e){var n=t.document.documentElement,a=Object(r.a)(t).on("dragstart.drag",null);e&&(a.on("click.drag",o,i),setTimeout((function(){a.on("click.drag",null)}),0)),"onselectstart"in n?a.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}e.a=function(t){var e=t.document.documentElement,n=Object(r.a)(t).on("dragstart.drag",o,i);"onselectstart"in e?n.on("selectstart.drag",o,i):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}},"u/C0":function(t,e,n){"use strict";n.d(e,"a",(function(){return R}));var r=n("XwrM"),i={value:()=>{}};function o(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new a(r)}function a(t){this._=t}function s(t,e){return t.trim().split(/^|\s+/).map((function(t){var n="",r=t.indexOf(".");if(r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),t&&!e.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}}))}function l(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}function u(t,e,n){for(var r=0,o=t.length;r<o;++r)if(t[r].name===e){t[r]=i,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}a.prototype=o.prototype={constructor:a,on:function(t,e){var n,r=this._,i=s(t+"",r),o=-1,a=i.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++o<a;)if(n=(t=i[o]).type)r[n]=u(r[n],t.name,e);else if(null==e)for(n in r)r[n]=u(r[n],t.name,null);return this}for(;++o<a;)if((n=(t=i[o]).type)&&(n=l(r[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new a(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=new Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,n=(r=this._[t]).length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};var c,h,d=o,f=0,p=0,g=0,m=0,y=0,v=0,x="object"==typeof performance&&performance.now?performance:Date,w="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function _(){return y||(w(b),y=x.now()+v)}function b(){y=0}function C(){this._call=this._time=this._next=null}function k(t,e,n){var r=new C;return r.restart(t,e,n),r}function E(){y=(m=x.now())+v,f=p=0;try{!function(){_(),++f;for(var t,e=c;e;)(t=y-e._time)>=0&&e._call.call(void 0,t),e=e._next;--f}()}finally{f=0,function(){for(var t,e,n=c,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:c=e);h=t,$(r)}(),y=0}}function M(){var t=x.now(),e=t-m;e>1e3&&(v-=e,m=t)}function $(t){f||(p&&(p=clearTimeout(p)),t-y>24?(t<1/0&&(p=setTimeout(E,t-x.now()-v)),g&&(g=clearInterval(g))):(g||(m=x.now(),g=setInterval(M,1e3)),f=1,w(E)))}C.prototype=k.prototype={constructor:C,restart:function(t,e,n){if("function"!=typeof t)throw new TypeError("callback is not a function");n=(null==n?_():+n)+(null==e?0:+e),this._next||h===this||(h?h._next=this:c=this,h=this),this._call=t,this._time=n,$()},stop:function(){this._call&&(this._call=null,this._time=1/0,$())}};var S=function(t,e,n){var r=new C;return r.restart(n=>{r.stop(),t(n+e)},e=null==e?0:+e,n),r},B=d("start","end","cancel","interrupt"),N=[],z=function(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(l){var u,c,h,d;if(1!==n.state)return s();for(u in i)if((d=i[u]).name===n.name){if(3===d.state)return S(o);4===d.state?(d.state=6,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete i[u]):+u<e&&(d.state=6,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete i[u])}if(S((function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(l))})),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(n.state=3,r=new Array(h=n.tween.length),u=0,c=-1;u<h;++u)(d=n.tween[u].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=d);r.length=c+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(s),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),s())}function s(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=k((function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)}),0,n.time)}(t,n,{name:e,index:r,group:i,on:B,tween:N,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})};function A(t,e){var n=O(t,e);if(n.state>0)throw new Error("too late; already scheduled");return n}function T(t,e){var n=O(t,e);if(n.state>3)throw new Error("too late; already running");return n}function O(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw new Error("transition not found");return n}var X,R=function(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o)(n=o[i]).name===e?(r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]):a=!1;a&&delete t.__transition}},L=function(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}},j=180/Math.PI,H={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1},Y=function(t,e,n,r,i,o){var a,s,l;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(l=t*n+e*r)&&(n-=t*l,r-=e*l),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,l/=s),t*r<e*n&&(t=-t,e=-e,l=-l,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*j,skewX:Math.atan(l)*j,scaleX:a,scaleY:s}};function P(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var s=[],l=[];return o=t(o),a=t(a),function(t,r,i,o,a,s){if(t!==i||r!==o){var l=a.push("translate(",null,e,null,n);s.push({i:l-4,x:L(t,i)},{i:l-2,x:L(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,s,l),function(t,e,n,o){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),o.push({i:n.push(i(n)+"rotate(",null,r)-2,x:L(t,e)})):e&&n.push(i(n)+"rotate("+e+r)}(o.rotate,a.rotate,s,l),function(t,e,n,o){t!==e?o.push({i:n.push(i(n)+"skewX(",null,r)-2,x:L(t,e)}):e&&n.push(i(n)+"skewX("+e+r)}(o.skewX,a.skewX,s,l),function(t,e,n,r,o,a){if(t!==n||e!==r){var s=o.push(i(o)+"scale(",null,",",null,")");a.push({i:s-4,x:L(t,n)},{i:s-2,x:L(e,r)})}else 1===n&&1===r||o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,s,l),o=a=null,function(t){for(var e,n=-1,r=l.length;++n<r;)s[(e=l[n]).i]=e.x(t);return s.join("")}}}var D=P((function(t){const e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?H:Y(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),I=P((function(t){return null==t?H:(X||(X=document.createElementNS("http://www.w3.org/2000/svg","g")),X.setAttribute("transform",t),(t=X.transform.baseVal.consolidate())?Y((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):H)}),", ",")",")"),F=n("PtdH");function q(t,e){var n,r;return function(){var i=T(this,t),o=i.tween;if(o!==n)for(var a=0,s=(r=n=o).length;a<s;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}i.tween=r}}function U(t,e,n){var r,i;if("function"!=typeof n)throw new Error;return function(){var o=T(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var s={name:e,value:n},l=0,u=i.length;l<u;++l)if(i[l].name===e){i[l]=s;break}l===u&&i.push(s)}o.tween=i}}function W(t,e,n){var r=t._id;return t.each((function(){var t=T(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)})),function(t){return O(t,r).value[e]}}var J=function(t,e,n){t.prototype=e.prototype=n,n.constructor=t};function V(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Z(){}var G=.7,Q=1/G,K="\\s*([+-]?\\d+)\\s*",tt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",et="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",nt=/^#([0-9a-f]{3,8})$/,rt=new RegExp(`^rgb\\(${K},${K},${K}\\)$`),it=new RegExp(`^rgb\\(${et},${et},${et}\\)$`),ot=new RegExp(`^rgba\\(${K},${K},${K},${tt}\\)$`),at=new RegExp(`^rgba\\(${et},${et},${et},${tt}\\)$`),st=new RegExp(`^hsl\\(${tt},${et},${et}\\)$`),lt=new RegExp(`^hsla\\(${tt},${et},${et},${tt}\\)$`),ut={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ct(){return this.rgb().formatHex()}function ht(){return this.rgb().formatRgb()}function dt(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=nt.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?ft(e):3===n?new yt(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?pt(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?pt(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=rt.exec(t))?new yt(e[1],e[2],e[3],1):(e=it.exec(t))?new yt(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=ot.exec(t))?pt(e[1],e[2],e[3],e[4]):(e=at.exec(t))?pt(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=st.exec(t))?Ct(e[1],e[2]/100,e[3]/100,1):(e=lt.exec(t))?Ct(e[1],e[2]/100,e[3]/100,e[4]):ut.hasOwnProperty(t)?ft(ut[t]):"transparent"===t?new yt(NaN,NaN,NaN,0):null}function ft(t){return new yt(t>>16&255,t>>8&255,255&t,1)}function pt(t,e,n,r){return r<=0&&(t=e=n=NaN),new yt(t,e,n,r)}function gt(t){return t instanceof Z||(t=dt(t)),t?new yt((t=t.rgb()).r,t.g,t.b,t.opacity):new yt}function mt(t,e,n,r){return 1===arguments.length?gt(t):new yt(t,e,n,null==r?1:r)}function yt(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function vt(){return`#${bt(this.r)}${bt(this.g)}${bt(this.b)}`}function xt(){const t=wt(this.opacity);return`${1===t?"rgb(":"rgba("}${_t(this.r)}, ${_t(this.g)}, ${_t(this.b)}${1===t?")":`, ${t})`}`}function wt(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function _t(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function bt(t){return((t=_t(t))<16?"0":"")+t.toString(16)}function Ct(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new Et(t,e,n,r)}function kt(t){if(t instanceof Et)return new Et(t.h,t.s,t.l,t.opacity);if(t instanceof Z||(t=dt(t)),!t)return new Et;if(t instanceof Et)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,s=o-i,l=(o+i)/2;return s?(a=e===o?(n-r)/s+6*(n<r):n===o?(r-e)/s+2:(e-n)/s+4,s/=l<.5?o+i:2-o-i,a*=60):s=l>0&&l<1?0:a,new Et(a,s,l,t.opacity)}function Et(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function Mt(t){return(t=(t||0)%360)<0?t+360:t}function $t(t){return Math.max(0,Math.min(1,t||0))}function St(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}function Bt(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}J(Z,dt,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ct,formatHex:ct,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return kt(this).formatHsl()},formatRgb:ht,toString:ht}),J(yt,mt,V(Z,{brighter(t){return t=null==t?Q:Math.pow(Q,t),new yt(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?G:Math.pow(G,t),new yt(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new yt(_t(this.r),_t(this.g),_t(this.b),wt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:vt,formatHex:vt,formatHex8:function(){return`#${bt(this.r)}${bt(this.g)}${bt(this.b)}${bt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:xt,toString:xt})),J(Et,(function(t,e,n,r){return 1===arguments.length?kt(t):new Et(t,e,n,null==r?1:r)}),V(Z,{brighter(t){return t=null==t?Q:Math.pow(Q,t),new Et(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?G:Math.pow(G,t),new Et(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new yt(St(t>=240?t-240:t+120,i,r),St(t,i,r),St(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new Et(Mt(this.h),$t(this.s),$t(this.l),wt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=wt(this.opacity);return`${1===t?"hsl(":"hsla("}${Mt(this.h)}, ${100*$t(this.s)}%, ${100*$t(this.l)}%${1===t?")":`, ${t})`}`}}));var Nt=t=>()=>t;function zt(t,e){var n=e-t;return n?function(t,e){return function(n){return t+n*e}}(t,n):Nt(isNaN(t)?e:t)}var At=function t(e){var n=function(t){return 1==(t=+t)?zt:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):Nt(isNaN(e)?n:e)}}(e);function r(t,e){var r=n((t=mt(t)).r,(e=mt(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=zt(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return r.gamma=t,r}(1);function Tt(t){return function(e){var n,r,i=e.length,o=new Array(i),a=new Array(i),s=new Array(i);for(n=0;n<i;++n)r=mt(e[n]),o[n]=r.r||0,a[n]=r.g||0,s[n]=r.b||0;return o=t(o),a=t(a),s=t(s),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=s(t),r+""}}}Tt((function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1];return Bt((n-r/e)*e,r>0?t[r-1]:2*i-o,i,o,r<e-1?t[r+2]:2*o-i)}})),Tt((function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e);return Bt((n-r/e)*e,t[(r+e-1)%e],t[r%e],t[(r+1)%e],t[(r+2)%e])}}));var Ot=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Xt=new RegExp(Ot.source,"g"),Rt=function(t,e){var n,r,i,o=Ot.lastIndex=Xt.lastIndex=0,a=-1,s=[],l=[];for(t+="",e+="";(n=Ot.exec(t))&&(r=Xt.exec(e));)(i=r.index)>o&&(i=e.slice(o,i),s[a]?s[a]+=i:s[++a]=i),(n=n[0])===(r=r[0])?s[a]?s[a]+=r:s[++a]=r:(s[++a]=null,l.push({i:a,x:L(n,r)})),o=Xt.lastIndex;return o<e.length&&(i=e.slice(o),s[a]?s[a]+=i:s[++a]=i),s.length<2?l[0]?function(t){return function(e){return t(e)+""}}(l[0].x):function(t){return function(){return t}}(e):(e=l.length,function(t){for(var n,r=0;r<e;++r)s[(n=l[r]).i]=n.x(t);return s.join("")})},Lt=function(t,e){var n;return("number"==typeof e?L:e instanceof dt?At:(n=dt(e))?(e=n,At):Rt)(t,e)};function jt(t){return function(){this.removeAttribute(t)}}function Ht(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Yt(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}}function Pt(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}function Dt(t,e,n){var r,i,o;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(t))===(s=l+"")?null:a===r&&s===i?o:(i=s,o=e(r=a,l));this.removeAttribute(t)}}function It(t,e,n){var r,i,o;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(t.space,t.local))===(s=l+"")?null:a===r&&s===i?o:(i=s,o=e(r=a,l));this.removeAttributeNS(t.space,t.local)}}function Ft(t,e){return function(n){this.setAttribute(t,e.call(this,n))}}function qt(t,e){return function(n){this.setAttributeNS(t.space,t.local,e.call(this,n))}}function Ut(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&qt(t,i)),n}return i._value=e,i}function Wt(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&Ft(t,i)),n}return i._value=e,i}function Jt(t,e){return function(){A(this,t).delay=+e.apply(this,arguments)}}function Vt(t,e){return e=+e,function(){A(this,t).delay=e}}function Zt(t,e){return function(){T(this,t).duration=+e.apply(this,arguments)}}function Gt(t,e){return e=+e,function(){T(this,t).duration=e}}function Qt(t,e){if("function"!=typeof e)throw new Error;return function(){T(this,t).ease=e}}var Kt=n("aRnI");function te(t,e,n){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?A:T;return function(){var a=o(this,t),s=a.on;s!==r&&(i=(r=s).copy()).on(e,n),a.on=i}}var ee=n("ZqYF"),ne=n("68h0"),re=r.b.prototype.constructor,ie=n("AKqJ");function oe(t){return function(){this.style.removeProperty(t)}}function ae(t,e,n){return function(r){this.style.setProperty(t,e.call(this,r),n)}}function se(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&ae(t,o,n)),r}return o._value=e,o}function le(t){return function(e){this.textContent=t.call(this,e)}}function ue(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&le(r)),e}return r._value=t,r}var ce=0;function he(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}function de(){return++ce}var fe=r.b.prototype;he.prototype=(function(t){return Object(r.b)().transition(t)}).prototype={constructor:he,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=Object(ee.a)(t));for(var r=this._groups,i=r.length,o=new Array(i),a=0;a<i;++a)for(var s,l,u=r[a],c=u.length,h=o[a]=new Array(c),d=0;d<c;++d)(s=u[d])&&(l=t.call(s,s.__data__,d,u))&&("__data__"in s&&(l.__data__=s.__data__),h[d]=l,z(h[d],e,n,d,h,O(s,n)));return new he(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=Object(ne.a)(t));for(var r=this._groups,i=r.length,o=[],a=[],s=0;s<i;++s)for(var l,u=r[s],c=u.length,h=0;h<c;++h)if(l=u[h]){for(var d,f=t.call(l,l.__data__,h,u),p=O(l,n),g=0,m=f.length;g<m;++g)(d=f[g])&&z(d,e,n,g,f,p);o.push(f),a.push(l)}return new he(o,a,e,n)},selectChild:fe.selectChild,selectChildren:fe.selectChildren,filter:function(t){"function"!=typeof t&&(t=Object(Kt.b)(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var o,a=e[i],s=a.length,l=r[i]=[],u=0;u<s;++u)(o=a[u])&&t.call(o,o.__data__,u,a)&&l.push(o);return new he(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,n=t._groups,r=e.length,i=Math.min(r,n.length),o=new Array(r),a=0;a<i;++a)for(var s,l=e[a],u=n[a],c=l.length,h=o[a]=new Array(c),d=0;d<c;++d)(s=l[d]||u[d])&&(h[d]=s);for(;a<r;++a)o[a]=e[a];return new he(o,this._parents,this._name,this._id)},selection:function(){return new re(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=de(),r=this._groups,i=r.length,o=0;o<i;++o)for(var a,s=r[o],l=s.length,u=0;u<l;++u)if(a=s[u]){var c=O(a,e);z(a,t,n,u,s,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new he(r,this._parents,t,n)},call:fe.call,nodes:fe.nodes,node:fe.node,size:fe.size,empty:fe.empty,each:fe.each,on:function(t,e){var n=this._id;return arguments.length<2?O(this.node(),n).on.on(t):this.each(te(n,t,e))},attr:function(t,e){var n=Object(F.a)(t),r="transform"===n?I:Lt;return this.attrTween(t,"function"==typeof e?(n.local?It:Dt)(n,r,W(this,"attr."+t,e)):null==e?(n.local?Ht:jt)(n):(n.local?Pt:Yt)(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw new Error;var r=Object(F.a)(t);return this.tween(n,(r.local?Ut:Wt)(r,e))},style:function(t,e,n){var r="transform"==(t+="")?D:Lt;return null==e?this.styleTween(t,function(t,e){var n,r,i;return function(){var o=Object(ie.b)(this,t),a=(this.style.removeProperty(t),Object(ie.b)(this,t));return o===a?null:o===n&&a===r?i:i=e(n=o,r=a)}}(t,r)).on("end.style."+t,oe(t)):"function"==typeof e?this.styleTween(t,function(t,e,n){var r,i,o;return function(){var a=Object(ie.b)(this,t),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(t),l=s=Object(ie.b)(this,t)),a===l?null:a===r&&l===i?o:(i=l,o=e(r=a,s))}}(t,r,W(this,"style."+t,e))).each(function(t,e){var n,r,i,o,a="style."+e,s="end."+a;return function(){var l=T(this,t),u=l.on,c=null==l.value[a]?o||(o=oe(e)):void 0;u===n&&i===c||(r=(n=u).copy()).on(s,i=c),l.on=r}}(this._id,t)):this.styleTween(t,function(t,e,n){var r,i,o=n+"";return function(){var a=Object(ie.b)(this,t);return a===o?null:a===r?i:i=e(r=a,n)}}(t,r,e),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw new Error;return this.tween(r,se(t,e,null==n?"":n))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(W(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw new Error;return this.tween(e,ue(t))},remove:function(){return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}));var t},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=O(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?q:U)(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?Jt:Vt)(e,t)):O(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?Zt:Gt)(e,t)):O(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(Qt(e,t)):O(this.node(),e).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,e){return function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw new Error;T(this,t).ease=n}}(this._id,t))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise((function(o,a){var s={value:a},l={value:function(){0==--i&&o()}};n.each((function(){var n=T(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(s),e._.interrupt.push(s),e._.end.push(l)),n.on=e})),0===i&&o()}))},[Symbol.iterator]:fe[Symbol.iterator]};var pe={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function ge(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return n}r.b.prototype.interrupt=function(t){return this.each((function(){R(this,t)}))},r.b.prototype.transition=function(t){var e,n;t instanceof he?(e=t._id,t=t._name):(e=de(),(n=pe).time=_(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,s=r[o],l=s.length,u=0;u<l;++u)(a=s[u])&&z(a,t,e,u,s,n||ge(a,e));return new he(r,this._parents,t,e)}},wakA:function(t,e,n){"use strict";e.a=function(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}},xo8x:function(t,e,n){"use strict";var r={value:()=>{}};function i(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,e){return t.trim().split(/^|\s+/).map((function(t){var n="",r=t.indexOf(".");if(r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),t&&!e.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}}))}function s(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}function l(t,e,n){for(var i=0,o=t.length;i<o;++i)if(t[i].name===e){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}o.prototype=i.prototype={constructor:o,on:function(t,e){var n,r=this._,i=a(t+"",r),o=-1,u=i.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++o<u;)if(n=(t=i[o]).type)r[n]=l(r[n],t.name,e);else if(null==e)for(n in r)r[n]=l(r[n],t.name,null);return this}for(;++o<u;)if((n=(t=i[o]).type)&&(n=s(r[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new o(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=new Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,n=(r=this._[t]).length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}},e.a=i},yUfG:function(t,e,n){"use strict";n.d(e,"a",(function(){return r.c})),n.d(e,"b",(function(){return w}));var r=n("a8tM"),i=n("IeU1");function o(){return 0}var a=function(t){return function(){return t}};const s=4294967296;function l(t,e){var n,r;if(h(e,t))return[e];for(n=0;n<t.length;++n)if(u(e,t[n])&&h(f(t[n],e),t))return[t[n],e];for(n=0;n<t.length-1;++n)for(r=n+1;r<t.length;++r)if(u(f(t[n],t[r]),e)&&u(f(t[n],e),t[r])&&u(f(t[r],e),t[n])&&h(p(t[n],t[r],e),t))return[t[n],t[r],e];throw new Error}function u(t,e){var n=t.r-e.r,r=e.x-t.x,i=e.y-t.y;return n<0||n*n<r*r+i*i}function c(t,e){var n=t.r-e.r+1e-9*Math.max(t.r,e.r,1),r=e.x-t.x,i=e.y-t.y;return n>0&&n*n>r*r+i*i}function h(t,e){for(var n=0;n<e.length;++n)if(!c(t,e[n]))return!1;return!0}function d(t){switch(t.length){case 1:return{x:(e=t[0]).x,y:e.y,r:e.r};case 2:return f(t[0],t[1]);case 3:return p(t[0],t[1],t[2])}var e}function f(t,e){var n=t.x,r=t.y,i=t.r,o=e.x,a=e.y,s=e.r,l=o-n,u=a-r,c=s-i,h=Math.sqrt(l*l+u*u);return{x:(n+o+l/h*c)/2,y:(r+a+u/h*c)/2,r:(h+i+s)/2}}function p(t,e,n){var r=t.x,i=t.y,o=t.r,a=e.x,s=e.y,l=e.r,u=n.x,c=n.y,h=n.r,d=r-a,f=r-u,p=i-s,g=i-c,m=l-o,y=h-o,v=r*r+i*i-o*o,x=v-a*a-s*s+l*l,w=v-u*u-c*c+h*h,_=f*p-d*g,b=(p*w-g*x)/(2*_)-r,C=(g*m-p*y)/_,k=(f*x-d*w)/(2*_)-i,E=(d*y-f*m)/_,M=C*C+E*E-1,$=2*(o+b*C+k*E),S=b*b+k*k-o*o,B=-(Math.abs(M)>1e-6?($+Math.sqrt($*$-4*M*S))/(2*M):S/$);return{x:r+b+C*B,y:i+k+E*B,r:B}}function g(t,e,n){var r,i,o,a,s=t.x-e.x,l=t.y-e.y,u=s*s+l*l;u?(i=e.r+n.r,a=t.r+n.r,(i*=i)>(a*=a)?(r=(u+a-i)/(2*u),o=Math.sqrt(Math.max(0,a/u-r*r)),n.x=t.x-r*s-o*l,n.y=t.y-r*l+o*s):(r=(u+i-a)/(2*u),o=Math.sqrt(Math.max(0,i/u-r*r)),n.x=e.x+r*s-o*l,n.y=e.y+r*l+o*s)):(n.x=e.x+n.r,n.y=e.y)}function m(t,e){var n=t.r+e.r-1e-6,r=e.x-t.x,i=e.y-t.y;return n>0&&n*n>r*r+i*i}function y(t){var e=t._,n=t.next._,r=e.r+n.r,i=(e.x*n.r+n.x*e.r)/r,o=(e.y*n.r+n.y*e.r)/r;return i*i+o*o}function v(t){this._=t,this.next=null,this.previous=null}function x(t){return Math.sqrt(t.value)}var w=function(){var t=null,e=1,n=1,r=o;function l(i){const a=function(){let t=1;return()=>(t=(1664525*t+1013904223)%s)/s}();return i.x=e/2,i.y=n/2,t?i.eachBefore(_(t)).eachAfter(b(r,.5,a)).eachBefore(C(1)):i.eachBefore(_(x)).eachAfter(b(o,1,a)).eachAfter(b(r,i.r/Math.min(e,n),a)).eachBefore(C(Math.min(e,n)/(2*i.r))),i}return l.radius=function(e){return arguments.length?(t=Object(i.a)(e),l):t},l.size=function(t){return arguments.length?(e=+t[0],n=+t[1],l):[e,n]},l.padding=function(t){return arguments.length?(r="function"==typeof t?t:a(+t),l):r},l};function _(t){return function(e){e.children||(e.r=Math.max(0,+t(e)||0))}}function b(t,e,n){return function(r){if(i=r.children){var i,o,a,s=i.length,u=t(r)*e||0;if(u)for(o=0;o<s;++o)i[o].r+=u;if(a=function(t,e){if(!(a=(n=t,t="object"==typeof n&&"length"in n?n:Array.from(n)).length))return 0;var n,r,i,o,a,s,u,h,f,p,x,w;if((r=t[0]).x=0,r.y=0,!(a>1))return r.r;if(r.x=-(i=t[1]).r,i.x=r.r,i.y=0,!(a>2))return r.r+i.r;g(i,r,o=t[2]),r=new v(r),i=new v(i),o=new v(o),r.next=o.previous=i,i.next=r.previous=o,o.next=i.previous=r;t:for(h=3;h<a;++h){g(r._,i._,o=t[h]),o=new v(o),f=i.next,p=r.previous,x=i._.r,w=r._.r;do{if(x<=w){if(m(f._,o._)){r.next=i=f,i.previous=r,--h;continue t}x+=f._.r,f=f.next}else{if(m(p._,o._)){(r=p).next=i,i.previous=r,--h;continue t}w+=p._.r,p=p.previous}}while(f!==p.next);for(o.previous=r,o.next=i,r.next=i.previous=i=o,s=y(r);(o=o.next)!==i;)(u=y(o))<s&&(r=o,s=u);i=r.next}for(r=[i._],o=i;(o=o.next)!==i;)r.push(o._);for(o=function(t,e){for(var n,r,i=0,o=(t=function(t,e){let n,r,i=t.length;for(;i;)r=e()*i--|0,n=t[i],t[i]=t[r],t[r]=n;return t}(Array.from(t),e)).length,a=[];i<o;)n=t[i],r&&c(r,n)?++i:(r=d(a=l(a,n)),i=0);return r}(r,e),h=0;h<a;++h)(r=t[h]).x-=o.x,r.y-=o.y;return o.r}(i,n),u)for(o=0;o<s;++o)i[o].r-=u;r.r=a+u}}}function C(t){return function(e){var n=e.parent;e.r*=t,n&&(e.x=n.x+t*e.x,e.y=n.y+t*e.y)}}}}]);