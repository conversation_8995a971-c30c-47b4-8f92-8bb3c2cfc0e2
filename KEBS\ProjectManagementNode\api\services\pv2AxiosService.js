const logger = require("../../logger").logger;
const axios = require("axios");
const rpool = require("../../databaseCon").rpool;

const utilityService = require("../utils/pv2UtilityService");




/**
 * 
 * @param {*} db 
 * @param {*} item_id 
 * @param {*} associate_id 
 * @description Update Allocated Hours For Employees
 * <AUTHOR>
 * @returns 
 */
module.exports.updateAllocatedHoursForEmployees = async(db, item_id, associate_id, authorization)=>{
    try{
        logger.info("AXIOSSSS"+item_id+" - "+associate_id+" - "+authorization)
        await axios.post(`http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/project/updateAllocatedHoursForEmployee`, {
            item_id: item_id,
            associate_oid: associate_id,
            db: db
        },
        {
            headers: 
            {
                Authorization: authorization   
            }
        });

        return Promise.resolve({messType:"S", message:"Updated successful"})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Allocated Hours For Employees!"})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} item_id 
 * @param {*} associate_id 
 * @description insertWorkflow
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.insertWorkflow = async(approve_prams,workflow_id,db, user,authorization)=>{
    try{
     let workflow_response =   await axios.post
        (
            `http://${ process.env.WORKFLOW_NODE_PORT || "localhost:3012"}/api/wfPrimary/createWorkflowItems`,
            {
                workflowId:workflow_id || 0,
                initiatorOId: user.oid,
                approvers: approve_prams,
                submission: "",
                comments: "",
                isAggregationAllowed: 1,
                originalInitiatorOId: user.oid,
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
           
            logger.info(workflow_response['data'])
            return Promise.resolve( {messType:'S' ,data:workflow_response['data']} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while insertWorkflow!"})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description insertWorkflow
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.rejectRequests = async(project_id,db, authorization)=>{
    try{
        logger.info('test ih')
       logger.info(project_id)
     let workflow_response=   await axios.post
        (
            `http://localhost:${process.env.PORT || 3030}/api/rmg/requests/rejectProjectRequests`,
            {
                project_id:project_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
           
            logger.info(workflow_response)
            return Promise.resolve( {messType:'S' ,data:workflow_response} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while insertWorkflow!"})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} wf_payload 
 * @description updateWorkflow
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.updateWorkflow = async(wf_payload,user,db, authorization)=>{
    try{
        logger.info('test ih')
       logger.info(wf_payload)

       let check_workflow_compelte = await rpool(`select id from ${db}.t_workflow_header where is_workflow_complete=1 and is_active=1 and id=?`, [wf_payload?.workflowHeaderId])

       if (check_workflow_compelte && check_workflow_compelte.length == 0) {
         let workflow_details = await rpool(`select mw.single_appr_wf from ${db}.m_workflow mw 
         left join ${db}.t_workflow_header twh on twh.workflow_id=mw.id
         where twh.id=?`, [wf_payload?.workflowHeaderId])
         let single_appr_wf = 0
         if (workflow_details && workflow_details.length > 0) {
            wf_payload.single_appr_wf = workflow_details[0]['single_appr_wf']
         }
       }else{
        return Promise.resolve({messType:'E', messText:"Workflow Deatils Not Found"})
       }
   

       let wf_response = await axios.post(
        `http://${
          process.env.WORKFLOW_NODE_PORT || "localhost:3012"
        }/api/wfPrimary/updateWorkflowItems`,
        wf_payload,
        {
          headers: { Authorization: authorization },
        }
      );
    return Promise.resolve(wf_response)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updateWorkflow!"})
    }
}


/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description Update Billing Advice RR Projection
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updateBillingAdviceRRProjection = async(milestone_id, db, user, authorization)=>{
    try{
        logger.info("Billing Advice Axios Function : updateBillingAdviceRRProjection")
        
        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/billingAdviceRevenueUBRPosting`,
            {
                milestoneId: milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        ).then(function (response) {   
            return response
        })
        .catch(function (error) {
            if (error.response) {
                return error.response.data
            }
        });
 
      

        let data = {
            "milestone_id": milestone_id,
            "output": result['data'],
            "updated_on": moment().format(),
            "user": user
        }

        utilityService.externalAPICall(data, db)
           

        return Promise.resolve( {messType:'S'} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Billing Advice RR Projections!"})
    }
}


/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description Update Billing Advice RR Projection
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updateBillingAdviceRRProjection = async(milestone_id, db, user, authorization)=>{
    try{

        await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/billingAdviceRevenueUBRPosting`,
            {
                milestoneId: milestone_id,
                db_name: db
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
           

        return Promise.resolve( {messType:'S'} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Billing Advice RR Projections!"})
    }
}


/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description Update Projection on Quote Allocation
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updateProjectionOnQuoteAllocation = async(quote_id, project_id, item_id, db, user, authorization)=>{
    try{

        await axios.post(
            `http://${process.env.MIS_NODE_PORT || 'localhost:3039'}/api/misFunctions/updateProjectionOnQuoteAllocation`,
            {
                quote_id: quote_id,
                project_id: item_id,
                db_name: db
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
           

        return Promise.resolve( {messType:'S'} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updateProjectionOnQuoteAllocation!"})
    }
}


/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description updateProjectionOnPeopleAllocation
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.updateProjectionOnPeopleAllocation = async(isa_id, rate_card_id, position_type, project_id, item_id, db, user, authorization)=>{
    try{

        let value = {
            isa_id: isa_id, 
            position_id: rate_card_id, 
            position_type:position_type, 
            project_id: item_id
        }

        logger.info("VALUE")

        logger.info(value)

        logger.info("CALL THE APIIIII!!!!")

        await axios.post(
            `http://${process.env.MIS_NODE_PORT || 'localhost:3039'}/api/misFunctions/updateProjectionOnPeopleAllocation`,
            {
                isa_id: isa_id, 
                position_id: rate_card_id, 
                position_type:position_type, 
                project_id: item_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
           

        return Promise.resolve( {messType:'S'} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating updateProjectionOnPeopleAllocation!"})
    }
}

/**
 * 
 * @param {*} quote_id 
 * @param {*} planned_start_date 
 * @param {*} planned_end_date 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Month Wise Position Effort
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMonthWisePositionEffort = async(quote_id, planned_start_date, planned_end_date, db, user, authorization)=>{
    try{
        let result = await axios.post(`http://${process.env.QB_NODE_PORT || 'localhost:3033'}/api/qb/quote/getMonthWisePositionEffort`,
            {
                quote_id: quote_id,
                start_date: planned_start_date,
                end_date: planned_end_date
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )

        return Promise.resolve({messType:"S", data: result['data']['data']})
    }
    catch(err){
        logger.info(err);
        return Promise.resolve({messType:"E", error: err, message:"Error while getting Month Wise Position Effort"})
    }
}

/**
 * @param {*} db 
 * @param {*} milestone_ids 
 * @description get accrual reversal date for the milestones
 * <AUTHOR> Arasu
 * @returns 
 */

module.exports.getAccrualReversalDateDetails = async(milestoneIds, db, user, authorization) =>{
    try{
        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/getUBRReversalDateForMilestones`,
            {
                milestoneIds: milestoneIds
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while fetching accrual reversal date!"})
    }
}

/**
 * @param {*} db 
 * @param {*} item_id 
 * @param {*} associate_id 
 * @description Update UBR Reversal Posting Date when end date changes
 * <AUTHOR> S
 * @returns 
 */
module.exports.updateUBRReversalPostingDate = async(db, id, data, authorization)=>{
    try{

        await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/PostBillingRevenueUBRPosting`,
            {
                milestoneId: id,
                startDate: data.startDate ? data.startDate: null,
                endDate: data.endDate ? data.endDate : null
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while fetching accrual reversal date!"})
    }
}

/**
 * @param {*} db 
 * @param {*} item_id 
 * @description get project financial details from finance
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectFinacialdetailsFromFinance = async(db, project_item_id, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/v2/getFinancialDetailsForProject`,
            {
                project_item_id: project_item_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while fetching project fincial details from finance"})
    }
}




/**
 * @param {*} db 
 * @param {*} milestone_id 
 * @description get project financial details from finance
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.updateUBROnMilestoneCancellation = async(db, milestone_id, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/updateUBROnMilestoneCancellation`,
            {
                milestoneId: milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}

/**
 * 
 * @param {*} db 
 * @param {*} project_item_id 
 * @param {*} authorization 
 * @description Get Revenue Billed Value for the Project Quote Level
 * @returns 
 */
module.exports.getProjectQuoteFinancialDetailsFromFinance = async(db, project_item_id, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/v2/getRevenueBilledForProjectQuote `,
            {
                project_item_id: project_item_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )

        console.log(result)
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}

// /**
//  * 
//  * @param {*} isa_id 
//  * @param {*} db 
//  * @param {*} user 
//  * @param {*} authorization 
//  * @description Insert Time Trakcer Allocated Hours
//  * <AUTHOR> Raam Baskar
//  * @returns 
//  */
// module.exports.insertTimeTrackerAllocatedHours = async(isa_id, db, user, authorization)=>{
//     try{

//         let tenant = await utilityService.getDomainName(db);


//         if(tenant['messType']=="S")
//         {
//             tenantDomain = tenant["data"]

//             let result = await axios.post(
//                 `http://${tenantDomain}/api/bgjPrimary/buildEmployeePlannedHours `,
//                 {
//                     project_item_id: project_item_id
//                 },
//                 {
//                     headers: 
//                     {
//                         Authorization: authorization   
//                     }
//                 }
//             )
//         }

//         return Promise.resolve(result)
//     }
//     catch(err){
//         logger.info(err)
//         return Promise.resolve({messType:"E", error: err, message:"Error while inserting Time Tracker Allocated HOurs"})
//     }
// }



/**
 * 
 * @param {*} db 
 * @param {*} project_item_id 
 * @param {*} authorization 
 * @description Get Revenue Billed Value for the Project Quote Level
 * @returns 
 */
module.exports.revenueReversal = async(milestone_id, db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/revenueReversal`,
            {
                milestoneId: milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}


/**
 * 
 * @param {*} db 
 * @param {*} project_item_id 
 * @param {*} authorization 
 * @description Partial Billing Revenue Posting in Invoice
 * @returns 
 */
module.exports.partialBillingRevenuePosting = async(milestone_id, parent_milestone_id, db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/partialInvoiceRevenuePosting`,
            {
                milestoneId: milestone_id,
                parentMilestoneId: parent_milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}


/**
 * 
 * @param {*} milestone_id 
 * @param {*} parent_milestone_id 
 * @param {*} db 
 * @param {*} authorization 
 * @description Credit Note Revenue UBR posting
 * @returns 
 */
module.exports.billingAdviceRevenueUBRPosting = async(milestone_id, parent_milestone_id, db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || 'localhost:3001'}/api/invoice/billingAdviceRevenueUBRPosting`,
            {
                milestoneId: milestone_id,
                parentMilestoneId: parent_milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}

/**
 * 
 * @param {*} unique_id 
 * @param {*} table_name 
 * @param {*} comments_data 
 * @param {*} db 
 * @param {*} authorization 
 * @description Insert comments
 * @returns 
 */
module.exports.insertComments = async(unique_id, table_name, comments_data,db, authorization)=>{
    try{

        // format for comments data
        // let commentData = {
        //     context: {},
        //     sequence_number: sequence_number + 1,
        //     time: new Date(),
        //     commentor_oid: oid,
        //     commentor_name: name,
        //     comment: comment ? comment : "",
        //     url: ""
        // }

        let result = await axios.post(
            `http://${process.env.PROJECT_NODE_PORT || "localhost:3001"
            }/api/general/writeComment`,
            {
              application_id: 915,
              unique_id_1: unique_id,
              table_name: table_name,
              comment: comments_data
            },
            {
              headers: { Authorization: authorization }
            }
          )
        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating ubr on milestone cancellation"})
    }
}

/**
 * @description fetching Total Available Hours
 * @param {*} db 
 * @param {*} allocation_data 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getTotalAllocatedHours = async(db,allocation_data, authorization)=>{
    try{

        let data = await axios.post(
            `http://${process.env.MIS_NODE_PORT || 'localhost:3039'}/api/misFunctions/getPlannedAllocatedHours`,
            allocation_data,
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
         
        logger.info('________________________')
        logger.info(data[''])

        return Promise.resolve( {messType:'S',data:data['data']} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getTotalAllocatedHours!"})
    }
}

/**
 * @description Quote Position Check
 * @param {*} db 
 * @param {*} allocation_data 
 * @param {*} authorization 
 * @returns 
 */
module.exports.quotePositionCheck = async(db,allocation_data, authorization)=>{
    try{
        logger.info(allocation_data)
        let data = await axios.post(
            `http://${process.env.MIS_NODE_PORT || 'localhost:3039'}/api/misFunctions/getPlannedAllocatedAndRemainingBillableHours`,
            allocation_data,
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
         
        logger.info('________________________')
        logger.info(data['data'])

        return Promise.resolve( {messType:'S',data:data['data']} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while getTotalAllocatedHours!"})
    }
}



/**
 * @description Updating Employee Allocation
 * @param {*} params 
 * @param {*} db 
 * @param {*} authorization 
 * @returns 
 */
module.exports.updateEmployeeAllocation = async(params,db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.EMPLOYEE_CAPACITY_NODE_PORT || "localhost:3822"
            }/api/employeeCapacity/reportData/updateEmployeeAllocation`,
            {
              params: params
            },
            {
              headers: { Authorization: authorization }
            }
          )
        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Employee Allocation"})
    }
}

/**
 * @description Updating Employee Allocation on Edit
 * @param {*} params 
 * @param {*} db 
 * @param {*} authorization 
 * @returns 
 */
module.exports.updateEmployeeAllocationonEdit = async(params,db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.EMPLOYEE_CAPACITY_NODE_PORT || "localhost:3822"
            }/api/employeeCapacity/reportData/updateEmployeeAllocationonEdit`,
            {
              params: params
            },
            {
              headers: { Authorization: authorization }
            }
          )
        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Employee Allocation on ISA Edit"})
    }
}

/**
 * @description Updating the Employee on Demobilization
 * @param {*} params 
 * @param {*} db 
 * @param {*} authorization 
 * @returns 
 */
module.exports.updateEmployeeDemobilization = async(params,db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.EMPLOYEE_CAPACITY_NODE_PORT || "localhost:3822"
            }/api/employeeCapacity/reportData/updateEmployeeDemobilization`,
            {
              params: params
            },
            {
              headers: { Authorization: authorization }
            }
          )
        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Allocation on Demobilization"})
    }
}
/**
 * 
 * @param {*} db 
 * @param {*} project_id 
 * @description Update Billing Advice RR Projection
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.insertProjectDetails = async(data,selectedOption,week_array,fields,stakeholders,[],code,intergeration,[], db, user, authorization)=>{
    try{
        
        let result = await axios.post(
            `http://nova.kebs.app/api/pm/planning/saveProjectDetails`,
            {
                code:code,
                data: data,
                external_stakeholders:[],
                fields:fields,
                intergeration:intergeration,
                selectedOption :selectedOption,
                stakeholders: [],
                week_array:week_array           
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        ).then(function (response) {   
            return response
        })
        .catch(function (error) {
            if (error.response) {
                return error.response.data
            }
        });
 
        return Promise.resolve( {messType:'S'} )
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while Inserting Project!"})
    }
}

module.exports.updateInboxPeopleAllocationWorkflowRequest = async(milestone_id, db, authorization)=>{
    try{

        let result = await axios.post(
            `http://${process.env.RMG_NODE_PORT || 'localhost:3030'}/api/rmg/resources/initiateApprovalAction`,
            {
                milestoneId: milestone_id
            },
            {
                headers: 
                {
                    Authorization: authorization   
                }
            }
        )
	

        return Promise.resolve(result)

    }catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while updating Approval action"})
    }
}